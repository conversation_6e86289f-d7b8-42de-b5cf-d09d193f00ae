import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext # Added scrolledtext
from typing import List, Dict, Optional, Any # Any for lasio.LASFile

# Assuming these will be available in the new structure
# For now, we'll assume these imports are correct relative to the xplot_app package
from processing import analyze_log_availability, validate_calculation_inputs
# Note: execute_custom_calculations is NOT called by the dialog itself, but by main.py

class CalculatorDialog(tk.Toplevel):
    """
    Dialog for entering custom log calculations and validating them.
    """

    def __init__(self, parent: tk.Widget, las_files: List[Any]):  # Any for lasio.LASFile
        super().__init__(parent)
        self.title("Custom Log Calculator - Enhanced with Availability Info")
        self.geometry("1000x700")  # Adjusted size

        self.las_files = las_files
        self.calculation_string: Optional[str] = None  # Stores the validated string on OK
        self._log_analysis: Optional[Dict[str, Any]] = None  # Will be loaded on demand
        
        self._build_ui()
        self._populate_initial_logs()  # Show basic UI first

        # Schedule the log analysis to run after the UI is shown
        self.after(100, self._load_log_analysis_async)

        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
        
    def _build_ui(self) -> None:
        """Builds the UI for the calculator dialog."""
        main_paned_window = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Left Frame: Available logs and legend
        left_frame = ttk.Frame(main_paned_window, width=300)
        main_paned_window.add(left_frame, weight=1) # Adjust weight as needed

        ttk.Label(left_frame, text="Available Variables:", font=('Arial', 12, 'bold')).pack(anchor='w', pady=(0,5))
        
        legend_frame = ttk.Frame(left_frame)
        legend_frame.pack(fill=tk.X, pady=2)
        ttk.Label(legend_frame, text=" Safe (all wells)", foreground='green').pack(anchor='w')
        ttk.Label(legend_frame, text=" Partial (some wells - may cause errors)", foreground='orange').pack(anchor='w')

        listbox_frame = ttk.Frame(left_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.logs_listbox = tk.Listbox(listbox_frame, font=('Courier', 10), height=15)
        scrollbar_logs = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.logs_listbox.yview)
        self.logs_listbox.config(yscrollcommand=scrollbar_logs.set)
        self.logs_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_logs.pack(side=tk.RIGHT, fill=tk.Y)
        self._populate_logs_listbox()

        # Right Frame: Instructions and Text Area
        right_frame = ttk.Frame(main_paned_window)
        main_paned_window.add(right_frame, weight=3) # Adjust weight

        instructions = (
            "Enter calculations (e.g., NEW_LOG = GR * 2).\n"
            "Use 'NP.' prefix for numpy functions (e.g., NP.LOG(GR)).\n"
            "Only use  logs for calculations intended to work on all wells."
        )
        ttk.Label(right_frame, text=instructions, justify=tk.LEFT, wraplength=550).pack(anchor='w', pady=(0,10))

        self.calc_text_area = scrolledtext.ScrolledText(right_frame, width=80, height=20, font=('Courier', 11), undo=True)
        self.calc_text_area.pack(fill=tk.BOTH, expand=True)

        # Buttons Frame (below PanedWindow)
        buttons_frame = ttk.Frame(self, padding=(0, 10, 0, 0))
        buttons_frame.pack(fill=tk.X, pady=10) # Added pady
        
        ttk.Button(buttons_frame, text="Check Syntax", command=self._on_check_syntax).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Check Log Availability", command=self._on_check_availability).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Submit & Close", command=self._on_submit).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="Cancel", command=self._on_cancel).pack(side=tk.RIGHT, padx=5)

    def _populate_initial_logs(self) -> None:
        """Populates the listbox with a loading message."""
        self.logs_listbox.delete(0, tk.END)
        self.logs_listbox.insert(tk.END, "Loading log information...")
        self.logs_listbox.itemconfig(0, {'fg': 'blue'})
        self.update_idletasks()
    
    def _populate_logs_listbox(self) -> None:
        """Populates the listbox with common and partial logs."""
        if self._log_analysis is None:
            self._populate_initial_logs()
            return
            
        self.logs_listbox.delete(0, tk.END)
        
        if not self._log_analysis['common_logs'] and not self._log_analysis['partial_logs']:
            self.logs_listbox.insert(tk.END, "No log data available")
            return
            
        if self._log_analysis['common_logs']:
            self.logs_listbox.insert(tk.END, "--- Common Logs (All Wells) ---")
            for log_name in self._log_analysis['common_logs']:
                self.logs_listbox.insert(tk.END, f"  Safe {log_name}")
                self.logs_listbox.itemconfig(tk.END, {'fg': 'green'})
                self.update_idletasks()  # Update UI periodically

        if self._log_analysis['partial_logs']:
            if self._log_analysis['common_logs']:  # Add spacer only if there were common logs
                self.logs_listbox.insert(tk.END, "")
            self.logs_listbox.insert(tk.END, "--- Partial Logs (Some Wells) ---")
            for log_name, info in self._log_analysis['partial_logs'].items():
                self.logs_listbox.insert(tk.END, f"  Partial {log_name} ({info['count']}/{self._log_analysis['total_wells']})")
                self.logs_listbox.itemconfig(tk.END, {'fg': 'orange'})
                if self.logs_listbox.size() % 10 == 0:  # Update UI every 10 items
                    self.update_idletasks()
            
    def _on_check_syntax(self) -> None:
        """Checks the syntax of the entered calculations."""
        if self._log_analysis is None:
            messagebox.showinfo("Please Wait", "Still analyzing log availability. Please wait a moment and try again.", parent=self)
            return
            
        calc_str = self.calc_text_area.get("1.0", tk.END).strip()
        if not calc_str:
            messagebox.showwarning("Syntax Check", "No calculations entered.", parent=self)
            return
        try:
            compile(calc_str, '<string>', 'exec')
            messagebox.showinfo("Syntax Check", "Syntax appears OK.", parent=self)
        except SyntaxError as e:
            messagebox.showerror("Syntax Error", f"Syntax error:\n{e}", parent=self)

    def _load_log_analysis_async(self) -> None:
        """Load log analysis in a background thread and update UI safely from the main thread."""
        # Show loading message
        self.logs_listbox.delete(0, tk.END)
        self.logs_listbox.insert(tk.END, "Analyzing log availability...")
        self.logs_listbox.itemconfig(0, {'fg': 'blue'})
        self.update_idletasks()

        import threading, queue
        from processing import analyze_log_availability

        # Queue to communicate between thread and mainloop
        self._analysis_queue: "queue.Queue[tuple[str, Any]]" = queue.Queue(maxsize=1)

        def analyze() -> None:
            try:
                result = analyze_log_availability(self.las_files)
                self._analysis_queue.put(("success", result))
            except Exception as exc:
                self._analysis_queue.put(("error", str(exc)))

        threading.Thread(target=analyze, daemon=True).start()

        # Start polling for result on the main thread
        self.after(100, self._check_analysis_queue)

    def _check_analysis_queue(self) -> None:
        """Poll the queue for analysis result and update UI when ready."""
        import queue
        try:
            status, payload = self._analysis_queue.get_nowait()
        except queue.Empty:
            # Not ready yet, poll again
            self.after(100, self._check_analysis_queue)
            return

        if status == "success":
            self._log_analysis = payload  # type: ignore[assignment]
            self._populate_logs_listbox()
        else:  # status == "error"
            self._show_error(payload)

    def _show_error(self, message: str) -> None:
        """Show an error message in the logs listbox."""
        self.logs_listbox.delete(0, tk.END)
        self.logs_listbox.insert(tk.END, "Error loading log information:")
        self.logs_listbox.insert(tk.END, message)
        self.logs_listbox.itemconfig(0, {'fg': 'red'})
        self.logs_listbox.itemconfig(1, {'fg': 'red'})
    
    def _on_check_availability(self) -> None:
        """Checks which logs are used in the calculations and their availability."""
        if self._log_analysis is None:
            messagebox.showinfo("Please Wait", "Still analyzing log availability. Please wait a moment and try again.", parent=self)
            return
            
        calc_str = self.calc_text_area.get("1.0", tk.END).strip()
        if not calc_str:
            messagebox.showwarning("Check Availability", "No calculations entered.", parent=self)
            return

        validation_res = validate_calculation_inputs(self.las_files, calc_str)
        
        if validation_res['valid']:
            msg = "All input logs found in all wells."
            if validation_res['identified_output_vars']:
                msg += f"\nOutput variables: {', '.join(validation_res['identified_output_vars'])}"
            messagebox.showinfo("Log Availability", msg, parent=self)
        else:
            error_msg = "Validation Failed!\n"
            if validation_res['error_summary']:
                 error_msg += validation_res['error_summary'] + "\n"
            for well, missing in validation_res['missing_logs_detail'].items():
                error_msg += f"  Well '{well}' missing: {', '.join(missing)}\n"
            if validation_res['recommendation_info']:
                error_msg += "\n" + validation_res['recommendation_info']
            messagebox.showerror("Log Availability Error", error_msg, parent=self)

    def _on_submit(self) -> None:
        """Stores the calculation string and closes the dialog if valid or empty."""
        calc_str = self.calc_text_area.get("1.0", tk.END).strip()
        
        if not calc_str: 
            self.calculation_string = "" 
            self.destroy()
            return

        validation_res = validate_calculation_inputs(self.las_files, calc_str)
        if not validation_res['valid']:
            error_msg = "Cannot submit! Validation Failed:\n"
            if validation_res['error_summary']:
                 error_msg += validation_res['error_summary'] + "\n"
            for well, missing in validation_res['missing_logs_detail'].items():
                error_msg += f"  Well '{well}' missing: {', '.join(missing)}\n"
            if validation_res['recommendation_info']:
                error_msg += "\n" + validation_res['recommendation_info']
            error_msg += "\n\nDo you want to edit the calculations?"
            
            if messagebox.askyesno("Validation Error", error_msg + "\n(Yes to edit, No to close without submitting)", parent=self):
                return 
            else:
                self.calculation_string = None 
                self.destroy()
                return
        
        self.calculation_string = calc_str
        self.destroy()

    def _on_cancel(self) -> None:
        """Handles dialog cancellation."""
        self.calculation_string = None 
        self.destroy()

    def get_calculation_string(self) -> Optional[str]:
        """Returns the calculation string if submitted, None if cancelled or invalid on close."""
        self.wait_window(self)  # Wait for this dialog to close before returning the value
        return self.calculation_string

if __name__ == '__main__':
    from unittest.mock import Mock 
    
    root = tk.Tk()
    root.withdraw() 
    
    class MockLAS: 
        def __init__(self, name, curves_mnem):
            self.well = Mock(); self.well.WELL.value = name
            self.curves = [Mock(mnemonic=m) for m in curves_mnem]
    
    mock_las_files = [
        MockLAS("Well-A", ['DEPTH', 'GR', 'DT']),
        MockLAS("Well-B", ['DEPTH', 'GR', 'NPHI']) 
    ]
    dialog = CalculatorDialog(root, mock_las_files)
    calc_string = dialog.get_calculation_string()
    
    if calc_string is not None:
        print("Calculation String:", calc_string)
    else:
        print("Calculator cancelled or closed with invalid input.")

    if root.winfo_exists():
        root.destroy()
