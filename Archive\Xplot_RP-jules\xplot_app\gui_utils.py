import tkinter as tk
from tkinter import ttk
from typing import Tuple

"""GUI utility functions for the XPlot App."""

def create_scrollable_frame(parent: tk.Widget) -> Tuple[ttk.Frame, tk.Canvas]:
    """
    Creates a scrollable frame within a given parent widget.

    This utility sets up a main frame, a canvas within it, a vertical scrollbar
    linked to the canvas, and a content frame within the canvas where other
    widgets can be placed. The content frame will expand/contract with the
    widgets it contains, and the scrollbar will activate as needed.

    Args:
        parent: The parent widget (e.g., a tk.Toplevel dialog or another ttk.Frame)
                where the scrollable frame should be placed.

    Returns:
        A tuple containing:
            - content_frame (ttk.Frame): The frame within the canvas where widgets
                                         should be added by the caller.
            - canvas (tk.Canvas): The canvas widget, in case the caller needs to
                                  interact with it directly (rarely needed).
    """
    # Container for the canvas and scrollbar
    container = ttk.Frame(parent)
    container.pack(fill=tk.BOTH, expand=True)

    canvas = tk.Canvas(container)
    scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
    
    # This is the frame that will be scrolled
    content_frame = ttk.Frame(canvas)

    # When the content_frame's size changes, update the scrollregion of the canvas
    content_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    # Create a window in the canvas that contains the content_frame
    canvas.create_window((0, 0), window=content_frame, anchor="nw")
    
    # Link the scrollbar to the canvas's yview
    canvas.configure(yscrollcommand=scrollbar.set)

    # Pack canvas and scrollbar into the container frame
    canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    return content_frame, canvas
