# EEI Function Modular

A unified framework for Extended Elastic Impedance (EEI) analysis and general well log cross-plotting.

## Overview

EEI Function Modular consolidates functionality from **EEI_XCorr_Modular** and **EEI_Xplot_Modular** into a single, maintainable codebase. It provides:

- **EEI Analysis**: Extended Elastic Impedance, CPEI, and PEIL calculations
- **Cross-Plotting**: General-purpose well log visualization
- **Data Management**: Unified LAS and Excel file handling
- **Validation**: Comprehensive data validation framework
- **Modular Architecture**: Clean separation of concerns

## Project Status

**Phase 1: Core Foundation - COMPLETED** ✅

- ✅ Directory structure created
- ✅ Configuration modules merged and consolidated
- ✅ Data I/O operations unified
- ✅ Validation framework implemented

## Installation

### Requirements

```
numpy>=1.21.0
pandas>=1.3.0
lasio>=0.29
matplotlib>=3.5.0
scipy>=1.7.0
openpyxl>=3.0.7
```

### Setup

1. Ensure all dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

2. Add the module to your Python path or import directly

## Module Structure

```
EEI_Function_Modular/
├── config/                      # Configuration modules
│   ├── base_config.py          # Common settings & log keywords
│   ├── eei_config.py           # EEI-specific parameters
│   ├── visualization_config.py # Plotting & colormap settings
│   └── ui_config.py            # UI layout & styling
│
├── core/                        # Core functionality
│   ├── data_io.py              # LAS/Excel I/O operations
│   └── data_validation.py      # Validation framework
│
├── calculations/                # Calculation engines (Phase 3)
├── visualization/               # Plotting modules (Phase 4)
├── ui/                         # User interface (Phase 5-6)
├── specialized/                # EEI-specific processing
└── utils/                      # General utilities
```

## Quick Start

### Loading LAS Files

```python
from EEI_Function_Modular import load_las_files

# Load files via dialog
las_files = load_las_files()

# Or specify paths
las_files = load_las_files(['well1.las', 'well2.las'])
```

### Loading Excel Boundaries

```python
from EEI_Function_Modular import load_excel_boundaries

# Load boundaries
boundaries = load_excel_boundaries()
```

### Data Validation

```python
from EEI_Function_Modular import DataValidator, ValidationResult
import numpy as np

# Validate arrays
data = np.array([1, 2, 3, 4, 5])
result = DataValidator.validate_numeric(data, "My Data")

if result:
    print("Data is valid!")
else:
    print(f"Validation failed: {result.message}")
```

### Configuration Access

```python
from EEI_Function_Modular import BaseConfig, EEIConfig

# Get log keywords
keywords = BaseConfig.get_log_keywords()

# Get EEI analysis parameters
eei_params = EEIConfig.get_analysis_params('EEI')
```

## Development Roadmap

### ✅ Phase 1: Core Foundation (Week 2) - COMPLETED
- Configuration modules merged
- Data I/O operations unified
- Validation framework created

### 🔄 Phase 2: Processing Layer (Week 3) - PENDING
- Data processing functions
- Statistical calculations
- EEI-specific processing

### 🔄 Phase 3: Calculation Engines (Week 4) - PENDING
- EEI calculation engine
- Custom calculator
- Derived log formulas

### 🔄 Phase 4: Visualization (Week 5) - PENDING
- Plotting engine
- Statistical plots
- Specialized EEI plots

### 🔄 Phase 5: User Interface (Week 6) - PENDING
- Dialog components
- Calculator interface
- Depth/boundary dialogs

### 🔄 Phase 6: Workflows (Week 7) - PENDING
- XPlot workflow
- EEI workflow
- Workflow manager

### 🔄 Phase 7: Integration & Testing (Week 8) - PENDING
- Integration testing
- Performance optimization
- Documentation finalization

## Features

### Configuration Management
- **Unified Log Keywords**: Comprehensive log detection across both modules
- **EEI Parameters**: CPEI, PEIL, and EEI analysis settings
- **Visualization Settings**: Colormap categories and plot styling
- **UI Configuration**: Window sizes, colors, fonts

### Data I/O
- **LAS File Loading**: Single or multiple files with error handling
- **Excel Integration**: Boundary and depth range loading
- **Well Matching**: Automatic filtering of Excel data for loaded wells
- **Log Detection**: Automatic identification of standard logs

### Validation Framework
- **Array Validation**: Compatibility, finite values, range checking
- **Calculation Validation**: Input/output verification
- **Plot Data Validation**: X/Y/Z data and depth range validation
- **Detailed Results**: ValidationResult objects with messages and details

## Testing

Phase 1 testing checklist:

- [x] Load single LAS file
- [x] Load multiple LAS files
- [x] Find default columns in LAS files
- [x] Load Excel boundaries
- [x] Filter Excel data for wells
- [x] Validate array compatibility
- [x] Validate finite values
- [x] Configuration access methods

## Contributing

This is an internal integration project. For questions or issues, contact the development team.

## License

Internal use only - PT Pertamina (Persero)

## Version History

### Version 1.0.0 (2025)
- Initial release
- Phase 1: Core Foundation completed
- Configuration modules consolidated
- Data I/O unified
- Validation framework implemented

## Contact

For support or questions, please contact the EEI Function Modular development team.
