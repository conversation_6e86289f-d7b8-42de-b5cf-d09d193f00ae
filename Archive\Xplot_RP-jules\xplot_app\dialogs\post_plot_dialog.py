import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Literal


def show_post_plot_options() -> Optional[Literal['restart', 'exit']]:
    """
    Show post-plot options dialog (legacy wrapper for backward compatibility).
    
    Returns:
        'restart' if user wants to start new analysis
        'exit' if user wants to exit
        None if dialog was closed without making a choice
    """
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        
        dialog = PostPlotDialog(root)
        root.wait_window(dialog)
        
        return dialog.get_choice()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to show post-plot options: {e}")
        return 'exit'  # Default to exit on error

class PostPlotDialog(tk.Toplevel):
    """
    Dialog shown after a plot is generated, offering options to restart or exit.
    """

    def __init__(self, parent: tk.Widget) -> None:
        super().__init__(parent)
        self.title("Analysis Complete")
        self.geometry("450x200") # Adjusted size slightly
        self.resizable(False, False)

        self.choice: Optional[str] = None  # Stores 'restart' or 'exit'

        self._build_ui()
        
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self._on_exit) # Default to exit if window closed
        self.focus_set() # Ensure dialog has focus

        # Center the dialog (optional, but good UX)
        self.update_idletasks()
        parent_x = parent.winfo_rootx()
        parent_y = parent.winfo_rooty()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()
        dialog_width = self.winfo_width()
        dialog_height = self.winfo_height()
        
        x = parent_x + (parent_width // 2) - (dialog_width // 2)
        y = parent_y + (parent_height // 2) - (dialog_height // 2)
        self.geometry(f"+{x}+{y}")


    def _build_ui(self) -> None:
        """Builds the UI components for the post-plot options dialog."""
        main_frame = ttk.Frame(self, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        message_label = ttk.Label(
            main_frame,
            text="Crossplot analysis complete!\n\nWhat would you like to do next?",
            font=("Arial", 12),
            justify=tk.CENTER
        )
        message_label.pack(pady=(0, 20))

        # Description (optional, could be removed if buttons are clear enough)
        # description_label = ttk.Label(
        #     main_frame,
        #     text="• Start New Analysis: Load new LAS files and begin fresh analysis\n"
        #          "• Exit Application: Close the application",
        #     font=("Arial", 10),
        #     justify=tk.LEFT
        # )
        # description_label.pack(pady=(0, 15))

        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=10)

        restart_button = ttk.Button(buttons_frame, text="Start New Analysis", command=self._on_restart, width=20)
        restart_button.pack(side=tk.LEFT, padx=(0, 10))

        exit_button = ttk.Button(buttons_frame, text="Exit Application", command=self._on_exit, width=20)
        exit_button.pack(side=tk.LEFT)
        
        # Ensure buttons are visible and dialog has a minimum size
        self.update_idletasks() # Ensure widgets are created for size calculation
        min_width = restart_button.winfo_reqwidth() + exit_button.winfo_reqwidth() + 80 # Estimate
        min_height = message_label.winfo_reqheight() + buttons_frame.winfo_reqheight() + 60
        current_geom = self.geometry().split('+')
        current_size = current_geom[0].split('x')
        new_width = max(min_width, int(current_size[0]))
        new_height = max(min_height, int(current_size[1]))
        if len(current_geom) > 1:
            self.geometry(f"{new_width}x{new_height}+{current_geom[1]}+{current_geom[2]}")
        else: # Fallback if x,y not found
            self.geometry(f"{new_width}x{new_height}")


    def _on_restart(self) -> None:
        """Sets choice to 'restart' and closes the dialog."""
        self.choice = 'restart'
        self.destroy()

    def _on_exit(self) -> None:
        """Sets choice to 'exit' and closes the dialog."""
        self.choice = 'exit'
        self.destroy()

    def get_choice(self) -> Optional[str]:
        """
        Returns the user's choice after the dialog is closed.
        'restart', 'exit', or None if closed unexpectedly before a choice.
        """
        return self.choice

# Example Usage
if __name__ == '__main__':
    # Test the legacy wrapper function
    print("Testing legacy wrapper function:")
    result = show_post_plot_options()
    print(f"Result from legacy wrapper: {result}")
    
    # Original test code
    print("\nTesting direct dialog:")
    root = tk.Tk()
    root.title("Main App (Test)")
    # To test centering, make the root window visible and sized
    root.geometry("800x600+100+100") 
    
    # Simulate opening the dialog from the main app
    def open_post_plot_dialog():
        dialog = PostPlotDialog(root)
        root.wait_window(dialog) # Wait for the dialog to be destroyed
        
        user_choice = dialog.get_choice()
        if user_choice == 'restart':
            print("User chose to RESTART.")
            # Here, the main app would loop back to the beginning.
            # For this test, we'll just print and then allow exit.
            # To truly restart test, you might call open_post_plot_dialog() again after a delay.
        elif user_choice == 'exit':
            print("User chose to EXIT.")
            root.destroy() # Close main app window
        else:
            print("Dialog closed without an explicit choice (e.g., WM_DELETE_WINDOW with no default).")
            root.destroy() 

    # A button to open the dialog
    test_button = ttk.Button(root, text="Show Post Plot Dialog", command=open_post_plot_dialog)
    test_button.pack(pady=50)
    
    root.mainloop()
