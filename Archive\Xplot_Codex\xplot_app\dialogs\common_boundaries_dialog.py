"""Dialog for selecting common boundaries for all wells."""

from __future__ import annotations

from typing import Dict, List, Tuple, Optional
import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class CommonBoundariesDialog(tk.Toplevel):
    """
    Dialog for selecting common boundaries that apply to all wells simultaneously.
    
    This dialog provides the interface shown in the user's image with:
    - Checkbox to use common boundaries for all wells
    - Dropdown selections for common top and bottom boundaries
    - Apply to All Wells button
    - Individual well boundaries table with status indicators
    """

    def __init__(self, parent: tk.Misc, df: pd.DataFrame, las_well_names: List[str]) -> None:
        super().__init__(parent)
        self.title("Select Boundaries for All Wells")
        self.geometry("900x600")
        self.df = df
        self.las_well_names = las_well_names
        self.result: Optional[Dict[str, Tuple[float, float]]] = None
        
        # Filter data for wells that exist in both Excel and LAS files
        self.available_wells = [well for well in las_well_names if well in df['Well'].values]
        
        if not self.available_wells:
            messagebox.showerror(
                "No Matching Wells",
                "No wells from your LAS files were found in the Excel boundary data."
            )
            self.destroy()
            return
            
        self._build_ui()

    def _build_ui(self) -> None:
        """Build the user interface matching the design in the user's image."""
        # Main container
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Instructions
        instructions = ttk.Label(
            main_frame,
            text="Select top and bottom boundaries for all wells. You can use common boundaries for all wells or set individual boundaries.",
            font=("", 10)
        )
        instructions.pack(anchor=tk.W, pady=(0, 15))

        # Common Boundaries Section
        self._create_common_boundaries_section(main_frame)
        
        # Individual Well Boundaries Section
        self._create_individual_boundaries_section(main_frame)
        
        # Button frame
        self._create_button_frame(main_frame)

    def _create_common_boundaries_section(self, parent: ttk.Frame) -> None:
        """Create the common boundaries section."""
        common_frame = ttk.LabelFrame(parent, text="Common Boundaries", padding="10")
        common_frame.pack(fill=tk.X, pady=(0, 15))

        # Get all unique surfaces across all wells
        self.all_surfaces = sorted(self.df['Surface'].unique().tolist())

        # Use common boundaries checkbox
        self.use_common_var = tk.BooleanVar(value=False)
        common_checkbox = ttk.Checkbutton(
            common_frame,
            text="Use common boundaries for all wells",
            variable=self.use_common_var,
            command=self._toggle_common_mode
        )
        common_checkbox.pack(anchor=tk.W, pady=(0, 10))

        # Common boundary selection frame
        select_frame = ttk.Frame(common_frame)
        select_frame.pack(fill=tk.X, pady=(0, 10))

        # Common Top Boundary
        ttk.Label(select_frame, text="Common Top Boundary:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.common_top_var = tk.StringVar()
        if self.all_surfaces:
            self.common_top_var.set(self.all_surfaces[0])
        self.common_top_combo = ttk.Combobox(
            select_frame, 
            textvariable=self.common_top_var, 
            values=self.all_surfaces, 
            state="disabled", 
            width=25
        )
        self.common_top_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # Common Bottom Boundary
        ttk.Label(select_frame, text="Common Bottom Boundary:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.common_bottom_var = tk.StringVar()
        if len(self.all_surfaces) > 1:
            self.common_bottom_var.set(self.all_surfaces[-1])
        elif self.all_surfaces:
            self.common_bottom_var.set(self.all_surfaces[0])
        self.common_bottom_combo = ttk.Combobox(
            select_frame, 
            textvariable=self.common_bottom_var, 
            values=self.all_surfaces, 
            state="disabled", 
            width=25
        )
        self.common_bottom_combo.grid(row=0, column=3, sticky=tk.W)

        # Apply to All Wells button
        self.apply_common_btn = ttk.Button(
            select_frame, 
            text="Apply to All Wells", 
            command=self._apply_common_boundaries,
            state=tk.DISABLED
        )
        self.apply_common_btn.grid(row=1, column=0, columnspan=4, pady=(15, 0))

    def _create_individual_boundaries_section(self, parent: ttk.Frame) -> None:
        """Create the individual well boundaries section."""
        individual_frame = ttk.LabelFrame(parent, text="Individual Well Boundaries", padding="10")
        individual_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Create scrollable frame
        canvas = tk.Canvas(individual_frame)
        scrollbar = ttk.Scrollbar(individual_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Create headers
        self._create_table_headers()
        
        # Create well selection rows
        self._create_well_rows()

    def _create_table_headers(self) -> None:
        """Create the table headers."""
        headers = ["Well", "Top Boundary", "Top Depth", "Bottom Boundary", "Bottom Depth", "Status"]
        for i, header in enumerate(headers):
            ttk.Label(self.scrollable_frame, text=header, font=("", 9, "bold")).grid(
                row=0, column=i, padx=5, pady=5, sticky=tk.W
            )

    def _create_well_rows(self) -> None:
        """Create rows for each well."""
        self.well_selections = {}
        
        for i, well_name in enumerate(self.available_wells, start=1):
            well_data = self.df[self.df['Well'] == well_name].sort_values('MD')
            surfaces = well_data['Surface'].unique().tolist()

            # Well name
            ttk.Label(self.scrollable_frame, text=well_name).grid(row=i, column=0, padx=5, pady=2, sticky=tk.W)

            # Top surface selection
            top_var = tk.StringVar()
            if surfaces:
                top_var.set(surfaces[0])
            top_combo = ttk.Combobox(self.scrollable_frame, textvariable=top_var, values=surfaces, state="readonly", width=20)
            top_combo.grid(row=i, column=1, padx=5, pady=2)

            # Top depth label
            top_depth_label = ttk.Label(self.scrollable_frame, text="", width=12)
            top_depth_label.grid(row=i, column=2, padx=5, pady=2)

            # Bottom surface selection
            bottom_var = tk.StringVar()
            if len(surfaces) > 1:
                bottom_var.set(surfaces[-1])
            elif surfaces:
                bottom_var.set(surfaces[0])
            bottom_combo = ttk.Combobox(self.scrollable_frame, textvariable=bottom_var, values=surfaces, state="readonly", width=20)
            bottom_combo.grid(row=i, column=3, padx=5, pady=2)

            # Bottom depth label
            bottom_depth_label = ttk.Label(self.scrollable_frame, text="", width=12)
            bottom_depth_label.grid(row=i, column=4, padx=5, pady=2)

            # Status label
            status_label = ttk.Label(self.scrollable_frame, text="Manual", foreground="blue", width=12)
            status_label.grid(row=i, column=5, padx=5, pady=2)

            # Store references
            self.well_selections[well_name] = {
                'top_var': top_var,
                'bottom_var': bottom_var,
                'top_label': top_depth_label,
                'bottom_label': bottom_depth_label,
                'status_label': status_label,
                'well_data': well_data,
                'top_combo': top_combo,
                'bottom_combo': bottom_combo
            }

            # Bind update functions
            update_func = self._make_update_function(well_name)
            top_var.trace('w', update_func)
            bottom_var.trace('w', update_func)

            # Initialize depth labels
            update_func()

    def _create_button_frame(self, parent: ttk.Frame) -> None:
        """Create the button frame."""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="OK", command=self._on_ok).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.LEFT)

    def _make_update_function(self, well_name: str):
        """Create an update function for a specific well."""
        def update_depths(*args):
            selection = self.well_selections[well_name]
            top_surface = selection['top_var'].get()
            bottom_surface = selection['bottom_var'].get()
            data = selection['well_data']

            # Update top depth
            top_md = data[data['Surface'] == top_surface]['MD'].values
            if len(top_md) > 0:
                selection['top_label'].config(text=f"{top_md[0]:.2f}")
            else:
                selection['top_label'].config(text="N/A")

            # Update bottom depth
            bottom_md = data[data['Surface'] == bottom_surface]['MD'].values
            if len(bottom_md) > 0:
                selection['bottom_label'].config(text=f"{bottom_md[0]:.2f}")
            else:
                selection['bottom_label'].config(text="N/A")

        return update_depths

    def _toggle_common_mode(self) -> None:
        """Toggle between common and individual boundary modes."""
        is_common = self.use_common_var.get()
        
        # Enable/disable common boundary controls
        state = "readonly" if is_common else "disabled"
        self.common_top_combo.config(state=state)
        self.common_bottom_combo.config(state=state)
        self.apply_common_btn.config(state=tk.NORMAL if is_common else tk.DISABLED)
        
        # Update individual well controls and status
        for selection in self.well_selections.values():
            if is_common:
                selection['status_label'].config(text="Common Mode", foreground="orange")
                # Disable individual controls when in common mode
                selection['top_combo'].config(state="disabled")
                selection['bottom_combo'].config(state="disabled")
            else:
                selection['status_label'].config(text="Manual", foreground="blue")
                # Re-enable individual controls
                selection['top_combo'].config(state="readonly")
                selection['bottom_combo'].config(state="readonly")

    def _apply_common_boundaries(self) -> None:
        """Apply the selected common boundaries to all wells."""
        top_surface = self.common_top_var.get()
        bottom_surface = self.common_bottom_var.get()
        
        if not top_surface or not bottom_surface:
            messagebox.showerror("Selection Required", "Please select both common top and bottom boundaries.")
            return
        
        if top_surface == bottom_surface:
            messagebox.showerror("Invalid Selection", "Top and bottom boundaries must be different.")
            return
        
        # Apply to all wells and validate
        errors = []
        applied_count = 0
        
        for well_name, selection in self.well_selections.items():
            well_data = selection['well_data']
            available_surfaces = well_data['Surface'].unique()
            
            # Check if both surfaces exist for this well
            if top_surface not in available_surfaces:
                errors.append(f"Well '{well_name}': Top surface '{top_surface}' not found")
                continue
            if bottom_surface not in available_surfaces:
                errors.append(f"Well '{well_name}': Bottom surface '{bottom_surface}' not found")
                continue
            
            # Get depths and validate
            top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
            bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values
            
            if len(top_md) == 0 or len(bottom_md) == 0:
                errors.append(f"Well '{well_name}': Could not find depth values for selected surfaces")
                continue
            
            top_depth = float(top_md[0])
            bottom_depth = float(bottom_md[0])
            
            if top_depth >= bottom_depth:
                errors.append(f"Well '{well_name}': Top depth ({top_depth:.2f}) must be less than bottom depth ({bottom_depth:.2f})")
                continue
            
            # Apply the common boundaries
            selection['top_var'].set(top_surface)
            selection['bottom_var'].set(bottom_surface)
            selection['top_label'].config(text=f"{top_depth:.2f}")
            selection['bottom_label'].config(text=f"{bottom_depth:.2f}")
            selection['status_label'].config(text="Common", foreground="green")
            applied_count += 1
        
        # Show results
        if errors:
            error_msg = f"Common boundaries could not be applied to {len(errors)} wells:\n\n" + "\n".join(errors)
            if applied_count > 0:
                error_msg += f"\n\nSuccessfully applied to {applied_count} wells."
            messagebox.showwarning("Partial Application", error_msg)
        else:
            messagebox.showinfo("Success", f"Common boundaries successfully applied to all {applied_count} wells.")

    def _on_ok(self) -> None:
        """Handle OK button click."""
        boundaries = {}
        errors = []
        
        for well_name, selection in self.well_selections.items():
            top_surface = selection['top_var'].get()
            bottom_surface = selection['bottom_var'].get()
            data = selection['well_data']

            if not top_surface or not bottom_surface:
                errors.append(f"Well '{well_name}': Please select both top and bottom surfaces")
                continue

            # Get depths and validate
            try:
                top_md = data[data['Surface'] == top_surface]['MD'].values
                bottom_md = data[data['Surface'] == bottom_surface]['MD'].values

                if len(top_md) == 0 or len(bottom_md) == 0:
                    errors.append(f"Well '{well_name}': Could not find depth values for selected surfaces")
                    continue

                top_depth = float(top_md[0])
                bottom_depth = float(bottom_md[0])

                if top_depth >= bottom_depth:
                    errors.append(f"Well '{well_name}': Top depth ({top_depth:.2f}) must be less than bottom depth ({bottom_depth:.2f})")
                    continue

                boundaries[well_name] = (top_depth, bottom_depth)

            except Exception as e:
                errors.append(f"Well '{well_name}': Error processing depth data - {str(e)}")
                continue

        if errors:
            error_msg = "The following issues were found:\n\n" + "\n".join(errors)
            if boundaries:
                error_msg += f"\n\n{len(boundaries)} wells were processed successfully. Continue with valid wells only?"
                if messagebox.askyesno("Validation Issues", error_msg):
                    self.result = boundaries
                    self.destroy()
                    return
            else:
                messagebox.showerror("Validation Errors", error_msg)
                return

        if not boundaries:
            messagebox.showerror("No Valid Selections", "No valid boundary selections were made.")
            return

        self.result = boundaries
        self.destroy()

    def _on_cancel(self) -> None:
        """Handle Cancel button click."""
        self.result = None
        self.destroy()

    def get_boundaries(self) -> Optional[Dict[str, Tuple[float, float]]]:
        """Return the selected boundaries."""
        return self.result


def select_common_boundaries_for_all_wells(df: pd.DataFrame, las_well_names: List[str]) -> Optional[Dict[str, Tuple[float, float]]]:
    """
    Show the common boundaries dialog and return the selected boundaries.
    
    Args:
        df: DataFrame containing boundary data
        las_well_names: List of well names from LAS files
        
    Returns:
        Dictionary mapping well names to (top_depth, bottom_depth) tuples or None if cancelled
    """
    root = tk.Tk()
    root.withdraw()
    
    dialog = CommonBoundariesDialog(root, df, las_well_names)
    root.wait_window(dialog)
    result = dialog.get_boundaries()
    
    root.destroy()
    return result
