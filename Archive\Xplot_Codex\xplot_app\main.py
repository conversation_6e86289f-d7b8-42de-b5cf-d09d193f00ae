"""Entry point for the refactored Xplot application."""

import data_io
import processing
import config
import tkinter as tk
from tkinter import messagebox
from dialogs.calculator_dialog import CalculatorDialog
from dialogs.column_select_dialog import ColumnSelectionDialog
from dialogs.plot_settings_dialog import PlotSettingsDialog
from plotting import create_plot
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class XplotApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the root window initially
        self.root.protocol("WM_DELETE_WINDOW", self.quit_application)
        self.preloaded_excel_df = None  # Store Excel data for depth ranges

    def quit_application(self):
        """Safely quit the application."""
        if messagebox.askokcancel("Quit", "Are you sure you want to quit?"):
            self.root.quit()
            self.root.destroy()
            return True
        return False

    def run(self):
        """Run the main application loop with enhanced Excel depth processing."""
        try:
            while True:
                # Step 1: Load LAS files
                las_files = data_io.load_multiple_las_files()
                if not las_files:
                    if messagebox.askyesno("No Files", "No LAS files were loaded. Would you like to try again?"):
                        continue
                    break

                logger.info(f"Loaded {len(las_files)} LAS files successfully")

                # Step 2: Optional Excel depth ranges loading (early in workflow)
                try:
                    self.preloaded_excel_df = data_io.load_excel_depth_ranges(las_files)
                    if self.preloaded_excel_df is not None:
                        logger.info(f"Pre-loaded Excel depth data for {self.preloaded_excel_df['Well'].nunique()} wells")
                    else:
                        logger.info("No Excel depth data loaded - will use manual input or load later")
                except Exception as e:
                    logger.error(f"Error loading Excel depth ranges: {str(e)}")
                    messagebox.showwarning("Excel Load Warning",
                        f"Failed to load Excel depth ranges: {str(e)}\n\nYou can still proceed with manual depth input.")
                    self.preloaded_excel_df = None

                # Step 3: Calculator workflow with retry logic
                while True:
                    calc = CalculatorDialog(self.root, las_files)
                    self.root.wait_window(calc)  # Wait for the dialog to close

                    if getattr(calc, 'cancelled', False):
                        if self.quit_application():
                            return
                        break

                    if not calc.result:
                        # No calculations entered, proceed
                        break

                    # Use the enhanced calculation function with validation
                    success = processing.execute_calculations_with_validation(las_files, calc.result)
                    if success:
                        break
                    # If not successful, the loop will continue to show calculator again

                # Step 4: Column selection dialog (now with preloaded Excel data)
                col_dlg = ColumnSelectionDialog(self.root, las_files, self.preloaded_excel_df)
                self.root.wait_window(col_dlg)
                if not col_dlg.result:
                    if self.quit_application():
                        return
                    continue

                # Step 5: Plot settings dialog
                settings_dlg = PlotSettingsDialog(
                    self.root,
                    col_dlg.result.get("z_col"),
                    col_dlg.result.get("class_col"),
                    las_files
                )
                self.root.wait_window(settings_dlg)
                if not settings_dlg.result:
                    if self.quit_application():
                        return
                    continue

                # Step 6: Create the plot
                choice = create_plot(
                    las_files,
                    col_dlg.result["x_col"],
                    col_dlg.result["y_col"],
                    col_dlg.result["class_col"],
                    col_dlg.result["depth_ranges"],
                    settings_dlg.result,
                    col_dlg.result["z_col"],
                )

                if choice != "restart":
                    break
                else:
                    # Reset Excel data for restart
                    self.preloaded_excel_df = None

        except Exception as e:
            logger.error(f"Unexpected error in main application: {str(e)}")
            messagebox.showerror("Error", f"An unexpected error occurred: {str(e)}")
        finally:
            if tk._default_root:  # Check if Tk root still exists
                self.root.quit()
                self.root.destroy()

def main() -> None:
    """Run the Xplot application."""
    app = XplotApp()
    app.run()
    
    # Ensure all Tkinter windows are properly destroyed
    root = tk.Tk()
    root.withdraw()
    root.quit()
    root.destroy()

if __name__ == "__main__":  # pragma: no cover - manual invocation
    main()
