import tkinter as tk
from tkinter import ttk, scrolledtext
from typing import Dict, Any, List

class StatisticsDialog(tk.Toplevel):
    """
    Dialog to display data statistics generated during validation.
    """

    def __init__(self, parent: tk.Widget, validation_stats: Dict[str, Any]):
        super().__init__(parent)
        self.title("Data Statistics")
        self.geometry("550x450")

        self.validation_stats = validation_stats
        self._build_ui()
        
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self.destroy)

    def _build_ui(self) -> None:
        """Builds the UI components for displaying statistics."""
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        text_area = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, height=20, width=60)
        text_area.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        stats_content = self._format_statistics()
        text_area.insert(tk.END, stats_content)
        text_area.config(state=tk.DISABLED) # Make it read-only

        close_button = ttk.Button(main_frame, text="Close", command=self.destroy)
        close_button.pack(pady=10)

    def _format_statistics(self) -> str:
        """Formats the validation_stats dictionary into a displayable string."""
        stats = self.validation_stats
        lines: List[str] = []

        lines.append("Data Validation Statistics")
        lines.append("=" * 28 + "\n")

        lines.append(f"Total Data Points Considered (in ranges): {stats.get('total_points_considered', 'N/A')}")
        valid_points = stats.get('valid_points_for_plot', 0)
        total_considered = stats.get('total_points_considered', 0)
        percent_valid = (valid_points / max(1, total_considered)) * 100 if total_considered > 0 else 0
        lines.append(f"Valid Points for Plot (non-NaN X,Y,Z): {valid_points} ({percent_valid:.1f}%)\n")

        lines.append(f"X-Axis Unique Values: {stats.get('x_unique_values', 'N/A')}")
        lines.append(f"Y-Axis Unique Values: {stats.get('y_unique_values', 'N/A')}")

        if stats.get('class_unique_values') is not None:
            lines.append(f"Class Column Unique Values: {stats['class_unique_values']}")
        else:
            lines.append("Class Column: Not selected or no valid data.")
        
        if stats.get('z_unique_values') is not None:
            lines.append(f"Z-Axis (Colormap) Unique Values: {stats['z_unique_values']}\n")
        else:
            lines.append("Z-Axis (Colormap): Not selected or no valid data.\n")

        wells_with_data = stats.get('wells_with_data', [])
        lines.append(f"Wells Contributing Valid Data ({len(wells_with_data)}):")
        if wells_with_data:
            for well in wells_with_data: lines.append(f"  - {well}")
        else:
            lines.append("  (None)")
        lines.append("")

        wells_without_data = stats.get('wells_without_data', [])
        if wells_without_data:
            lines.append(f"Wells Skipped or With No Valid Data ({len(wells_without_data)}):")
            for well in wells_without_data: lines.append(f"  - {well}")
            lines.append("")

        wells_missing_cols = stats.get('wells_missing_cols', {})
        if wells_missing_cols:
            lines.append(f"Wells Missing Required Columns ({len(wells_missing_cols)}):")
            for well, missing_cols in wells_missing_cols.items():
                lines.append(f"  - {well}: Missing {', '.join(missing_cols)}")
            lines.append("")
            
        return "\n".join(lines)

# Ensure the example usage is properly guarded if included for testing
# if __name__ == '__main__':
#     root = tk.Tk()
#     # ... mock_stats definition ...
#     # Example:
#     # mock_stats = {
#     #     'total_points_considered': 1000, 'valid_points_for_plot': 800,
#     #     'x_unique_values': 50, 'y_unique_values': 60,
#     #     'class_unique_values': 5, 'z_unique_values': 70,
#     #     'wells_with_data': ['WellA', 'WellB'],
#     #     'wells_without_data': ['WellC'],
#     #     'wells_missing_cols': {'WellD': ['GR', 'DT']}
#     # }
#     dialog = StatisticsDialog(root, mock_stats)
#     root.wait_window(dialog)
#     root.destroy()
