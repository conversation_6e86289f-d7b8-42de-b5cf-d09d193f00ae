#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Processing Layer - Phase 2

This script tests the processing layer implementations to ensure they work correctly.

Author: EEI Function Modular Integration
Created: 2025-10-08
Version: 1.0.0
"""

import numpy as np
import sys
import os

# Add the module path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_data_processing():
    """Test core data processing functions."""
    print("🔄 Testing Core Data Processing...")
    
    try:
        from EEI_Function_Modular.core.data_processing import DataProcessor, MergedData
        
        # Create test data
        well1_data = {
            'depth': np.arange(1000, 1100, 0.5),
            'DT': np.random.normal(100, 10, 200),
            'RHOB': np.random.normal(2.3, 0.2, 200)
        }
        
        well2_data = {
            'depth': np.arange(1050, 1150, 0.5),
            'DT': np.random.normal(95, 8, 200),
            'RHOB': np.random.normal(2.4, 0.15, 200)
        }
        
        wells_data = {
            'Well_1': well1_data,
            'Well_2': well2_data
        }
        
        # Test merge function
        print("  Testing merge_well_data...")
        merged = DataProcessor.merge_well_data(wells_data)
        assert isinstance(merged, MergedData)
        assert len(merged.depth) > 0
        assert 'DT' in merged.data_arrays
        assert 'RHOB' in merged.data_arrays
        print("  ✅ merge_well_data passed")
        
        # Test interpolation
        print("  Testing interpolate_missing_values...")
        test_data = np.array([1.0, np.nan, 3.0, np.nan, 5.0])
        test_depth = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
        interpolated = DataProcessor.interpolate_missing_values(test_depth, test_data)
        assert not np.any(np.isnan(interpolated))
        print("  ✅ interpolate_missing_values passed")
        
        # Test robust limits
        print("  Testing get_robust_limits...")
        test_array = np.array([1, 2, 3, 4, 5])
        min_lim, max_lim = DataProcessor.get_robust_limits(test_array)
        assert min_lim < 1 and max_lim > 5
        print("  ✅ get_robust_limits passed")
        
        # Test find_nearest_index
        print("  Testing find_nearest_index...")
        test_array = np.array([1.0, 2.5, 4.0, 6.5, 8.0])
        idx = DataProcessor.find_nearest_index(test_array, 3.0)
        assert idx == 1  # Should find index of 2.5
        print("  ✅ find_nearest_index passed")
        
        print("✅ Core Data Processing tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Core Data Processing tests failed: {str(e)}")
        return False


def test_statistical_processing():
    """Test statistical processing functions."""
    print("🔄 Testing Statistical Processing...")
    
    try:
        from EEI_Function_Modular.core.statistical_processing import StatisticalProcessor
        
        # Create test data
        x_data = np.random.normal(0, 1, 100)
        y_data = 2 * x_data + np.random.normal(0, 0.1, 100)  # Strong correlation
        
        # Test correlation calculation
        print("  Testing calculate_correlation_safe...")
        corr_result = StatisticalProcessor.calculate_correlation_safe(x_data, y_data)
        assert corr_result.value > 0.8  # Should be strong positive correlation
        assert 'slope' in corr_result.statistics
        print("  ✅ calculate_correlation_safe passed")
        
        # Test array statistics
        print("  Testing calculate_array_statistics...")
        stats_result = StatisticalProcessor.calculate_array_statistics(x_data, include_distribution=True)
        assert 'mean' in stats_result.value
        assert 'std' in stats_result.value
        assert 'skewness' in stats_result.statistics
        print("  ✅ calculate_array_statistics passed")
        
        # Test array compatibility
        print("  Testing validate_array_compatibility...")
        arrays = [x_data, y_data, np.random.normal(0, 1, 100)]
        compat_result = StatisticalProcessor.validate_array_compatibility(arrays)
        assert compat_result.value == True
        print("  ✅ validate_array_compatibility passed")
        
        print("✅ Statistical Processing tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Statistical Processing tests failed: {str(e)}")
        return False


def test_eei_processing():
    """Test EEI-specific processing functions."""
    print("🔄 Testing EEI Processing...")
    
    try:
        from EEI_Function_Modular.specialized.eei_processing import EEIProcessor
        
        # Create realistic EEI test data
        n_points = 100
        depth = np.arange(1000, 1000 + n_points * 0.5, 0.5)
        dt_data = np.random.normal(100, 10, n_points)  # P-wave slowness
        dts_data = np.random.normal(200, 20, n_points)  # S-wave slowness
        rhob_data = np.random.normal(2.3, 0.2, n_points)  # Bulk density
        target_data = np.random.normal(50, 5, n_points)  # Target log
        
        well_data = {
            'Well_1': {
                'depth': depth,
                'DT': dt_data,
                'DTS': dts_data,
                'RHOB': rhob_data,
                'TARGET': target_data
            }
        }
        
        # Test EEI data preparation
        print("  Testing prepare_eei_data...")
        prep_result = EEIProcessor.prepare_eei_data(well_data)
        assert prep_result['success'] == True
        assert 'Well_1' in prep_result['well_status']
        print("  ✅ prepare_eei_data passed")
        
        # Test EEI input validation
        print("  Testing validate_eei_inputs...")
        validation = EEIProcessor.validate_eei_inputs(dt_data, dts_data, rhob_data, target_data)
        assert validation['valid'] == True
        print("  ✅ validate_eei_inputs passed")
        
        # Test EEI correlation analysis
        print("  Testing calculate_eei_correlation_analysis...")
        chi_angles = np.array([0, 15, 30, 45, 60, 75, 90])
        eei_result = EEIProcessor.calculate_eei_correlation_analysis(
            dt_data, dts_data, rhob_data, target_data, chi_angles
        )
        assert eei_result.success == True
        assert eei_result.correlations is not None
        assert len(eei_result.correlations) == len(chi_angles)
        print("  ✅ calculate_eei_correlation_analysis passed")
        
        print("✅ EEI Processing tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ EEI Processing tests failed: {str(e)}")
        return False


def main():
    """Run all processing layer tests."""
    print("🚀 Starting Processing Layer Tests - Phase 2")
    print("=" * 60)
    
    test_results = []
    
    # Run all tests
    test_results.append(test_data_processing())
    test_results.append(test_statistical_processing())
    test_results.append(test_eei_processing())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"✅ All {total} test suites passed!")
        print("🎉 Processing Layer implementation is working correctly!")
        return True
    else:
        print(f"❌ {total - passed} out of {total} test suites failed!")
        print("🔧 Please review and fix the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
