{"mcp": {"inputs": [], "servers": {"mcp-server-time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=Asia/Makassar"], "env": {}}, "Context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}, "brave-search": {"command": "npx", "args": ["-y", "brave-search-mcp"], "env": {"BRAVE_API_KEY": "BSA3nkpkp05lMLWPCNPOFSYtfcnuSC3"}}}}, "workbench.settings.applyToAllProfiles": ["mcp"], "chat.mcp.discovery.enabled": true}