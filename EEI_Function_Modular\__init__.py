# -*- coding: utf-8 -*-
"""
EEI Function Modular

A unified framework for Extended Elastic Impedance (EEI) analysis and
general well log cross-plotting. This package consolidates functionality
from EEI_XCorr_Modular and EEI_Xplot_Modular into a single, maintainable
codebase.

Author: Integration of EEI_XCorr_Modular and EEI_Xplot_Modular
Created: 2025
Version: 1.0.0
"""

__version__ = '1.0.0'
__author__ = 'EEI Function Modular Team'

# Import main configuration classes
from .config import (
    BaseConfig,
    EEIConfig,
    VisualizationConfig,
    UIConfig,
)

# Import core functionality
from .core import (
    load_las_files,
    load_excel_boundaries,
    DataValidator,
    ValidationResult,
)

__all__ = [
    '__version__',
    '__author__',
    'BaseConfig',
    'EEIConfig',
    'VisualizationConfig',
    'UIConfig',
    'load_las_files',
    'load_excel_boundaries',
    'DataValidator',
    'ValidationResult',
]
