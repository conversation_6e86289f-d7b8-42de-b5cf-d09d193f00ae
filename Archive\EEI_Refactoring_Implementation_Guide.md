# EEI Analysis Code Refactoring Implementation Guide

## Overview
This document provides a comprehensive, step-by-step implementation guide for refactoring the `a7x_load_multilas_EEI_XCOR_PLOT_Refactor.py` file from 4,942 lines to approximately 2,500-3,000 lines (40-50% reduction) while preserving all existing functionality.

## Current Code Analysis

### File Structure Overview
- **Total Lines**: 4,942
- **Main Sections**:
  - Helper functions (lines 30-163): 133 lines
  - Calculator functions (lines 165-762): 597 lines
  - Log analysis functions (lines 764-1179): 415 lines
  - Analysis optimization (lines 1180-1432): 252 lines
  - CPEI/PEIL functions (lines 1433-1911): 478 lines
  - Plotting functions (lines 1913-2427): 514 lines
  - Individual/merged analysis (lines 2429-3307): 878 lines
  - Dialog functions (lines 3309-4539): 1,230 lines
  - Main execution (lines 4541-4942): 401 lines

### Key Duplication Patterns Identified
1. **CPEI/PEIL Functions**: Nearly identical 105-line functions with only calculation method differences
2. **Plotting Code**: Repetitive matplotlib setup and formatting across multiple functions
3. **Validation Patterns**: Similar input validation logic repeated throughout
4. **Dialog Creation**: Repetitive Tkinter widget creation patterns
5. **Data Processing**: Common array slicing and depth range handling

## Implementation Plan

### Phase 1: Foundation Classes (Priority: High)

#### 1.1 Create Base Configuration System
**Target**: Replace hardcoded values with configuration dictionaries
**Estimated Savings**: 100-150 lines

```python
# File: config.py
ANALYSIS_CONFIG = {
    'EEI': {
        'calc_func': 'eeimpcalc',
        'param_range': range(-90, 91),
        'plot_type': 'line',
        'requires_k': True,
        'param_names': ['angle']
    },
    'CPEI': {
        'calc_func': 'calculate_cpei',
        'n_range': np.arange(0.1, 2.1, 0.1),
        'phi_range': range(-90, 91),
        'plot_type': 'heatmap',
        'requires_k': False,
        'param_names': ['n', 'phi']
    },
    'PEIL': {
        'calc_func': 'calculate_peil',
        'n_range': np.arange(0.1, 2.1, 0.1),
        'phi_range': range(-90, 91),
        'plot_type': 'heatmap',
        'requires_k': False,
        'param_names': ['n', 'phi']
    }
}

LOG_KEYWORDS = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC'],
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO'],
    # ... rest of keywords
}
```

#### 1.2 Create Unified Formatter Class
**Target**: Consolidate all safe formatting functions
**Estimated Savings**: 50-100 lines

```python
# Replace lines 32-81
class SafeFormatter:
    """Unified safe formatting utilities"""
    
    @staticmethod
    def format_value(value, format_type='float', precision=4, default='N/A', **kwargs):
        """Generic safe formatting for all value types"""
        if value is None:
            logger.warning(f"SafeFormatter: Received None value, returning default: {default}")
            return default
        
        try:
            formatters = {
                'float': lambda v: f"{float(v):.{precision}f}",
                'int': lambda v: f"{int(v)}",
                'angle': lambda v: f"{v}°",
                'parameter': lambda v: SafeFormatter._format_parameters(v, **kwargs)
            }
            
            return formatters.get(format_type, str)(value)
        except (ValueError, TypeError) as e:
            logger.error(f"SafeFormatter: Error formatting {value}: {str(e)}")
            return default
    
    @staticmethod
    def _format_parameters(params, **kwargs):
        """Format parameter dictionaries like n=1.0, phi=45°"""
        if isinstance(params, dict):
            formatted = []
            for key, val in params.items():
                if key == 'phi':
                    formatted.append(f"{key}={val}°")
                else:
                    formatted.append(f"{key}={SafeFormatter.format_value(val, 'float', 1)}")
            return ", ".join(formatted)
        return str(params)
```

#### 1.3 Create Data Validator Class
**Target**: Consolidate validation patterns
**Estimated Savings**: 150-200 lines

```python
# Replace lines 83-163 and scattered validation code
class DataValidator:
    """Centralized validation utilities"""
    
    @staticmethod
    def validate_arrays(*arrays, min_length=2, check_finite=True):
        """Generic array validation"""
        errors = []
        
        for i, arr in enumerate(arrays):
            if arr is None:
                errors.append(f"Array {i} is None")
                continue
                
            if not hasattr(arr, 'size') or arr.size == 0:
                errors.append(f"Array {i} is empty")
                continue
                
            if check_finite and not np.isfinite(arr).any():
                errors.append(f"Array {i} contains no finite values")
                continue
                
            if arr.size < min_length:
                errors.append(f"Array {i} has insufficient data points ({arr.size} < {min_length})")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_impedance_inputs(pvel, svel, rhob, params, analysis_type):
        """Unified validation for EEI/CPEI/PEIL inputs"""
        # Combine existing validation logic from validate_cpei_peil_inputs
        # and EEI validation patterns
        pass
    
    @staticmethod
    def validate_log_availability(las_files, required_logs):
        """Check log availability across wells"""
        # Consolidate logic from validate_calculation_inputs
        pass
```

### Phase 2: Consolidate Analysis Functions (Priority: High)

#### 2.1 Create Unified Impedance Calculator
**Target**: Replace separate CPEI/PEIL optimization functions
**Estimated Savings**: 400-500 lines

```python
# Replace lines 1433-1643 (calculate_cpei_optimum_parameters and calculate_peil_optimum_parameters)
class ImpedanceCalculator:
    """Unified calculator for EEI, CPEI, and PEIL analysis"""
    
    def __init__(self, analysis_type, config=None):
        self.analysis_type = analysis_type
        self.config = config or ANALYSIS_CONFIG[analysis_type]
        self.calc_func = globals()[self.config['calc_func']]
    
    def optimize_parameters(self, las, base_mnemonics, target_mnemonic, 
                          top_depth, bottom_depth, **kwargs):
        """Generic parameter optimization for any impedance type"""
        
        # Common data extraction (consolidate from both functions)
        data = self._extract_and_prepare_data(las, base_mnemonics, target_mnemonic, 
                                            top_depth, bottom_depth)
        
        if self.analysis_type == 'EEI':
            return self._optimize_eei(data, **kwargs)
        else:
            return self._optimize_2d_parameters(data, **kwargs)
    
    def _extract_and_prepare_data(self, las, base_mnemonics, target_mnemonic, 
                                top_depth, bottom_depth):
        """Common data extraction logic"""
        # Consolidate repetitive data extraction from lines 1454-1477 and 1560-1583
        depth = np.array(las[base_mnemonics['DEPTH']].data)
        dt = np.array(las[base_mnemonics['DT']].data)
        dts = np.array(las[base_mnemonics['DTS']].data)
        rhob = np.array(las[base_mnemonics['RHOB']].data)
        target = np.array(las[target_mnemonic].data)
        
        # Common depth slicing logic
        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)
        
        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index
        
        # Slice arrays
        sliced_data = {
            'depth': depth[top_index:bottom_index+1],
            'pvel': 304800 / dt[top_index:bottom_index+1],
            'svel': 304800 / dts[top_index:bottom_index+1],
            'rhob': rhob[top_index:bottom_index+1],
            'target': target[top_index:bottom_index+1]
        }
        
        return sliced_data
    
    def _optimize_2d_parameters(self, data, **kwargs):
        """Unified optimization for CPEI/PEIL (2D parameter space)"""
        n_values = self.config['n_range']
        phi_values = self.config['phi_range']
        correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)
        
        print(f"Starting {self.analysis_type} optimization...")
        print(f"Searching through {len(n_values)} n values and {len(phi_values)} phi values...")
        
        # Unified optimization loop (consolidate lines 1488-1520 and 1594-1626)
        for i, n in enumerate(n_values):
            for j, phi in enumerate(phi_values):
                try:
                    # Validate inputs
                    validation = DataValidator.validate_impedance_inputs(
                        data['pvel'], data['svel'], data['rhob'], 
                        {'n': n, 'phi': phi}, self.analysis_type
                    )
                    
                    if not validation[0]:
                        correlation_matrix[i, j] = np.nan
                        continue
                    
                    # Calculate impedance
                    impedance = self.calc_func(data['pvel'], data['svel'], data['rhob'], n, phi)
                    
                    if impedance is None or (hasattr(impedance, 'size') and impedance.size == 0):
                        correlation_matrix[i, j] = np.nan
                        continue
                    
                    correlation_matrix[i, j] = nanaware_corrcoef(impedance, data['target'])
                    
                except Exception as e:
                    logger.error(f"Error calculating {self.analysis_type} for n={n}, phi={phi}: {str(e)}")
                    correlation_matrix[i, j] = np.nan
        
        return self._find_optimal_parameters(correlation_matrix, n_values, phi_values)
    
    def _find_optimal_parameters(self, correlation_matrix, n_values, phi_values):
        """Find optimal parameters from correlation matrix"""
        if np.all(np.isnan(correlation_matrix)):
            print(f"All correlations are NaN. Unable to find optimum parameters for {self.analysis_type}.")
            return None, None, None, n_values, phi_values, correlation_matrix
        
        max_indices = np.unravel_index(np.nanargmax(correlation_matrix), correlation_matrix.shape)
        optimal_n = n_values[max_indices[0]]
        optimal_phi = phi_values[max_indices[1]]
        max_correlation = np.nanmax(correlation_matrix)
        
        print(f"{self.analysis_type} optimization complete:")
        print(f"  Optimal n: {SafeFormatter.format_value(optimal_n, 'float', 1)}")
        print(f"  Optimal phi: {optimal_phi}°")
        print(f"  Maximum correlation: {SafeFormatter.format_value(max_correlation, 'float', 4)}")
        
        return optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix
```

#### 2.2 Create Unified Plotting Calculator
**Target**: Replace separate plotting functions for CPEI/PEIL
**Estimated Savings**: 150-200 lines

```python
# Replace lines 1765-1911 (calculate_cpei_for_plotting and calculate_peil_for_plotting)
class ImpedancePlotter:
    """Unified plotting calculator for all impedance types"""
    
    @staticmethod
    def calculate_for_plotting(analysis_type, las, base_mnemonics, target_mnemonic, 
                             vcl_mnemonic, top_depth, bottom_depth, **params):
        """Generic calculation for plotting any impedance type"""
        
        try:
            # Common data extraction
            depth = np.array(las[base_mnemonics['DEPTH']].data)
            dt = np.array(las[base_mnemonics['DT']].data)
            dts = np.array(las[base_mnemonics['DTS']].data)
            rhob = np.array(las[base_mnemonics['RHOB']].data)
            target = np.array(las[target_mnemonic].data)
            
            # Handle VCL (optional)
            vol_wetclay = None
            if vcl_mnemonic and vcl_mnemonic in las.curves:
                vol_wetclay = np.array(las[vcl_mnemonic].data)
            
            # Convert to velocities
            pvel = 304800 / dt
            svel = 304800 / dts
            
            # Validate arrays
            valid, errors = DataValidator.validate_arrays(pvel, svel, rhob, target)
            if not valid:
                logger.error(f"Input validation failed for {analysis_type} plotting: {errors}")
                return None, None, None, None
            
            # Calculate impedance based on type
            if analysis_type == 'EEI':
                impedance, _, _ = eeimpcalc(pvel, svel, rhob, params['angle'], 
                                          params['k'], calcmethod=params['calcmethod'])
            elif analysis_type == 'CPEI':
                impedance = calculate_cpei(pvel, svel, rhob, params['n'], params['phi'])
            elif analysis_type == 'PEIL':
                impedance = calculate_peil(pvel, svel, rhob, params['n'], params['phi'])
            
            if impedance is None:
                logger.error(f"{analysis_type} calculation returned None for plotting")
                return None, None, None, None
            
            # Common normalization and slicing
            return ImpedancePlotter._normalize_and_slice(
                impedance, depth, target, vol_wetclay, top_depth, bottom_depth
            )
            
        except Exception as e:
            logger.error(f"Error in calculate_for_plotting for {analysis_type}: {str(e)}")
            return None, None, None, None
    
    @staticmethod
    def _normalize_and_slice(impedance, depth, target, vol_wetclay, top_depth, bottom_depth):
        """Common normalization and depth slicing logic"""
        # Consolidate normalization logic from both functions (lines 1798-1831 and 1872-1905)
        # Min-Max Normalization
        imp_min = np.nanmin(impedance)
        imp_max = np.nanmax(impedance)
        
        if imp_max != imp_min:
            norm_impedance = (impedance - imp_min) / (imp_max - imp_min)
        else:
            norm_impedance = impedance
        
        # Find depth indices
        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)
        
        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index
        
        # Slice arrays
        depth_sliced = depth[top_index:bottom_index+1]
        target_sliced = target[top_index:bottom_index+1]
        impedance_sliced = norm_impedance[top_index:bottom_index+1]
        vcl_sliced = vol_wetclay[top_index:bottom_index+1] if vol_wetclay is not None else None
        
        # Check for empty arrays
        if depth_sliced.size == 0:
            return None, None, None, None
        
        # Apply percentile clipping
        imp_low, imp_high = np.nanpercentile(impedance_sliced, [2, 98])
        target_low, target_high = np.nanpercentile(target_sliced, [2, 98])
        
        impedance_clipped = np.clip(impedance_sliced, imp_low, imp_high)
        target_clipped = np.clip(target_sliced, target_low, target_high)
        
        return depth_sliced, target_clipped, impedance_clipped, vcl_sliced
```

### Phase 3: Consolidate Analysis Workflows (Priority: Medium)

#### 3.1 Create Unified Well Analyzer
**Target**: Replace individual_well_analysis and merged_well_analysis
**Estimated Savings**: 600-800 lines

```python
# Replace lines 2429-3307
class WellAnalyzer:
    """Unified well analysis handler for individual and merged analysis"""
    
    def __init__(self, las_files, log_keywords, analysis_config):
        self.las_files = las_files
        self.log_keywords = log_keywords
        self.config = analysis_config
        self.alternative_mnemonics = {}
        self.required_base_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']
    
    def analyze(self, target_log, depth_ranges, analysis_mode='individual'):
        """Main analysis entry point"""
        if analysis_mode == 'individual':
            return self._analyze_individual_wells(target_log, depth_ranges)
        else:
            return self._analyze_merged_wells(target_log, depth_ranges)
    
    def _analyze_individual_wells(self, target_log, depth_ranges):
        """Individual well analysis with consolidated logic"""
        all_wells_results = []
        all_wells_data = []
        
        for las in self.las_files:
            well_name = las.well.WELL.value
            top_depth, bottom_depth = depth_ranges[well_name]
            
            # Common well preparation
            well_data = self._prepare_well_data(las, well_name, target_log)
            if not well_data['valid']:
                all_wells_results.append(self._create_empty_result(well_name, top_depth, bottom_depth))
                all_wells_data.append(self._create_empty_data(well_name))
                continue
            
            # Perform analysis based on method
            result = self._perform_well_analysis(
                las, well_data, well_name, target_log, top_depth, bottom_depth
            )
            
            all_wells_results.append(result['summary'])
            all_wells_data.append(result['data'])
        
        return all_wells_results, all_wells_data
    
    def _prepare_well_data(self, las, well_name, target_log):
        """Common well data preparation and validation"""
        # Consolidate repetitive well preparation logic
        current_well_columns = find_default_columns(las, self.log_keywords)
        
        # Validate base logs
        base_mnemonics = {}
        missing_logs = []
        
        for log_name in self.required_base_logs:
            mnemonic = current_well_columns.get(log_name)
            if mnemonic is None or mnemonic not in las.curves:
                missing_logs.append(f"{log_name} (searched: {self.log_keywords.get(log_name, [])})")
            else:
                base_mnemonics[log_name] = mnemonic
        
        if missing_logs:
            print(f"Warning: Missing base logs for well {well_name}: {', '.join(missing_logs)}")
            return {'valid': False, 'reason': 'missing_base_logs'}
        
        # Handle target log
        target_mnemonic = self._resolve_target_log(las, well_name, target_log, current_well_columns)
        if target_mnemonic is None:
            return {'valid': False, 'reason': 'missing_target_log'}
        
        # Optional VCL
        vcl_mnemonic = current_well_columns.get('VCL')
        
        return {
            'valid': True,
            'base_mnemonics': base_mnemonics,
            'target_mnemonic': target_mnemonic,
            'vcl_mnemonic': vcl_mnemonic
        }
    
    def _perform_well_analysis(self, las, well_data, well_name, target_log, top_depth, bottom_depth):
        """Perform analysis based on configured method"""
        calculator = ImpedanceCalculator(self.config['analysis_type'])
        
        if self.config['analysis_method'] == 1:  # EEI
            return self._analyze_eei(calculator, las, well_data, well_name, target_log, top_depth, bottom_depth)
        elif self.config['analysis_method'] == 2:  # CPEI
            return self._analyze_cpei_peil(calculator, las, well_data, well_name, target_log, top_depth, bottom_depth, 'CPEI')
        elif self.config['analysis_method'] == 3:  # PEIL
            return self._analyze_cpei_peil(calculator, las, well_data, well_name, target_log, top_depth, bottom_depth, 'PEIL')
    
    def _analyze_eei(self, calculator, las, well_data, well_name, target_log, top_depth, bottom_depth):
        """EEI-specific analysis logic"""
        # Consolidate EEI analysis from lines 2518-2565
        optimum_angle, max_correlation, angles, correlations = calculator.optimize_parameters(
            las, well_data['base_mnemonics'], well_data['target_mnemonic'],
            top_depth, bottom_depth, 
            calcmethod=self.config['calcmethod'],
            k_method=self.config['k_method'],
            k_value=self.config['k_value']
        )
        
        if optimum_angle is None:
            return {
                'summary': self._create_empty_result(well_name, top_depth, bottom_depth),
                'data': self._create_empty_data(well_name)
            }
        
        # Plot correlation vs angle
        self._plot_eei_correlation(angles, correlations, optimum_angle, well_name, target_log, top_depth, bottom_depth)
        
        # Calculate final EEI for plotting
        depth, target, normalized_eei, vol_wetclay = ImpedancePlotter.calculate_for_plotting(
            'EEI', las, well_data['base_mnemonics'], well_data['target_mnemonic'],
            well_data['vcl_mnemonic'], top_depth, bottom_depth,
            angle=optimum_angle, calcmethod=self.config['calcmethod'],
            k_method=self.config['k_method'], k_value=self.config['k_value']
        )
        
        return {
            'summary': {
                'well_name': well_name,
                'optimum_angle': optimum_angle,
                'max_correlation': max_correlation,
                'top_depth': top_depth,
                'bottom_depth': bottom_depth
            },
            'data': {
                'well_name': well_name,
                'depth': depth,
                'target': target,
                'normalized_eei': normalized_eei,
                'angle': optimum_angle,
                'vol_wetclay': vol_wetclay
            }
        }
```

### Phase 4: Consolidate Plotting Functions (Priority: Medium)

#### 4.1 Create Unified Plot Generator
**Target**: Consolidate repetitive plotting code
**Estimated Savings**: 300-400 lines

```python
# Replace scattered plotting code throughout the file
class PlotGenerator:
    """Unified plotting utilities for all analysis types"""
    
    @staticmethod
    def plot_correlation_heatmap(correlation_matrix, n_values, phi_values, 
                               optimal_n, optimal_phi, analysis_type, 
                               target_log, well_name=None, **kwargs):
        """Generic heatmap plotting for CPEI/PEIL"""
        # Consolidate heatmap plotting from lines 2596-2643 and 2704-2751
        plt.figure(figsize=(12, 8))
        plt.imshow(correlation_matrix, aspect='auto', origin='lower', cmap='viridis')
        plt.colorbar(label='Correlation Coefficient')
        plt.xlabel('Phi (degrees)')
        plt.ylabel('n (exponent)')
        
        title = f'{analysis_type}-{target_log} Correlation Matrix'
        if well_name:
            title += f' for Well: {well_name}'
            if 'top_depth' in kwargs and 'bottom_depth' in kwargs:
                title += f'\nDepth range: {SafeFormatter.format_value(kwargs["top_depth"], "float", 2)} - {SafeFormatter.format_value(kwargs["bottom_depth"], "float", 2)}'
        else:
            title += ' for Merged Wells'
        
        plt.title(title)
        
        # Set tick labels
        phi_ticks = range(0, len(phi_values), 30)
        n_ticks = range(0, len(n_values), 5)
        plt.xticks(phi_ticks, [phi_values[i] for i in phi_ticks])
        plt.yticks(n_ticks, [SafeFormatter.format_value(n_values[i], 'float', 1) for i in n_ticks])
        
        # Mark optimal point
        optimal_phi_idx = list(phi_values).index(optimal_phi)
        optimal_n_idx = list(n_values).index(optimal_n)
        
        label_text = f'Optimal: {SafeFormatter.format_value({"n": optimal_n, "phi": optimal_phi}, "parameter")}'
        plt.plot(optimal_phi_idx, optimal_n_idx, 'r*', markersize=15, label=label_text)
        
        # Add annotation
        max_correlation = np.nanmax(correlation_matrix)
        annotation_text = f'Max Correlation: {SafeFormatter.format_value(max_correlation, "float", 4)}'
        plt.annotate(annotation_text,
                    xy=(optimal_phi_idx, optimal_n_idx),
                    xytext=(optimal_phi_idx + 10, optimal_n_idx + 2),
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.2'),
                    fontsize=10, fontweight='bold')
        
        # Add summary text box
        PlotGenerator._add_summary_textbox(analysis_type, well_name, optimal_n, optimal_phi, 
                                         max_correlation, target_log)
        
        plt.legend()
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_well_comparison(all_wells_data, target_log, depth_ranges, analysis_type):
        """Unified plotting for individual well comparisons"""
        # Consolidate plotting logic from plot_eei_vs_target function
        global_min, global_max = PlotGenerator._calculate_global_percentiles(all_wells_data)
        
        # Get user input for axis ranges
        x_min, x_max = PlotGenerator._get_user_axis_range(target_log)
        
        for well_data in all_wells_data:
            if not PlotGenerator._validate_well_data(well_data):
                continue
            
            PlotGenerator._plot_single_well(well_data, target_log, depth_ranges, 
                                          analysis_type, global_min, global_max, 
                                          x_min, x_max)
    
    @staticmethod
    def _add_summary_textbox(analysis_type, well_name, optimal_n, optimal_phi, 
                           max_correlation, target_log):
        """Add summary text box to plots"""
        summary_text = f'{analysis_type} '
        summary_text += 'Individual Well Summary:\n' if well_name else 'Merged Analysis Summary:\n'
        
        if well_name:
            summary_text += f'Well: {well_name}\n'
        
        summary_text += f'Optimal n: {SafeFormatter.format_value(optimal_n, "float", 1)}\n'
        summary_text += f'Optimal φ: {SafeFormatter.format_value(optimal_phi, "float", 0)}°\n'
        summary_text += f'Max Correlation: {SafeFormatter.format_value(max_correlation, "float", 4)}\n'
        summary_text += f'Target: {target_log}'
        
        color = 'lightblue' if analysis_type == 'CPEI' else 'lightgreen'
        plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor=color, alpha=0.8),
                verticalalignment='top', fontsize=9, fontfamily='monospace')
```

### Phase 5: Simplify Dialog Creation (Priority: Low)

#### 5.1 Create Dialog Factory
**Target**: Consolidate repetitive Tkinter patterns
**Estimated Savings**: 200-300 lines

```python
# Replace repetitive dialog creation patterns
class DialogFactory:
    """Factory for creating common dialog patterns"""
    
    @staticmethod
    def create_selection_dialog(title, options, multi_select=False, **kwargs):
        """Generic selection dialog with common patterns"""
        root = tk.Tk()
        root.withdraw()
        
        dialog = tk.Toplevel()
        dialog.title(title)
        dialog.geometry(kwargs.get('geometry', '400x300'))
        dialog.grab_set()
        
        # Common frame setup
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Instructions
        if 'instructions' in kwargs:
            ttk.Label(main_frame, text=kwargs['instructions'], 
                     wraplength=380).pack(pady=(0, 10))
        
        # Selection widget
        if multi_select:
            selection_widget = DialogFactory._create_multi_selection(main_frame, options)
        else:
            selection_widget = DialogFactory._create_single_selection(main_frame, options)
        
        # Buttons
        result = {'selection': None, 'cancelled': False}
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(10, 0))
        
        def on_ok():
            result['selection'] = DialogFactory._get_selection(selection_widget, multi_select)
            dialog.destroy()
        
        def on_cancel():
            result['cancelled'] = True
            dialog.destroy()
        
        ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)
        
        # Center and show dialog
        DialogFactory._center_dialog(dialog)
        dialog.wait_window()
        root.destroy()
        
        return None if result['cancelled'] else result['selection']
    
    @staticmethod
    def create_parameter_input_dialog(title, parameters, **kwargs):
        """Generic parameter input dialog"""
        # Consolidate parameter input patterns
        pass
    
    @staticmethod
    def create_progress_dialog(title, message, **kwargs):
        """Generic progress dialog"""
        # Consolidate progress dialog patterns
        pass
```

### Phase 6: Optimize Main Workflow (Priority: Low)

#### 6.1 Create Workflow Orchestrator
**Target**: Simplify main execution function
**Estimated Savings**: 200-300 lines

```python
# Replace lines 4541-4942
class EEIAnalysisWorkflow:
    """Main workflow orchestrator"""
    
    def __init__(self):
        self.las_files = None
        self.analysis_config = None
        self.results = None
        self.logger = AnalysisLogger()
    
    def run(self):
        """Main workflow execution with step-by-step approach"""
        workflow_steps = [
            ('Loading Files', self._load_files),
            ('Validating Logs', self._validate_logs),
            ('Calculating Derived Logs', self._calculate_derived_logs),
            ('Getting User Inputs', self._get_user_inputs),
            ('Performing Analysis', self._perform_analysis),
            ('Generating Plots', self._generate_plots),
            ('Showing Results', self._show_results)
        ]
        
        for step_name, step_func in workflow_steps:
            self.logger.log_step(step_name, "Starting...")
            
            try:
                if not step_func():
                    self.logger.log_step(step_name, "Failed or cancelled")
                    return False
                self.logger.log_step(step_name, "Completed successfully")
            except Exception as e:
                self.logger.log_step(step_name, f"Error: {str(e)}")
                return False
        
        return True
    
    def _load_files(self):
        """File loading step"""
        self.las_files = load_multiple_las_files()
        return len(self.las_files) > 0
    
    def _validate_logs(self):
        """Log validation step"""
        validation_results = validate_essential_logs(self.las_files, LOG_KEYWORDS)
        validation_summary = generate_validation_report(validation_results)
        
        if validation_summary['invalid_files'] > 0:
            return self._ask_continue_with_missing_logs(validation_summary)
        return True
    
    def _get_user_inputs(self):
        """Consolidate all user input gathering"""
        # Get analysis parameters
        self.analysis_config = self._get_analysis_configuration()
        if not self.analysis_config:
            return False
        
        # Get target log
        self.analysis_config['target_log'] = self._get_target_log_selection()
        if not self.analysis_config['target_log']:
            return False
        
        # Get depth ranges
        self.analysis_config['depth_ranges'] = self._get_depth_ranges()
        if not self.analysis_config['depth_ranges']:
            return False
        
        return True
```

## Implementation Timeline

### Week 1: Foundation (Phase 1)
- [ ] Create configuration system
- [ ] Implement SafeFormatter class
- [ ] Implement DataValidator class
- [ ] Test basic functionality

### Week 2: Core Consolidation (Phase 2)
- [ ] Implement ImpedanceCalculator class
- [ ] Implement ImpedancePlotter class
- [ ] Test CPEI/PEIL consolidation
- [ ] Verify EEI compatibility

### Week 3: Analysis Workflows (Phase 3)
- [ ] Implement WellAnalyzer class
- [ ] Test individual well analysis
- [ ] Test merged well analysis
- [ ] Performance testing

### Week 4: Plotting and UI (Phase 4-5)
- [ ] Implement PlotGenerator class
- [ ] Implement DialogFactory class
- [ ] Test all plotting functions
- [ ] UI testing

### Week 5: Integration and Testing (Phase 6)
- [ ] Implement EEIAnalysisWorkflow class
- [ ] Integration testing
- [ ] Performance optimization
- [ ] Documentation updates

## Testing Strategy

### Unit Testing
```python
# Create test files for each new class
test_safe_formatter.py
test_data_validator.py
test_impedance_calculator.py
test_impedance_plotter.py
test_well_analyzer.py
test_plot_generator.py
test_dialog_factory.py
test_workflow.py
```

### Integration Testing
```python
# Test complete workflows
test_eei_workflow.py
test_cpei_workflow.py
test_peil_workflow.py
test_individual_analysis.py
test_merged_analysis.py
```

### Regression Testing
- Compare outputs with original implementation
- Verify all edge cases are handled
- Test with various LAS file formats
- Validate all plotting outputs

## Risk Mitigation

### Backup Strategy
1. Create feature branch: `feature/code-refactoring`
2. Keep original file as `a7x_load_multilas_EEI_XCOR_PLOT_Refactor_ORIGINAL.py`
3. Implement changes incrementally
4. Test each phase before proceeding

### Rollback Plan
- If any phase fails, revert to previous working state
- Maintain compatibility with existing data files
- Preserve all original function signatures during transition

### Quality Assurance
- Code review after each phase
- Automated testing suite
- Performance benchmarking
- User acceptance testing

## Expected Outcomes

### Quantitative Improvements
- **Code Reduction**: 40-50% (from 4,942 to ~2,500-3,000 lines)
- **Function Count**: Reduce from ~50 to ~25 functions
- **Duplication**: Eliminate 90% of duplicate code patterns
- **Maintainability**: Improve cyclomatic complexity by 60%

### Qualitative Improvements
- **Readability**: Clear separation of concerns
- **Testability**: Modular design enables unit testing
- **Extensibility**: Easy to add new impedance types
- **Performance**: Reduced memory footprint and faster execution

## Post-Implementation Tasks

### Documentation Updates
- [ ] Update function docstrings
- [ ] Create API documentation
- [ ] Update user manual
- [ ] Create developer guide

### Performance Optimization
- [ ] Profile memory usage
- [ ] Optimize critical paths
- [ ] Cache frequently used calculations
- [ ] Parallel processing for multi-well analysis

### Future Enhancements
- [ ] Plugin architecture for new impedance types
- [ ] Configuration file support
- [ ] Batch processing capabilities
- [ ] Web interface option

## Conclusion

This refactoring plan provides a systematic approach to reducing the EEI analysis code by 40-50% while improving maintainability, testability, and extensibility. The phased implementation approach minimizes risk while ensuring all existing functionality is preserved.

The key to success is the consolidation of duplicate patterns, particularly the CPEI/PEIL functions, plotting code, and validation logic. By implementing object-oriented design principles and configuration-driven approaches, the codebase will become more maintainable and easier to extend in the future.