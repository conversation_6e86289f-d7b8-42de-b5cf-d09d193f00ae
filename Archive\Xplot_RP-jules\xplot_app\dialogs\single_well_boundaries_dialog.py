import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from typing import Optional, Tuple, List, Any # Any for DataFrame

class SingleWellBoundariesDialog(tk.Toplevel):
    """
    Dialog to select top and bottom boundaries from Excel data for a specific well.
    """

    def __init__(self, parent: tk.Widget, 
                 boundary_df_for_well: pd.DataFrame, 
                 well_name: str) -> None:
        super().__init__(parent)
        self.title(f"Select Boundaries for {well_name}")
        self.geometry("450x350") # Adjusted size

        self.well_data = boundary_df_for_well.sort_values('MD') # Already filtered for the specific well
        self.well_name = well_name
        self.result_boundaries: Optional[Tuple[float, float]] = None

        if self.well_data.empty:
            messagebox.showerror(
                "Missing Well Data",
                f"No boundary data found for well '{self.well_name}' in the provided DataFrame.",
                parent=self
            )
            self.destroy()
            return
            
        self.surfaces = self.well_data['Surface'].unique().tolist()
        if len(self.surfaces) < 2:
             messagebox.showwarning(
                "Insufficient Data",
                f"Well '{self.well_name}' has fewer than two surfaces. Cannot select top and bottom.",
                parent=self
            )
             # Allow dialog to open but OK might not work well, or destroy:
             # self.destroy() 
             # return

        self._build_ui()
        
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
        # self.wait_window(self) # Make it properly modal by waiting if called directly

    def _build_ui(self) -> None:
        """Builds the main UI components of the dialog."""
        frame = ttk.Frame(self, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        self.top_surface_var = tk.StringVar()
        self.bottom_surface_var = tk.StringVar()

        if self.surfaces:
            self.top_surface_var.set(self.surfaces[0])
            if len(self.surfaces) > 1:
                self.bottom_surface_var.set(self.surfaces[-1])
            else: # Only one surface, set bottom to the same for now
                self.bottom_surface_var.set(self.surfaces[0])
        
        # Top Boundary
        ttk.Label(frame, text="Select Top Boundary:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.top_combo = ttk.Combobox(frame, textvariable=self.top_surface_var, values=self.surfaces, state="readonly", width=30)
        self.top_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        self.top_depth_label = ttk.Label(frame, text="Depth: N/A")
        self.top_depth_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # Bottom Boundary
        ttk.Label(frame, text="Select Bottom Boundary:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.bottom_combo = ttk.Combobox(frame, textvariable=self.bottom_surface_var, values=self.surfaces, state="readonly", width=30)
        self.bottom_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        self.bottom_depth_label = ttk.Label(frame, text="Depth: N/A")
        self.bottom_depth_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.top_surface_var.trace_add("write", self._update_depth_labels)
        self.bottom_surface_var.trace_add("write", self._update_depth_labels)
        self._update_depth_labels() # Initial call

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(20,0))
        ttk.Button(button_frame, text="OK", command=self._on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.LEFT, padx=5)

    def _update_depth_labels(self, *_args: Any) -> None:
        """Updates the depth display labels when surface selections change."""
        top_surface = self.top_surface_var.get()
        bottom_surface = self.bottom_surface_var.get()

        top_md_series = self.well_data[self.well_data['Surface'] == top_surface]['MD']
        bottom_md_series = self.well_data[self.well_data['Surface'] == bottom_surface]['MD']

        self.top_depth_val: Optional[float] = None
        self.bottom_depth_val: Optional[float] = None

        if not top_md_series.empty:
            self.top_depth_val = float(top_md_series.iloc[0])
            self.top_depth_label.config(text=f"Depth: {self.top_depth_val:.2f}")
        else:
            self.top_depth_label.config(text="Depth: N/A")

        if not bottom_md_series.empty:
            self.bottom_depth_val = float(bottom_md_series.iloc[0])
            self.bottom_depth_label.config(text=f"Depth: {self.bottom_depth_val:.2f}")
        else:
            self.bottom_depth_label.config(text="Depth: N/A")
            
    def _on_ok(self) -> None:
        """Validates selections and closes the dialog, storing results."""
        if self.top_depth_val is None or self.bottom_depth_val is None:
            messagebox.showerror("Missing Data", "Could not retrieve depth values for selected surfaces. Ensure surfaces are selected.", parent=self)
            return
        
        if self.bottom_depth_val <= self.top_depth_val:
            messagebox.showerror("Input Error", 
                                 f"Bottom depth ({self.bottom_depth_val:.2f}) must be greater than top depth ({self.top_depth_val:.2f}).",
                                 parent=self)
            return

        self.result_boundaries = (self.top_depth_val, self.bottom_depth_val)
        self.destroy()

    def _on_cancel(self) -> None:
        """Handles dialog cancellation."""
        self.result_boundaries = None
        self.destroy()

    def get_selected_boundaries(self) -> Optional[Tuple[float, float]]:
        """Returns the tuple of (top_depth, bottom_depth) or None if cancelled."""
        self.wait_window(self) # Ensures dialog closes before returning value
        return self.result_boundaries

# Example Usage (for testing the dialog standalone)
if __name__ == '__main__':
    from unittest.mock import Mock # Added for Mock LASFile in example
    root_app = tk.Tk()
    root_app.title("Main App (Test)")
    # root_app.withdraw() # Hide if only testing dialog logic

    sample_well_data = {
        'Well': ['Well-A', 'Well-A', 'Well-A'], 
        'Surface': ['Top1', 'Mid1', 'Base1'],
        'MD': [1000.0, 1500.0, 2000.0]
    }
    mock_df_well = pd.DataFrame(sample_well_data)
    
    print("Launching SingleWellBoundariesDialog...")
    dialog = SingleWellBoundariesDialog(root_app, mock_df_well, "Well-A")
    # The get_selected_boundaries now includes wait_window, so this call will block.
    selected_data = dialog.get_selected_boundaries() 
    
    if selected_data:
        print("Selected Boundaries for Well-A:", selected_data)
    else:
        print("Dialog for Well-A was cancelled or no valid selections.")
    
    # Only destroy if not running a full mainloop for UI testing
    if not root_app.winfo_viewable(): # If it was withdrawn or dialog was last to close
         root_app.destroy()
    elif root_app.children: # Check if there are still child windows (like the dialog if it wasn't destroyed on error)
        pass # Don't destroy if dialog might still be open from an error
    else:
        root_app.destroy()
