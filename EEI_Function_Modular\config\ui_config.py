# -*- coding: utf-8 -*-
"""
UI Configuration Module

This module contains configuration for user interface elements,
including window sizes, colors, fonts, and layout parameters.

Author: Migrated from EEI_XCorr_Modular
Created: 2025
Version: 1.0.0
"""

from typing import Dict, Any, Tuple

# ============================================================================
# UI CONFIGURATION
# ============================================================================

UI_CONFIG: Dict[str, Any] = {
    'window_sizes': {
        'calculator': (1000, 800),
        'target_selection': (350, 450),
        'depth_ranges': (600, 500),
        'boundary_selection': (800, 600),
        'column_selection': (500, 400),
        'plot_settings': (700, 600),
        'file_selection': (600, 400),
        'main_window': (1200, 900),
    },
    'colors': {
        'safe_logs': '#00CC00',
        'partial_logs': '#FF6600',
        'calculated_logs': '#0066CC',
        'error': '#FF0000',
        'warning': '#FFA500',
        'info': '#0000FF',
        'success': '#00AA00',
        'background': '#F0F0F0',
        'foreground': '#000000',
        'button_bg': '#E0E0E0',
        'button_active': '#D0D0D0',
    },
    'fonts': {
        'default': ('Arial', 10),
        'bold': ('Arial', 10, 'bold'),
        'small': ('Arial', 8),
        'large': ('Arial', 12),
        'code': ('Courier', 10),
        'title': ('Arial', 14, 'bold'),
        'monospace': ('Consolas', 10),
    },
    'padding': {
        'default': 5,
        'small': 2,
        'large': 10,
        'frame': 10,
    },
    'button_sizes': {
        'default': {'width': 15, 'height': 2},
        'small': {'width': 10, 'height': 1},
        'large': {'width': 20, 'height': 2},
    },
    'listbox_sizes': {
        'default': {'width': 40, 'height': 10},
        'small': {'width': 30, 'height': 5},
        'large': {'width': 60, 'height': 20},
    },
    'entry_sizes': {
        'default': 30,
        'small': 15,
        'large': 50,
    },
}

# ============================================================================
# DIALOG CONFIGURATION
# ============================================================================

DIALOG_CONFIG: Dict[str, Any] = {
    'default_position': 'center',  # 'center', 'mouse', or (x, y)
    'modal': True,
    'resizable': {
        'calculator': (True, True),
        'depth_ranges': (True, True),
        'column_selection': (False, False),
        'plot_settings': (True, True),
        'file_selection': (True, True),
    },
    'min_sizes': {
        'calculator': (800, 600),
        'depth_ranges': (500, 400),
        'column_selection': (400, 300),
        'plot_settings': (600, 500),
    }
}

# ============================================================================
# PROGRESS INDICATOR SETTINGS
# ============================================================================

PROGRESS_CONFIG: Dict[str, Any] = {
    'bar_length': 400,
    'bar_height': 30,
    'update_interval': 100,  # milliseconds
    'show_percentage': True,
    'show_time_remaining': True,
    'indeterminate_speed': 10,
}

# ============================================================================
# VALIDATION DISPLAY SETTINGS
# ============================================================================

VALIDATION_CONFIG: Dict[str, Any] = {
    'show_icons': True,
    'icon_size': 16,
    'icons': {
        'success': '✓',
        'error': '✗',
        'warning': '⚠',
        'info': 'ℹ',
    },
    'message_format': '{icon} {message}',
    'max_message_length': 200,
}

# ============================================================================
# UI CONFIGURATION CLASS
# ============================================================================

class UIConfig:
    """
    Configuration manager for user interface elements.
    
    Provides centralized access to UI configuration including
    window sizes, colors, fonts, and layout parameters.
    """
    
    @staticmethod
    def get_ui_config() -> Dict[str, Any]:
        """
        Get complete UI configuration.
        
        Returns:
            Dict containing all UI configuration
        """
        return UI_CONFIG.copy()
    
    @staticmethod
    def get_window_size(window_name: str) -> Tuple[int, int]:
        """
        Get window size for specified window.
        
        Args:
            window_name: Name of window
            
        Returns:
            Tuple of (width, height)
        """
        sizes = UI_CONFIG['window_sizes']
        return sizes.get(window_name, sizes['main_window'])
    
    @staticmethod
    def get_color(color_name: str) -> str:
        """
        Get color hex code for specified color name.
        
        Args:
            color_name: Name of color
            
        Returns:
            Hex color code
        """
        colors = UI_CONFIG['colors']
        return colors.get(color_name, colors['foreground'])
    
    @staticmethod
    def get_font(font_name: str = 'default') -> Tuple:
        """
        Get font specification.
        
        Args:
            font_name: Name of font style
            
        Returns:
            Tuple of font parameters
        """
        fonts = UI_CONFIG['fonts']
        return fonts.get(font_name, fonts['default'])
    
    @staticmethod
    def get_padding(padding_type: str = 'default') -> int:
        """
        Get padding value.
        
        Args:
            padding_type: Type of padding
            
        Returns:
            Padding value in pixels
        """
        padding = UI_CONFIG['padding']
        return padding.get(padding_type, padding['default'])
    
    @staticmethod
    def get_button_size(size_type: str = 'default') -> Dict[str, int]:
        """
        Get button size specification.
        
        Args:
            size_type: Type of button size
            
        Returns:
            Dict with width and height
        """
        sizes = UI_CONFIG['button_sizes']
        return sizes.get(size_type, sizes['default']).copy()
    
    @staticmethod
    def get_dialog_config(dialog_name: str) -> Dict[str, Any]:
        """
        Get dialog configuration.
        
        Args:
            dialog_name: Name of dialog
            
        Returns:
            Dict containing dialog configuration
        """
        config = DIALOG_CONFIG.copy()
        
        # Add specific resizable setting if available
        if dialog_name in config['resizable']:
            config['resizable'] = config['resizable'][dialog_name]
        else:
            config['resizable'] = (True, True)
        
        # Add specific min_size if available
        if dialog_name in config['min_sizes']:
            config['min_size'] = config['min_sizes'][dialog_name]
        
        return config
    
    @staticmethod
    def get_progress_config() -> Dict[str, Any]:
        """
        Get progress indicator configuration.
        
        Returns:
            Dict containing progress configuration
        """
        return PROGRESS_CONFIG.copy()
    
    @staticmethod
    def get_validation_config() -> Dict[str, Any]:
        """
        Get validation display configuration.
        
        Returns:
            Dict containing validation display settings
        """
        return VALIDATION_CONFIG.copy()
    
    @staticmethod
    def format_validation_message(message: str, status: str = 'info') -> str:
        """
        Format validation message with icon.
        
        Args:
            message: Message text
            status: Status type ('success', 'error', 'warning', 'info')
            
        Returns:
            Formatted message string
        """
        if not VALIDATION_CONFIG['show_icons']:
            return message
        
        icon = VALIDATION_CONFIG['icons'].get(status, '')
        return VALIDATION_CONFIG['message_format'].format(icon=icon, message=message)
