#!/usr/bin/env python3
"""
Demonstration script for Excel depth processing integration.

This script shows how to use the new Excel depth processing functionality
without requiring actual LAS files or Excel data.
"""

import pandas as pd
import numpy as np
from typing import Dict, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_excel_data():
    """Create sample Excel boundary data for demonstration."""
    print("Creating sample Excel boundary data...")
    
    # Sample data with multiple wells and geological surfaces
    data = {
        'Well': ['WELL_A', 'WELL_A', 'WELL_A', 'WELL_A', 'WELL_A', 'WELL_A',
                 'WELL_B', 'WELL_B', 'WELL_B', 'WELL_B', 'WELL_B', 'WELL_B',
                 'WELL_C', 'WELL_C', 'WELL_C', 'WELL_C'],
        'Surface': ['Top_Reservoir', 'Base_Reservoir', 'Top_Seal', 'Base_Seal', 'Top_Source', 'Base_Source',
                   'Top_Reservoir', 'Base_Reservoir', 'Top_Seal', 'Base_Seal', 'Top_Source', 'Base_Source',
                   'Top_Reservoir', 'Base_Reservoir', 'Top_Seal', 'Base_Seal'],
        'MD': [2100.5, 2250.8, 2050.2, 2100.5, 2300.1, 2450.7,
               2150.3, 2280.6, 2080.1, 2150.3, 2320.4, 2480.9,
               2200.7, 2350.2, 2120.5, 2200.7]
    }
    
    df = pd.DataFrame(data)
    print(f"Created sample data with {len(df)} boundary entries for {df['Well'].nunique()} wells")
    print("\nSample data preview:")
    print(df.head(10))
    
    return df

def create_mock_las_files():
    """Create mock LAS file objects for demonstration."""
    print("\nCreating mock LAS file objects...")
    
    class MockWell:
        def __init__(self, well_name):
            self.WELL = MockValue(well_name)
    
    class MockValue:
        def __init__(self, value):
            self.value = value
    
    class MockCurve:
        def __init__(self, mnemonic):
            self.mnemonic = mnemonic
    
    class MockLAS:
        def __init__(self, well_name):
            self.well = MockWell(well_name)
            self.curves = [
                MockCurve("DEPTH"),
                MockCurve("DT"),
                MockCurve("RHOB"),
                MockCurve("NPHI"),
                MockCurve("GR")
            ]
            # Mock data for depth calculations
            self.data = {
                "DEPTH": np.linspace(2000, 2500, 500),
                "DT": np.random.normal(100, 20, 500)
            }
        
        def __getitem__(self, key):
            return MockData(self.data.get(key, np.array([])))
    
    class MockData:
        def __init__(self, data):
            self.data = data
    
    mock_las_files = [
        MockLAS("WELL_A"),
        MockLAS("WELL_B"),
        MockLAS("WELL_C")
    ]
    
    print(f"Created {len(mock_las_files)} mock LAS files")
    return mock_las_files

def demonstrate_data_io_functions():
    """Demonstrate the enhanced data_io functions."""
    print("\n" + "="*60)
    print("DEMONSTRATING DATA_IO FUNCTIONS")
    print("="*60)
    
    try:
        import data_io
        import config
        
        # Create mock data
        mock_las_files = create_mock_las_files()
        sample_excel_df = create_sample_excel_data()
        
        print("\n1. Testing find_default_columns function:")
        las = mock_las_files[0]
        columns = data_io.find_default_columns(las, config.log_keywords)
        print(f"   Found columns: {columns}")
        
        print("\n2. Testing filter_excel_data_for_las_wells function:")
        filtered_df = data_io.filter_excel_data_for_las_wells(sample_excel_df, mock_las_files)
        if filtered_df is not None:
            print(f"   Filtered data: {len(filtered_df)} entries for {filtered_df['Well'].nunique()} wells")
            print(f"   Wells in filtered data: {list(filtered_df['Well'].unique())}")
        else:
            print("   No matching wells found")
        
        return True
        
    except Exception as e:
        print(f"Error in data_io demonstration: {e}")
        return False

def demonstrate_enhanced_dialog():
    """Demonstrate the enhanced dialog functionality (without GUI)."""
    print("\n" + "="*60)
    print("DEMONSTRATING ENHANCED DIALOG FUNCTIONALITY")
    print("="*60)
    
    try:
        from dialogs.enhanced_depth_dialog import EnhancedDepthDialog
        import config
        
        # Create mock data
        mock_las_files = create_mock_las_files()
        sample_excel_df = create_sample_excel_data()
        
        print("\n1. Creating EnhancedDepthDialog instance:")
        dialog = EnhancedDepthDialog()
        print("   ✓ Dialog instance created successfully")
        
        print("\n2. Testing boundary selection logic (without GUI):")
        # Test the data filtering logic that would be used in the GUI
        las_well_names = [las.well.WELL.value for las in mock_las_files]
        available_wells = [well for well in las_well_names if well in sample_excel_df['Well'].values]
        print(f"   Available wells for boundary selection: {available_wells}")
        
        print("\n3. Testing surface data extraction:")
        for well_name in available_wells[:2]:  # Test first 2 wells
            well_data = sample_excel_df[sample_excel_df['Well'] == well_name]
            surfaces = well_data['Surface'].unique().tolist()
            print(f"   Well {well_name}: {len(surfaces)} surfaces available")
            print(f"      Surfaces: {surfaces}")
            
            # Show depth range for this well
            min_depth = well_data['MD'].min()
            max_depth = well_data['MD'].max()
            print(f"      Depth range: {min_depth:.1f} - {max_depth:.1f}")
        
        return True
        
    except Exception as e:
        print(f"Error in enhanced dialog demonstration: {e}")
        return False

def demonstrate_integration_workflow():
    """Demonstrate the complete integration workflow."""
    print("\n" + "="*60)
    print("DEMONSTRATING INTEGRATION WORKFLOW")
    print("="*60)
    
    try:
        # Simulate the main application workflow
        print("\n1. Loading LAS files (simulated):")
        mock_las_files = create_mock_las_files()
        print(f"   ✓ Loaded {len(mock_las_files)} LAS files")
        
        print("\n2. Loading Excel depth ranges (simulated):")
        sample_excel_df = create_sample_excel_data()
        print(f"   ✓ Loaded Excel data with {len(sample_excel_df)} boundary entries")
        
        print("\n3. Filtering Excel data for LAS wells:")
        import data_io
        filtered_df = data_io.filter_excel_data_for_las_wells(sample_excel_df, mock_las_files)
        if filtered_df is not None:
            print(f"   ✓ Filtered to {len(filtered_df)} entries for matching wells")
        
        print("\n4. Preparing for depth range selection:")
        print("   ✓ Enhanced dialog system ready")
        print("   ✓ Excel data preloaded and available")
        print("   ✓ Manual input fallback available")
        
        print("\n5. Integration benefits demonstrated:")
        print("   • Geological marker-based depth selection")
        print("   • Batch processing for multiple wells")
        print("   • Automatic validation and error handling")
        print("   • Flexible input methods (Excel + Manual)")
        print("   • Comprehensive logging and feedback")
        
        return True
        
    except Exception as e:
        print(f"Error in integration workflow demonstration: {e}")
        return False

def main():
    """Run the complete demonstration."""
    print("="*80)
    print("EXCEL DEPTH PROCESSING INTEGRATION DEMONSTRATION")
    print("="*80)
    print("\nThis demonstration shows the new Excel depth processing functionality")
    print("integrated into the Xplot application.")
    
    demonstrations = [
        ("Data I/O Functions", demonstrate_data_io_functions),
        ("Enhanced Dialog System", demonstrate_enhanced_dialog),
        ("Integration Workflow", demonstrate_integration_workflow),
    ]
    
    results = []
    
    for name, demo_func in demonstrations:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            success = demo_func()
            results.append((name, success))
            if success:
                print(f"\n✓ {name} demonstration completed successfully")
            else:
                print(f"\n✗ {name} demonstration failed")
        except Exception as e:
            print(f"\n✗ {name} demonstration crashed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "="*80)
    print("DEMONSTRATION SUMMARY")
    print("="*80)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✓ PASSED" if success else "✗ FAILED"
        print(f"{status}: {name}")
    
    print(f"\nOverall: {successful}/{total} demonstrations successful")
    
    if successful == total:
        print("\n🎉 All demonstrations passed! Integration is working correctly.")
        print("\nThe Excel depth processing functionality is ready for use.")
        print("Users can now:")
        print("• Load Excel files with geological markers")
        print("• Select depth ranges using surface names")
        print("• Process multiple wells simultaneously")
        print("• Benefit from comprehensive validation and error handling")
    else:
        print("\n⚠️  Some demonstrations failed. Please check the implementation.")
    
    return 0 if successful == total else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
