# Python bytecode
__pycache__/
*.pyc
*.pyo
*.pyd

# Distribution / packaging
build/
dist/
*.egg-info/
*.egg
wheels/
*.tar.gz
*.zip

# Virtual environment folders
venv/
env/
*.venv

# IDE configuration files
.vscode/
.idea/

# Common OS files
.DS_Store
Thumbs.db

# Notebook checkpoints
.ipynb_checkpoints/

# Caching and temporary files from specific libraries (if any become problematic)
# .mypy_cache/
# .pytest_cache/
# .ruff_cache/

# Log files
*.log

# Configuration files that might contain secrets or local overrides
# config.local.py
# .env

# Data files (if large or specific to a run, consider adding specific patterns)
# data/raw/
# data/processed/

# Output files from scripts/analysis
# results/
# output/

# Reports
# reports/

# Documentation build output
# docs/_build/
# site/

# Coverage reports
.coverage
coverage.xml
htmlcov/
