"""Plotting utilities and dialogs for the Xplot application."""

from __future__ import annotations

from typing import Any, Dict, List, Optional

import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import ttk
from scipy import stats

import processing
from statistical_analyzers import StatisticalVisualizer


def show_data_statistics(validation_stats: Dict[str, Any]) -> None:
    """Display a dialog with statistics about the plotted data."""
    stats_window = tk.Toplevel()
    stats_window.title("Data Statistics")
    stats_window.geometry("500x400")

    text_frame = tk.Frame(stats_window)
    text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    text_widget = tk.Text(text_frame, wrap=tk.WORD)
    scrollbar = tk.Scrollbar(text_frame, command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)

    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    text_widget.insert(tk.END, "Data Statistics\n", "heading")
    text_widget.insert(tk.END, "=============\n\n")

    text_widget.insert(tk.END, f"Total data points: {validation_stats['total_points']}\n")
    text_widget.insert(tk.END, f"Valid data points: {validation_stats['valid_points']} ")
    text_widget.insert(
        tk.END,
        f"({validation_stats['valid_points'] / max(1, validation_stats['total_points']) * 100:.1f}%)\n\n",
    )

    text_widget.insert(tk.END, f"X-axis unique values: {validation_stats['x_unique_values']}\n")
    text_widget.insert(tk.END, f"Y-axis unique values: {validation_stats['y_unique_values']}\n")

    if validation_stats['class_unique_values'] is not None:
        text_widget.insert(tk.END, f"Class unique values: {validation_stats['class_unique_values']}\n")
    else:
        text_widget.insert(tk.END, "No class column selected.\n")

    if validation_stats.get('z_unique_values') is not None:
        text_widget.insert(tk.END, f"Z-axis unique values: {validation_stats['z_unique_values']}\n\n")
    else:
        text_widget.insert(tk.END, "No Z column selected.\n\n")

    text_widget.insert(tk.END, "Wells with valid data:\n")
    for well in validation_stats['wells_with_data']:
        text_widget.insert(tk.END, f"• {well}\n")

    if validation_stats['wells_without_data']:
        text_widget.insert(tk.END, "\nWells without valid data:\n")
        for well in validation_stats['wells_without_data']:
            text_widget.insert(tk.END, f"• {well}\n")

    text_widget.configure(state=tk.DISABLED)
    text_widget.tag_configure("heading", font=("Arial", 12, "bold"))

    tk.Button(stats_window, text="Close", command=stats_window.destroy).pack(pady=10)


def show_post_plot_options() -> str:
    """Show a dialog with options to restart or exit after plotting."""
    root = tk.Tk()
    root.withdraw()

    dialog = tk.Toplevel()
    dialog.title("Analysis Complete")
    dialog.geometry("450x250")
    dialog.resizable(False, False)
    dialog.transient()
    dialog.grab_set()

    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
    y = (dialog.winfo_screenheight() // 2) - (250 // 2)
    dialog.geometry(f"450x250+{x}+{y}")

    result = {"choice": "exit"}

    frame = ttk.Frame(dialog, padding="20")
    frame.pack(fill=tk.BOTH, expand=True)

    message = ttk.Label(
        frame,
        text="Crossplot analysis complete!\n\nWhat would you like to do next?",
        font=("Arial", 12),
        justify=tk.CENTER,
    )
    message.pack(pady=(0, 20))

    description = ttk.Label(
        frame,
        text="• Start New Analysis: Load new LAS files and begin fresh analysis\n• Exit Application: Close the application",
        font=("Arial", 10),
        justify=tk.LEFT,
    )
    description.pack(pady=(0, 20))

    button_frame = ttk.Frame(frame)
    button_frame.pack(pady=10)

    def on_restart() -> None:
        result["choice"] = "restart"
        dialog.destroy()
        root.destroy()

    def on_exit() -> None:
        result["choice"] = "exit"
        dialog.destroy()
        root.destroy()

    restart_btn = ttk.Button(button_frame, text="Start New Analysis", command=on_restart)
    restart_btn.pack(side=tk.LEFT, padx=(0, 15))
    exit_btn = ttk.Button(button_frame, text="Exit Application", command=on_exit)
    exit_btn.pack(side=tk.LEFT)

    dialog.protocol("WM_DELETE_WINDOW", on_exit)
    dialog.focus_set()
    dialog.wait_window()

    return result["choice"]


def create_plot(
    las_files: List,
    x_col: str,
    y_col: str,
    class_col: Optional[str],
    depth_ranges: Dict[str, tuple],
    settings: Dict[str, Any],
    z_col: Optional[str] = None,
) -> str:
    """Create the crossplot and return the user's post-plot choice."""
    validation_result = processing.validate_data_for_plotting(
        las_files, x_col, y_col, class_col, depth_ranges, z_col
    )

    fig = plt.figure(figsize=(12, 10))
    if not validation_result["valid"]:
        ax = fig.add_subplot(111)
        error_message = "Cannot create plot:\n\n" + "\n".join(
            f"• {issue}" for issue in validation_result["issues"]
        )
        ax.text(
            0.5,
            0.5,
            error_message,
            horizontalalignment="center",
            verticalalignment="center",
            transform=ax.transAxes,
            wrap=True,
            fontsize=12,
        )
        ax.set_xticks([])
        ax.set_yticks([])
        plt.title(f"Plot Error: {x_col} vs {y_col}", fontsize=14, fontweight="bold")
        plt.show()
        return show_post_plot_options()

    ax_histx, ax_histy = None, None
    if class_col and z_col is None:
        gs = fig.add_gridspec(4, 4, hspace=0.05, wspace=0.05)
        ax_main = fig.add_subplot(gs[1:, :-1])
        ax_histx = fig.add_subplot(gs[0, :-1], sharex=ax_main)
        ax_histy = fig.add_subplot(gs[1:, -1], sharey=ax_main)
        plt.setp(ax_histx.get_xticklabels(), visible=False)
        plt.setp(ax_histx.get_yticklabels(), visible=False)
        ax_histx.set_yticks([])
        plt.setp(ax_histy.get_xticklabels(), visible=False)
        plt.setp(ax_histy.get_yticklabels(), visible=False)
        ax_histy.set_xticks([])
    else:
        gs = fig.add_gridspec(1, 1)
        ax_main = fig.add_subplot(gs[0, 0])

    all_x_data: List[float] = []
    all_y_data: List[float] = []
    all_class_data_for_plot: List[float] = []
    all_z_data: List[float] = []
    overall_x_min, overall_x_max = float("inf"), float("-inf")
    overall_y_min, overall_y_max = float("inf"), float("-inf")
    wells_with_issues: List[str] = []

    for las in las_files:
        well_name = las.well.WELL.value
        if well_name not in depth_ranges:
            wells_with_issues.append(f"{well_name}: No depth range specified")
            continue
        top_depth, bottom_depth = depth_ranges[well_name]
        depth_curve = las.curves.get("DEPTH")
        if depth_curve is None:
            wells_with_issues.append(f"{well_name}: Missing DEPTH curve")
            continue
        depth = np.array(depth_curve.data)
        mask = (depth >= top_depth) & (depth <= bottom_depth)
        if not np.any(mask):
            wells_with_issues.append(f"{well_name}: No data in specified depth range")
            continue
        if x_col not in las.curves or y_col not in las.curves:
            wells_with_issues.append(f"{well_name}: Missing columns")
            continue
        x_data_full = np.array(las[x_col].data)[mask]
        y_data_full = np.array(las[y_col].data)[mask]
        z_data_full = np.array(las[z_col].data)[mask] if z_col and z_col in las.curves else None
        class_data = np.array(las[class_col].data)[mask] if class_col and class_col in las.curves else None

        valid_mask_xy = ~np.isnan(x_data_full) & ~np.isnan(y_data_full)
        if z_data_full is not None:
            valid_mask_xy &= ~np.isnan(z_data_full)
        x_data_clean = x_data_full[valid_mask_xy]
        y_data_clean = y_data_full[valid_mask_xy]
        z_data_clean = z_data_full[valid_mask_xy] if z_data_full is not None else None
        class_data_clean = class_data[valid_mask_xy] if class_data is not None else None

        if len(x_data_clean) == 0 or len(y_data_clean) == 0:
            wells_with_issues.append(f"{well_name}: No valid data")
            continue

        all_x_data.extend(x_data_clean)
        all_y_data.extend(y_data_clean)
        if class_data_clean is not None:
            all_class_data_for_plot.extend(class_data_clean)
        if z_data_clean is not None:
            all_z_data.extend(z_data_clean)

    all_x_data_np = np.array(all_x_data)
    all_y_data_np = np.array(all_y_data)
    all_z_data_np = np.array(all_z_data) if all_z_data else None
    all_class_data_np = (
        np.array(all_class_data_for_plot) if class_col and all_class_data_for_plot else None
    )

    if all_x_data_np.size == 0:
        ax_main.text(
            0.5,
            0.5,
            "No data to display for the selected criteria.",
            horizontalalignment="center",
            verticalalignment="center",
            transform=ax_main.transAxes,
            wrap=True,
            fontsize=12,
        )
        plt.show()
        return show_post_plot_options()

    if z_col and all_z_data_np is not None and len(all_z_data_np) > 0:
        scatter = ax_main.scatter(
            all_x_data_np,
            all_y_data_np,
            c=all_z_data_np,
            s=settings.get("point_size", 50),
            cmap=settings.get("colormap", "viridis"),
            alpha=0.7,
        )
        if settings.get("show_colorbar", True):
            plt.colorbar(scatter, ax=ax_main)
    elif class_col and all_class_data_np is not None and settings.get("symbols"):
        unique_plot_classes = np.unique(all_class_data_np[~np.isnan(all_class_data_np)])
        for class_val in unique_plot_classes:
            class_mask = all_class_data_np == class_val
            ax_main.scatter(
                all_x_data_np[class_mask],
                all_y_data_np[class_mask],
                s=settings.get("point_size", 50),
                color=settings["colors"].get(class_val, "#000000"),
                marker=settings["symbols"].get(class_val, "o"),
                label=settings["class_names"].get(class_val, f"Class {class_val}"),
                alpha=0.7,
            )

        # Handle NaN class if it exists in settings
        if 'NaN' in settings.get('symbols', {}) and 'NaN' in settings.get('colors', {}):
            nan_mask = np.isnan(all_class_data_np)
            if np.any(nan_mask):
                ax_main.scatter(
                    all_x_data_np[nan_mask],
                    all_y_data_np[nan_mask],
                    s=settings.get("point_size", 50),
                    color=settings['colors'].get('NaN', '#808080'),
                    marker=settings['symbols'].get('NaN', 'x'),
                    label=settings['class_names'].get('NaN', 'No Data'),
                    alpha=0.7,
                )
    else:
        ax_main.scatter(
            all_x_data_np,
            all_y_data_np,
            s=settings.get("point_size", 50),
            color="blue",
            marker="o",
            alpha=0.7,
            label=f"{x_col} vs {y_col}",
        )

    user_x_min, user_x_max = settings.get("x_axis_min_user"), settings.get("x_axis_max_user")
    user_y_min, user_y_max = settings.get("y_axis_min_user"), settings.get("y_axis_max_user")
    if all_x_data_np.size > 0 and (user_x_min is None or user_x_max is None):
        robust_x_min, robust_x_max = processing.get_robust_limits(all_x_data_np)
        final_x_min = user_x_min if user_x_min is not None else robust_x_min
        final_x_max = user_x_max if user_x_max is not None else robust_x_max
    else:
        final_x_min = user_x_min
        final_x_max = user_x_max

    if all_y_data_np.size > 0 and (user_y_min is None or user_y_max is None):
        robust_y_min, robust_y_max = processing.get_robust_limits(all_y_data_np)
        final_y_min = user_y_min if user_y_min is not None else robust_y_min
        final_y_max = user_y_max if user_y_max is not None else robust_y_max
    else:
        final_y_min = user_y_min
        final_y_max = user_y_max

    if final_x_min is not None and final_x_max is not None:
        ax_main.set_xlim(final_x_min, final_x_max)
    if final_y_min is not None and final_y_max is not None:
        ax_main.set_ylim(final_y_min, final_y_max)

    if ax_histx and ax_histy:
        x_data_for_marginal = all_x_data_np[~np.isnan(all_x_data_np)]
        y_data_for_marginal = all_y_data_np[~np.isnan(all_y_data_np)]
        class_data_for_marginal = None
        if all_class_data_np is not None and len(all_class_data_np) > 0:
            valid_mask = ~np.isnan(all_x_data_np) & ~np.isnan(all_y_data_np)
            class_data_for_marginal = all_class_data_np[valid_mask]
        visualizer = StatisticalVisualizer(
            x_data_for_marginal,
            y_data_for_marginal,
            class_data_for_marginal,
            settings.get("colors"),
            settings.get("class_names"),
        )
        visualizer.plot_marginal_distributions(
            ax_histx,
            ax_histy,
            plot_type=settings.get("marginal_plot_type", "Both"),
        )

    ax_main.set_xlabel(settings.get("x_title_text", x_col))
    ax_main.set_ylabel(settings.get("y_title_text", y_col))
    handles, labels = ax_main.get_legend_handles_labels()
    if handles:
        ax_main.legend(handles, labels, fontsize=settings.get("legend_font_size", 10))

    fig.tight_layout(rect=[0, 0.03, 1, 0.95])
    title = f"Crossplot: {settings.get('x_title_text', x_col)} vs {settings.get('y_title_text', y_col)}"
    fig.suptitle(title, fontsize=16, fontweight="bold")
    plt.show()

    show_data_statistics(validation_result["stats"])
    return show_post_plot_options()
