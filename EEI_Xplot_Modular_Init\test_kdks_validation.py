#!/usr/bin/env python3
"""
Test script to verify that the KdKs ratio calculation with np.clip works correctly
after fixing the log availability validation logic.
"""

import sys
import os
import numpy as np
import re
from unittest.mock import Mock

# Inline copy of the fixed validation function to avoid import issues
def validate_calculation_inputs(las_files, calculation_text):
    """
    Validate that all logs referenced in calculations exist in all wells.
    Distinguishes between input variables (must exist) and output variables (being created).
    """
    print("🔄 Validation: Starting validate_calculation_inputs()...")
    print(f"🔄 Validation: Calculation text length: {len(calculation_text)}")
    print(f"🔄 Validation: Number of LAS files: {len(las_files)}")

    # Parse calculation text to separate output variables from input variables
    print("🔄 Validation: Parsing calculation text to identify input vs output variables...")

    # Split into lines and analyze each assignment
    lines = [line.strip() for line in calculation_text.split('\n') if line.strip()]
    output_variables = set()
    input_variables = set()

    for line in lines:
        print(f"🔄 Validation: Analyzing line: '{line}'")

        # Skip comments
        if line.startswith('#'):
            continue

        # Look for assignment operations (=, +=, -=, *=, /=)
        if '=' in line and not any(op in line for op in ['==', '!=', '<=', '>=']):
            # Split on assignment operator
            if '+=' in line or '-=' in line or '*=' in line or '/=' in line:
                # Compound assignment - left side is both input and output
                left_side = line.split('=')[0].strip()
                right_side = '='.join(line.split('=')[1:]).strip()
            else:
                # Regular assignment
                parts = line.split('=', 1)
                if len(parts) == 2:
                    left_side = parts[0].strip()
                    right_side = parts[1].strip()
                else:
                    continue

            # Extract output variable name (left side of assignment)
            # Handle array indexing like VAR[0] = ...
            output_var = re.match(r'^([A-Z_][A-Z0-9_]*)', left_side.upper())
            if output_var:
                output_variables.add(output_var.group(1))
                print(f"🔄 Validation: Found output variable: {output_var.group(1)}")

            # Extract input variables from right side
            right_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', right_side.upper()))
            input_variables.update(right_vars)
            print(f"🔄 Validation: Found input variables in right side: {right_vars}")
        else:
            # No assignment, treat all variables as input
            line_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', line.upper()))
            input_variables.update(line_vars)
            print(f"🔄 Validation: Found variables in non-assignment line: {line_vars}")

    # Remove numpy functions and common Python keywords from input variables
    numpy_functions = {
        'NP', 'NUMPY', 'LOG', 'SQRT', 'EXP', 'SIN', 'COS', 'TAN',
        'NANMIN', 'NANMAX', 'NANMEAN', 'NANSTD', 'WHERE', 'ISNAN', 'ISFINITE',
        # Mathematical functions
        'CLIP', 'ABS', 'ABSOLUTE', 'MIN', 'MAX', 'MEAN', 'STD', 'SUM', 'PROD',
        'ROUND', 'FLOOR', 'CEIL', 'POWER', 'POW', 'SIGN',
        # Array functions
        'ARRAY', 'ZEROS', 'ONES', 'CONCATENATE', 'STACK', 'RESHAPE', 'FLATTEN',
        # Trigonometric functions
        'ARCSIN', 'ARCCOS', 'ARCTAN', 'ARCTAN2', 'SINH', 'COSH', 'TANH',
        # Statistical functions
        'MEDIAN', 'PERCENTILE', 'QUANTILE', 'VAR', 'CORRCOEF', 'COV',
        # Logical functions
        'ALL', 'ANY', 'LOGICAL_AND', 'LOGICAL_OR', 'LOGICAL_NOT',
        # Comparison functions
        'GREATER', 'LESS', 'EQUAL', 'NOT_EQUAL', 'GREATER_EQUAL', 'LESS_EQUAL'
    }
    python_keywords = {'IF', 'ELSE', 'FOR', 'WHILE', 'DEF', 'CLASS', 'IMPORT', 'FROM', 'AS', 'RETURN', 'TRUE', 'FALSE', 'NONE'}

    # Remove output variables from input variables (can't be missing if we're creating them)
    input_variables = input_variables - output_variables - numpy_functions - python_keywords

    print(f"🔄 Validation: Final output variables: {output_variables}")
    print(f"🔄 Validation: Final input variables to check: {input_variables}")

    # Get common logs across all wells
    common_logs = set(las_files[0].curves.keys())
    for las in las_files[1:]:
        common_logs.intersection_update(las.curves.keys())

    # Check which INPUT variables are missing in each well
    missing_logs = {}
    all_missing = set()

    for las in las_files:
        well_name = las.well.WELL.value
        well_curves = set(las.curves.keys())
        missing_in_well = input_variables - well_curves
        if missing_in_well:
            missing_logs[well_name] = list(missing_in_well)
            all_missing.update(missing_in_well)

    is_valid = len(all_missing) == 0

    if not is_valid:
        error_details = "CALCULATION ERROR: Missing Input Logs\n\n"
        error_details += "⚠️ WARNING: Your calculations reference INPUT logs that are NOT available in all wells.\n"
        error_details += "This will cause calculation failures in wells where these logs are missing.\n\n"
    else:
        error_details = ""

    result = {
        'valid': is_valid,
        'missing_logs': missing_logs,
        'available_logs': list(common_logs),
        'error_details': error_details
    }
    print("🔄 Validation: validate_calculation_inputs() completed successfully")
    return result

def create_mock_las_file(well_name, curves):
    """Create a mock LAS file with specified curves."""
    mock_las = Mock()
    mock_las.well.WELL.value = well_name
    mock_las.curves.keys.return_value = curves
    return mock_las

def test_kdks_calculation_validation():
    """Test that KdKs calculation with np.clip passes validation."""
    print("🧪 Testing KdKs calculation validation...")
    
    # Create mock LAS files with KDRY and KSOLID curves
    mock_las1 = create_mock_las_file('Well1', ['DEPTH', 'KDRY', 'KSOLID', 'GR'])
    mock_las2 = create_mock_las_file('Well2', ['DEPTH', 'KDRY', 'KSOLID', 'GR'])
    
    las_files = [mock_las1, mock_las2]
    
    # Test the problematic calculation that should now work
    calculation_text = """
# Calculate KdKs ratio
KdKs = KDRY / KSOLID

# Clip negative values (this was causing the validation error)
KdKs_CLIPPED = np.clip(KdKs, 0, None)

# Additional processing
KdKs_LOG = np.log(KdKs_CLIPPED + 1e-6)
"""
    
    print(f"📝 Testing calculation:\n{calculation_text}")
    
    # Run validation
    result = validate_calculation_inputs(las_files, calculation_text)
    
    # Check results
    print(f"\n✅ Validation result: {result['valid']}")
    
    if result['valid']:
        print("🎉 SUCCESS: KdKs calculation with np.clip passed validation!")
        print(f"📊 Available logs: {result['available_logs']}")
        return True
    else:
        print("❌ FAILED: Validation still failing")
        print(f"🔍 Error details:\n{result['error_details']}")
        print(f"❌ Missing logs: {result['missing_logs']}")
        return False

def test_clip_function_recognition():
    """Test that CLIP is properly recognized as a numpy function."""
    print("\n🧪 Testing CLIP function recognition...")
    
    # Create mock LAS files
    mock_las1 = create_mock_las_file('Well1', ['DEPTH', 'DATA'])
    mock_las2 = create_mock_las_file('Well2', ['DEPTH', 'DATA'])
    
    las_files = [mock_las1, mock_las2]
    
    # Test calculation that only uses np.clip (no input variables)
    calculation_text = """
# Test np.clip with constants
CLIPPED_DATA = np.clip(DATA, 0, 100)
"""
    
    print(f"📝 Testing calculation:\n{calculation_text}")
    
    result = validate_calculation_inputs(las_files, calculation_text)
    
    print(f"✅ Validation result: {result['valid']}")
    
    if result['valid']:
        print("🎉 SUCCESS: np.clip is properly recognized as a function!")
        return True
    else:
        print("❌ FAILED: np.clip still being treated as input variable")
        print(f"🔍 Error details:\n{result['error_details']}")
        return False

def test_missing_input_logs():
    """Test that validation still catches actual missing input logs."""
    print("\n🧪 Testing missing input log detection...")
    
    # Create mock LAS files with different curves (missing KSOLID in Well2)
    mock_las1 = create_mock_las_file('Well1', ['DEPTH', 'KDRY', 'KSOLID'])
    mock_las2 = create_mock_las_file('Well2', ['DEPTH', 'KDRY'])  # Missing KSOLID
    
    las_files = [mock_las1, mock_las2]
    
    # Test calculation that should fail due to missing KSOLID
    calculation_text = """
KdKs = KDRY / KSOLID
KdKs_CLIPPED = np.clip(KdKs, 0, None)
"""
    
    print(f"📝 Testing calculation:\n{calculation_text}")
    
    result = validate_calculation_inputs(las_files, calculation_text)
    
    print(f"✅ Validation result: {result['valid']}")
    
    if not result['valid']:
        print("🎉 SUCCESS: Validation correctly detected missing KSOLID log!")
        print(f"❌ Missing logs: {result['missing_logs']}")
        return True
    else:
        print("❌ FAILED: Validation should have detected missing KSOLID")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting KdKs validation tests...\n")
    
    tests = [
        test_kdks_calculation_validation,
        test_clip_function_recognition,
        test_missing_input_logs
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print("-" * 60)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print("-" * 60)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fix is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
