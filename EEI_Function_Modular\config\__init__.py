# -*- coding: utf-8 -*-
"""
Configuration Package for EEI Function Modular

This package provides centralized configuration management for all
EEI Function Modular operations, including base settings, EEI-specific
parameters, visualization settings, and UI configuration.

Author: Integration of EEI_XCorr_Modular and EEI_Xplot_Modular
Created: 2025
Version: 1.0.0
"""

from .base_config import BaseConfig, LOG_KEYWORDS, FILE_FORMATS
from .eei_config import EEIConfig, ANALYSIS_PARAMS, VALIDATION_PARAMS, DEFAULT_PARAMS
from .visualization_config import VisualizationConfig, COLORMAP_CATEGORIES
from .ui_config import UIConfig, UI_CONFIG

__all__ = [
    'BaseConfig',
    'EEIConfig',
    'VisualizationConfig',
    'UIConfig',
    'LOG_KEYWORDS',
    'FILE_FORMATS',
    'ANALYSIS_PARAMS',
    'VALIDATION_PARAMS',
    'DEFAULT_PARAMS',
    'COLORMAP_CATEGORIES',
    'UI_CONFIG',
]
