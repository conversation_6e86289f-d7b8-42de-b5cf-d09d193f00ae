"""Configuration constants and utilities for Xplot.

This module stores dictionaries for log detection and
available colormap categories as well as helper functions
for working with colormaps.
"""

from typing import Dict, List, Optional

import matplotlib.pyplot as plt

# Keywords used to detect common logs in LAS files
log_keywords: Dict[str, List[str]] = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC', 'AC', 'DT4P'],
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC', 'DT4S', 'SHEAR_DT'],
    'PHIT': ['PHIT', 'PHID', 'PHI_D', 'POR', 'TOTAL_POR'],
    'PHIE': ['PHIE', 'PHIE_D', 'EFF_POR', 'EFFECTIVE_POR'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO', 'BULK_DEN'],
    'SWT': ['SWT', 'SW', 'WATER_SAT', 'SAT_WATER'],
    'SWE': ['SWE', 'SWE_D', 'SW_EFF'],
    'DEPTH': ['DEPTH', 'DEPT', 'MD', 'MEASURED_DEPTH'],
    'P-WAVE': ['P-WAVE', 'P_VELOCITY', 'VP', 'VELP'],
    'S-WAVE': ['S-WAVE', 'S_VELOCITY', 'VS', 'VELS'],
    'FLUID_CODE': ['FLUID_CODE', 'FLUID', 'FLUID_ID'],
    'FLUID_PETREL': ['FLUID_PETREL'],
    'LITHO_CODE': ['LITHO_CODE', 'LITHOLOGY', 'LITHO_PETREL', 'LITH'],
    'GR': ['GR', 'GAMMA_RAY', 'GR_LOG', 'CGR', 'SGR'],
    'NPHI': ['NPHI', 'NEUTRON', 'NEUTRON_POROSITY', 'NPOR'],
    'VCL': ['VCL', 'VOL_WETCLAY', 'V_CLAY', 'VSH', 'SHALE_VOL'],
    'RT': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD', 'AT90', 'DEEP_RES'],
    'CALI': ['CALI', 'CAL', 'CALIPER'],
    'PEF': ['PEF', 'PE', 'PHOTOELECTRIC'],
    'SP': ['SP', 'SPONTANEOUS_POTENTIAL'],
    'DRHO': ['DRHO', 'DENSITY_CORRECTION', 'DELTARHO'],
}

# Categories of colormaps that may be chosen for Z-axis visualisation
from typing import Union

COLORMAP_CATEGORIES: Dict[str, Union[Dict[str, List[str]], List[str]]] = {
    'Sequential': {
        'Perceptually Uniform': ['viridis', 'plasma', 'inferno', 'magma', 'cividis'],
        'Single Hue': [
            'Blues', 'BuGn', 'BuPu', 'GnBu', 'Greens', 'Greys', 'Oranges',
            'OrRd', 'PuBu', 'PuBuGn', 'PuRd', 'Purples', 'RdPu', 'Reds',
            'YlGn', 'YlGnBu', 'YlOrBr', 'YlOrRd'
        ],
        'Multi Hue': [
            'rainbow', 'turbo', 'jet', 'hsv', 'gist_rainbow', 'gist_ncar',
            'nipy_spectral', 'CMRmap'
        ],
    },
    'Diverging': [
        'RdYlBu', 'RdBu', 'coolwarm', 'seismic', 'bwr', 'RdGy', 'PiYG',
        'PRGn', 'BrBG', 'RdYlGn', 'Spectral'
    ],
    'Qualitative': [
        'Set1', 'Set2', 'Set3', 'tab10', 'tab20', 'tab20b', 'tab20c',
        'Pastel1', 'Pastel2', 'Paired', 'Accent', 'Dark2'
    ],
}


def get_colormap_options(category: str, subcategory: Optional[str] = None) -> List[str]:
    """Return available colormaps for a category and optional subcategory."""
    if category not in COLORMAP_CATEGORIES:
        return ['viridis']

    if category == 'Sequential' and subcategory:
        return COLORMAP_CATEGORIES[category].get(subcategory, ['viridis'])
    if category == 'Sequential':
        all_maps: List[str] = []
        for maps in COLORMAP_CATEGORIES[category].values():
            all_maps.extend(maps)
        return all_maps
    return COLORMAP_CATEGORIES[category]  # type: ignore[return-value]


def get_sequential_subcategories() -> List[str]:
    """Return the list of subcategories for sequential colormaps."""
    return list(COLORMAP_CATEGORIES['Sequential'].keys())


def validate_colormap(colormap_name: str) -> bool:
    """Return ``True`` if *colormap_name* exists in Matplotlib."""
    try:
        plt.colormaps[colormap_name]
        return True
    except KeyError:
        return False


def apply_colormap_reversal(colormap_name: str, reverse: bool = False) -> str:
    """Return the colormap name with reversal applied if requested."""
    if reverse and not colormap_name.endswith('_r'):
        return f"{colormap_name}_r"
    if not reverse and colormap_name.endswith('_r'):
        return colormap_name[:-2]
    return colormap_name
