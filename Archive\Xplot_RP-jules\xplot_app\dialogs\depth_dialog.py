import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Optional, Tuple, Any
import numpy as np
import pandas as pd # Required for preloaded_excel_df type hinting

# Adjust import paths as necessary after full refactoring
from config import log_keywords
from processing import find_default_columns
from data_io import load_boundaries_from_excel, filter_excel_data_for_las_wells
from gui_utils import create_scrollable_frame # Added import
from .batch_boundaries_dialog import BatchBoundariesDialog


class DepthRangeDialog(tk.Toplevel):
    """
    Dialog for setting depth ranges for wells, either manually or from an Excel file.
    """

    def __init__(self, parent: tk.Widget, las_files: List[Any], 
                 preloaded_excel_df: Optional[pd.DataFrame] = None) -> None:
        super().__init__(parent)
        self.title("Set Depth Ranges for Wells")
        self.geometry("650x550")

        self.las_files = las_files
        self.preloaded_excel_df = preloaded_excel_df
        self.result_depth_ranges: Dict[str, Tuple[float, float]] = {}
        self.default_depth_values_for_fallback: Dict[str, Tuple[float, float]] = {}

        self._calculate_default_depth_fallbacks()
        self._build_ui()
        
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
        # self.wait_window(self) # Made non-blocking until get_selected_depth_ranges

    def _calculate_default_depth_fallbacks(self) -> None:
        """Calculates default depth fallbacks for each well based on LAS data."""
        for las in self.las_files:
            well_name = las.well.WELL.value if las.well.WELL else "UnknownWell"
            current_well_cols_for_depth = find_default_columns(las, log_keywords)
            depth_mnemonic = current_well_cols_for_depth.get('DEPTH')
            dt_mnemonic = current_well_cols_for_depth.get('DT') 

            default_top: float = 0.0
            default_bottom: float = 0.0

            if depth_mnemonic and depth_mnemonic in las.curves:
                depth_data = np.array(las[depth_mnemonic].data)
                valid_depth_data = depth_data[~np.isnan(depth_data)]

                if valid_depth_data.size > 0:
                    default_top = np.min(valid_depth_data)
                    default_bottom = np.max(valid_depth_data)

                    if dt_mnemonic and dt_mnemonic in las.curves:
                        dt_data = np.array(las[dt_mnemonic].data)
                        if len(depth_data) == len(dt_data):
                            valid_dt_mask = ~np.isnan(dt_data) & ~np.isnan(depth_data)
                            depths_with_valid_dt = depth_data[valid_dt_mask]
                            if depths_with_valid_dt.size > 0:
                                default_top = np.min(depths_with_valid_dt)
                                default_bottom = np.max(depths_with_valid_dt)
                        else:
                            print(f"Warning: DEPTH and {dt_mnemonic} length mismatch for well {well_name}. Using DEPTH range only.")
            self.default_depth_values_for_fallback[well_name] = (default_top, default_bottom)

    def _build_ui(self) -> None:
        """Builds the main UI components of the dialog."""
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        method_frame = ttk.LabelFrame(main_frame, text="Select Method for Defining Boundaries", padding="10")
        method_frame.pack(fill=tk.X, padx=5, pady=5)

        self.method_var = tk.StringVar(value="excel" if self.preloaded_excel_df is not None else "manual")
        
        ttk.Radiobutton(method_frame, text="Manual Input", variable=self.method_var,
                        value="manual", command=self._on_radio_click).pack(anchor=tk.W, pady=2)
        ttk.Radiobutton(method_frame, text="Import from Excel File", variable=self.method_var,
                        value="excel", command=self._on_radio_click).pack(anchor=tk.W, pady=2)

        self.content_frame = ttk.Frame(main_frame, padding="10")
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.manual_entries: List[Tuple[str, ttk.Entry, ttk.Entry]] = []
        self.excel_data_df: Optional[pd.DataFrame] = self.preloaded_excel_df
        self.excel_status_var = tk.StringVar()
        
        self.submit_btn = ttk.Button(main_frame, text="Submit", command=self._on_submit)
        self.submit_btn.pack(pady=10)
        self._on_radio_click()

    def _on_radio_click(self) -> None:
        method = self.method_var.get()
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        if method == "manual":
            self._create_manual_ui()
            self.submit_btn.config(state=tk.NORMAL)
        else:  # excel
            self._create_excel_ui()
            self.submit_btn.config(state=tk.NORMAL if self.excel_data_df is not None else tk.DISABLED)

    def _create_manual_ui(self) -> None:
        # Use the new utility function to create a scrollable frame
        scrollable_frame, _ = create_scrollable_frame(self.content_frame)

        ttk.Label(scrollable_frame, text="Well Name", font=("", 10, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        ttk.Label(scrollable_frame, text="Top Depth", font=("", 10, "bold")).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Label(scrollable_frame, text="Bottom Depth", font=("", 10, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)

        self.manual_entries.clear()
        for i, las in enumerate(self.las_files, start=1):
            well_name = las.well.WELL.value if las.well.WELL else f"UnknownWell_{i}"
            default_top, default_bottom = self.default_depth_values_for_fallback.get(well_name, (0.0, 0.0))
            
            ttk.Label(scrollable_frame, text=well_name).grid(row=i, column=0, padx=5, pady=2, sticky=tk.W)
            top_entry = ttk.Entry(scrollable_frame, width=10)
            top_entry.insert(0, f"{default_top:.2f}")
            top_entry.grid(row=i, column=1, padx=5, pady=2)
            bottom_entry = ttk.Entry(scrollable_frame, width=10)
            bottom_entry.insert(0, f"{default_bottom:.2f}")
            bottom_entry.grid(row=i, column=2, padx=5, pady=2)
            self.manual_entries.append((well_name, top_entry, bottom_entry))

    def _create_excel_ui(self) -> None:
        excel_frame = ttk.Frame(self.content_frame)
        excel_frame.pack(fill=tk.BOTH, expand=True)

        initial_status = "Click 'Load/Reload Excel' to import boundary data."
        if self.excel_data_df is not None:
            initial_status = f"Excel data loaded ({len(self.excel_data_df)} rows). Click 'Submit' or 'Load/Reload Excel'."
        
        self.excel_status_var.set(initial_status)
        status_label = ttk.Label(excel_frame, textvariable=self.excel_status_var, wraplength=500)
        status_label.pack(pady=10)

        buttons_frame = ttk.Frame(excel_frame)
        buttons_frame.pack(pady=5)
        
        self.load_excel_btn = ttk.Button(buttons_frame, text="Load/Reload Excel File", command=self._on_load_excel)
        self.load_excel_btn.pack(side=tk.LEFT, padx=5)
        
        preview_frame = ttk.LabelFrame(excel_frame, text="Data Preview (first 10 rows)")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        self.preview_text = tk.Text(preview_frame, height=8, wrap=tk.NONE)
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scrollbar_y = ttk.Scrollbar(preview_frame, orient="vertical", command=self.preview_text.yview)
        preview_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        self.preview_text.configure(yscrollcommand=preview_scrollbar_y.set)
        preview_scrollbar_x = ttk.Scrollbar(preview_frame, orient="horizontal", command=self.preview_text.xview)
        preview_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        self.preview_text.configure(xscrollcommand=preview_scrollbar_x.set)

        if self.excel_data_df is not None:
            self._update_excel_preview()

    def _on_load_excel(self) -> None:
        self.load_excel_btn.config(text="Loading...", state=tk.DISABLED)
        self.update_idletasks()

        # Pass self (the Toplevel dialog) as parent to ensure messageboxes are modal to this dialog
        df = load_boundaries_from_excel(title="Select Excel file with depth ranges") # Removed parent=self as it's not in data_io
        if df is not None:
            filtered_df = filter_excel_data_for_las_wells(df, self.las_files)
            if filtered_df is not None:
                self.excel_data_df = filtered_df
                self.excel_status_var.set(f"Excel loaded & filtered: {len(self.excel_data_df)} entries. Ready to Submit.")
                self._update_excel_preview()
                self.submit_btn.config(state=tk.NORMAL)
            else:
                self.excel_data_df = None
                self.excel_status_var.set("No matching wells in Excel. Load another or use Manual.")
                self._update_excel_preview()
                self.submit_btn.config(state=tk.DISABLED)
        else:
            self.excel_status_var.set("Excel loading cancelled or failed. Try again or use Manual.")
            self.submit_btn.config(state=tk.NORMAL if self.excel_data_df is not None else tk.DISABLED)
        self.load_excel_btn.config(text="Load/Reload Excel File", state=tk.NORMAL)
        
    def _update_excel_preview(self) -> None:
        self.preview_text.delete(1.0, tk.END)
        if self.excel_data_df is not None:
            self.preview_text.insert(tk.END, self.excel_data_df.head(10).to_string())
            self.preview_text.insert(tk.END, "\n\nNote: Data filtered for loaded LAS wells.")
        else:
            self.preview_text.insert(tk.END, "No Excel data loaded or no matching wells found.")

    def _on_submit(self) -> None:
        """Processes the selected depth ranges and closes the dialog."""
        method = self.method_var.get()
        temp_results: Dict[str, Tuple[float, float]] = {}
        las_well_names = [las.well.WELL.value for las in self.las_files if las.well.WELL and las.well.WELL.value]

        if method == "manual":
            for well_name, top_entry, bottom_entry in self.manual_entries:
                try:
                    top_depth = float(top_entry.get())
                    bottom_depth = float(bottom_entry.get())
                    if bottom_depth < top_depth: 
                        messagebox.showerror("Input Error", f"Bottom depth ({bottom_depth}) must be greater than or equal to top depth ({top_depth}) for well {well_name}.", parent=self)
                        return
                    temp_results[well_name] = (top_depth, bottom_depth)
                except ValueError:
                    messagebox.showerror("Input Error", f"Invalid depth value for well {well_name}. Please enter numbers.", parent=self)
                    return
        
        elif method == "excel":
            if self.excel_data_df is None or self.excel_data_df.empty:
                messagebox.showerror("No Data", "Please load a valid Excel file with matching wells first.", parent=self)
                return

            batch_dialog = BatchBoundariesDialog(self, self.excel_data_df, las_well_names)
            # self.wait_window(batch_dialog) # wait_window is called in get_selected_boundaries
            
            selected_boundaries = batch_dialog.get_selected_boundaries()

            if selected_boundaries:
                temp_results = selected_boundaries
                for well_name in las_well_names:
                    if well_name not in temp_results:
                        if well_name in self.default_depth_values_for_fallback:
                            temp_results[well_name] = self.default_depth_values_for_fallback[well_name]
                            print(f"Info: Using default depth range for well {well_name} as it was not in batch selection results.")
                        else: 
                            temp_results[well_name] = (0.0,0.0) 
            else:
                messagebox.showwarning("Batch Selection Skipped", 
                                       "Batch boundary selection was cancelled or yielded no results. Applying default depth ranges for all wells.", 
                                       parent=self)
                for well_name in las_well_names:
                    temp_results[well_name] = self.default_depth_values_for_fallback.get(well_name, (0.0, 0.0))

        self.result_depth_ranges = temp_results
        if not self.result_depth_ranges and self.las_files:
             messagebox.showwarning("No Ranges Set", "No depth ranges were successfully set for any well.", parent=self)
             return 
        
        self.destroy()

    def _on_cancel(self) -> None:
        self.result_depth_ranges = {} 
        self.destroy()

    def get_selected_depth_ranges(self) -> Dict[str, Tuple[float, float]]:
        # self.wait_window(self) # Ensures dialog is closed before returning.
        return self.result_depth_ranges

if __name__ == '__main__':
    class MockLASFile:
        def __init__(self, name, curves_mnem=None, data_dict=None):
            self.well = Mock()
            self.well.WELL.value = name
            self.curves = []
            if curves_mnem is None: curves_mnem = []
            if data_dict is None: data_dict = {}

            for mnem in curves_mnem:
                curve_item = Mock()
                curve_item.mnemonic = mnem
                curve_item.data = data_dict.get(mnem, np.array([]))
                self.curves.append(curve_item)
            
            if 'DEPTH' not in curves_mnem and 'DEPTH' not in data_dict: 
                 depth_curve_mock = Mock(mnemonic='DEPTH')
                 depth_curve_mock.data = np.array([100, 200, 300, 400, 500]) 
                 self.curves.append(depth_curve_mock)
            elif 'DEPTH' in data_dict and 'DEPTH' not in curves_mnem: 
                depth_curve_mock = Mock(mnemonic='DEPTH')
                depth_curve_mock.data = data_dict['DEPTH']
                self.curves.append(depth_curve_mock)

        def __getitem__(self, key): 
            for curve in self.curves:
                if curve.mnemonic == key:
                    return curve
            raise KeyError(f"Curve {key} not found")
        
        def __contains__(self, key: str) -> bool: 
            return any(curve.mnemonic == key for curve in self.curves)

    root_app = tk.Tk()
    root_app.title("Main App Window (Test)")
    root_app.withdraw() 
    
    mock_las_data_A = {
        'DEPTH': np.array([1000, 1100, 1200, 1300, 1400, 1500]),
        'GR': np.array([10, 20, 15, 25, 30, 35]),
        'DT': np.array([60, 65, 62, 68, 70, 72]),
        'RHOB': np.array([2.1, 2.2, 2.15, 2.25, 2.3, 2.35])
    }
    mock_las_data_B = {
        'DEPTH': np.array([2000, 2100, 2200, 2300, 2400, 2500]),
        'GR': np.array([15, 25, 20, 30, 35, 40]),
        'NPHI': np.array([0.3, 0.25, 0.28, 0.22, 0.2, 0.18]),
        'RHOB': np.array([2.2, 2.3, 2.25, 2.35, 2.4, 2.45])
    }

    mock_las_files = [
        MockLASFile("Well-A", curves_mnem=list(mock_las_data_A.keys()), data_dict=mock_las_data_A),
        MockLASFile("Well-B", curves_mnem=list(mock_las_data_B.keys()), data_dict=mock_las_data_B)
    ]
        
    print("Testing DepthRangeDialog without preloaded Excel...")
    dialog_no_excel = DepthRangeDialog(root_app, mock_las_files)
    print("Selected depths (no preloaded Excel):", dialog_no_excel.get_selected_depth_ranges())

    print("\nTesting DepthRangeDialog WITH preloaded Excel...")
    excel_content = {'Well': ['Well-A', 'Well-A', 'Well-B', 'Well-B', 'Well-C', 'Well-C'],
                     'Surface': ['TopSandA', 'BaseSandA', 'TopShaleB', 'BaseShaleB', 'TopC', 'BaseC'],
                     'MD': [1050.0, 1250.0, 2050.0, 2250.0, 3000.0, 3100.0]}
    mock_excel_df = pd.DataFrame(excel_content)
        
    dialog_with_excel = DepthRangeDialog(root_app, mock_las_files, preloaded_excel_df=mock_excel_df)
    print("Selected depths (with preloaded Excel):", dialog_with_excel.get_selected_depth_ranges())
    
    if not root_app.winfo_viewable():
         root_app.destroy()
