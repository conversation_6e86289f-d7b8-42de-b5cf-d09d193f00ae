# -*- coding: utf-8 -*-
"""
Core Data Processing Module

This module contains general data processing utilities for the EEI Function Modular framework.
It consolidates the best implementations from both EEI_XCorr_Modular and EEI_Xplot_Modular.

Functions include:
- Data merging and interpolation
- Robust limit calculation
- General data processing utilities

Author: EEI Function Modular Integration
Created: 2025-10-08
Version: 1.0.0
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Union, Any
from scipy import interpolate
from dataclasses import dataclass

# Configure module-specific logger
logger = logging.getLogger(__name__)


@dataclass
class MergedData:
    """
    Container for merged well data with metadata.
    
    Attributes:
        depth: Merged depth array
        data_arrays: Dictionary of merged data arrays by name
        metadata: Dictionary containing merge statistics and information
    """
    depth: np.ndarray
    data_arrays: Dict[str, np.ndarray]
    metadata: Dict[str, Any]


@dataclass
class ProcessingResult:
    """
    Container for processing operation results.
    
    Attributes:
        success: Whether the operation was successful
        data: Processed data (type varies by operation)
        messages: List of informational messages
        warnings: List of warning messages
        errors: List of error messages
    """
    success: bool
    data: Any
    messages: List[str]
    warnings: List[str]
    errors: List[str]


class DataProcessor:
    """
    General data processing operations for well log data.
    
    This class provides stateless methods for common data processing tasks
    including merging, interpolation, and validation.
    """

    @staticmethod
    def merge_well_data(
        wells_data: Dict[str, Dict[str, np.ndarray]],
        depth_ranges: Optional[Dict[str, Tuple[float, float]]] = None
    ) -> MergedData:
        """
        Merge data from multiple wells into unified arrays.
        
        This function combines the best features from both XCorr and Xplot modules,
        providing comprehensive error handling and detailed metadata.
        
        Args:
            wells_data: Dictionary with well names as keys and data dictionaries as values.
                       Each data dict should contain 'depth' and other log arrays.
            depth_ranges: Optional depth ranges for each well (top, bottom)
            
        Returns:
            MergedData object containing merged arrays and metadata
            
        Raises:
            ValueError: If input data is invalid or incompatible
        """
        try:
            if not wells_data:
                raise ValueError("No well data provided for merging")
            
            # Initialize containers
            merged_depth = []
            merged_arrays = {}
            merge_stats = {
                'wells_processed': 0,
                'wells_skipped': [],
                'total_points': 0,
                'points_per_well': {},
                'depth_range_applied': depth_ranges is not None,
                'common_logs': set(),
                'processing_notes': []
            }
            
            # Determine common log names across all wells
            all_log_names = set()
            for well_name, well_data in wells_data.items():
                if 'depth' not in well_data:
                    logger.warning(f"Well {well_name} missing depth data, skipping")
                    merge_stats['wells_skipped'].append(well_name)
                    continue
                all_log_names.update(well_data.keys())
            
            # Remove 'depth' from log names as it's handled separately
            all_log_names.discard('depth')
            
            # Initialize merged arrays for all logs
            for log_name in all_log_names:
                merged_arrays[log_name] = []
            
            # Process each well
            for well_name, well_data in wells_data.items():
                if well_name in merge_stats['wells_skipped']:
                    continue
                    
                try:
                    depth = well_data['depth']
                    
                    # Validate depth array
                    if not isinstance(depth, np.ndarray) or depth.size == 0:
                        logger.warning(f"Invalid depth data for well {well_name}, skipping")
                        merge_stats['wells_skipped'].append(well_name)
                        continue
                    
                    # Apply depth range if specified
                    if depth_ranges and well_name in depth_ranges:
                        top_depth, bottom_depth = depth_ranges[well_name]
                        depth_mask = (depth >= top_depth) & (depth <= bottom_depth)
                        
                        if not np.any(depth_mask):
                            logger.warning(f"No data in depth range for well {well_name}, skipping")
                            merge_stats['wells_skipped'].append(well_name)
                            continue
                            
                        depth = depth[depth_mask]
                        merge_stats['processing_notes'].append(
                            f"Applied depth range {top_depth}-{bottom_depth} to well {well_name}"
                        )
                    else:
                        depth_mask = np.ones(len(depth), dtype=bool)
                    
                    # Process each log for this well
                    well_logs_added = []
                    points_added = len(depth)
                    
                    for log_name in all_log_names:
                        if log_name in well_data:
                            log_data = well_data[log_name]
                            
                            # Validate log data
                            if not isinstance(log_data, np.ndarray):
                                log_data = np.array(log_data)
                            
                            if log_data.shape != well_data['depth'].shape:
                                logger.warning(f"Shape mismatch for {log_name} in well {well_name}, skipping log")
                                continue
                            
                            # Apply same depth mask
                            log_data = log_data[depth_mask]
                            merged_arrays[log_name].extend(log_data)
                            well_logs_added.append(log_name)
                        else:
                            # Fill with NaN for missing logs
                            merged_arrays[log_name].extend([np.nan] * points_added)
                    
                    # Add depth data
                    merged_depth.extend(depth)
                    
                    # Update statistics
                    merge_stats['wells_processed'] += 1
                    merge_stats['total_points'] += points_added
                    merge_stats['points_per_well'][well_name] = {
                        'points': points_added,
                        'logs_available': well_logs_added
                    }
                    merge_stats['common_logs'].update(well_logs_added)
                    
                    logger.info(f"Merged {points_added} points from well {well_name}")
                    
                except Exception as e:
                    logger.error(f"Error processing well {well_name}: {str(e)}")
                    merge_stats['wells_skipped'].append(well_name)
                    continue
            
            # Validate merged results
            if not merged_depth:
                raise ValueError("No valid data could be merged from any well")
            
            # Convert lists to numpy arrays
            final_depth = np.array(merged_depth)
            final_arrays = {}
            
            for log_name, log_data in merged_arrays.items():
                if log_data:  # Only include logs that have data
                    final_arrays[log_name] = np.array(log_data)
            
            # Update final statistics
            merge_stats['common_logs'] = list(merge_stats['common_logs'])
            merge_stats['final_array_shapes'] = {
                'depth': final_depth.shape,
                **{name: arr.shape for name, arr in final_arrays.items()}
            }
            
            logger.info(
                f"Data merge complete: {merge_stats['wells_processed']} wells, "
                f"{merge_stats['total_points']} total points, "
                f"{len(final_arrays)} log types"
            )
            
            return MergedData(
                depth=final_depth,
                data_arrays=final_arrays,
                metadata=merge_stats
            )
            
        except Exception as e:
            logger.error(f"Data merge failed: {str(e)}")
            raise ValueError(f"Data merge failed: {str(e)}") from e

    @staticmethod
    def interpolate_missing_values(
        depth: np.ndarray,
        data: np.ndarray,
        method: str = 'linear',
        fill_value: str = 'extrapolate'
    ) -> np.ndarray:
        """
        Interpolate missing (NaN) values in data array.

        Combines the best features from both modules:
        - XCorr: Robust error handling
        - Xplot: Clean implementation with scipy.interpolate

        Args:
            depth: Depth array corresponding to data
            data: Data array with potential NaN values
            method: Interpolation method ('linear', 'nearest', 'cubic', etc.)
            fill_value: How to handle extrapolation ('extrapolate' or scalar)

        Returns:
            Data array with interpolated values

        Raises:
            ValueError: If interpolation cannot be performed
        """
        try:
            # Convert to numpy arrays
            depth = np.asarray(depth, dtype=float)
            data = np.asarray(data, dtype=float)

            # Validate inputs
            if depth.shape != data.shape:
                raise ValueError("Depth and data arrays must have the same shape")

            if depth.size == 0:
                return data

            # Find finite (non-NaN) values
            finite_mask = np.isfinite(data) & np.isfinite(depth)

            if np.sum(finite_mask) < 2:
                logger.warning("Less than 2 finite values available for interpolation")
                return data

            if np.all(finite_mask):
                # No interpolation needed
                return data

            # Extract finite values for interpolation
            depth_finite = depth[finite_mask]
            data_finite = data[finite_mask]

            # Create interpolation function
            f = interpolate.interp1d(
                depth_finite,
                data_finite,
                kind=method,
                fill_value=fill_value,
                bounds_error=False
            )

            # Interpolate all values
            interpolated_data = f(depth)

            # Preserve original finite values (in case of numerical precision issues)
            interpolated_data[finite_mask] = data[finite_mask]

            logger.debug(f"Interpolated {np.sum(~finite_mask)} missing values using {method} method")

            return interpolated_data

        except Exception as e:
            logger.error(f"Interpolation failed: {str(e)}")
            return data  # Return original data if interpolation fails

    @staticmethod
    def interpolate_categorical_data(
        depth: np.ndarray,
        class_data: np.ndarray
    ) -> np.ndarray:
        """
        Interpolate categorical/class data using nearest-neighbor approach.

        Based on Xplot's interpolate_class function with enhanced error handling.

        Args:
            depth: Depth array corresponding to class data
            class_data: Categorical data array with potential NaN values

        Returns:
            Class data array with interpolated values
        """
        try:
            # Convert to numpy arrays
            depth = np.asarray(depth, dtype=float)
            class_data = np.asarray(class_data, dtype=float)

            # Validate inputs
            if depth.shape != class_data.shape:
                raise ValueError("Depth and class data arrays must have the same shape")

            if depth.size == 0:
                return class_data

            # Find finite values
            finite_mask = np.isfinite(class_data) & np.isfinite(depth)

            if np.sum(finite_mask) < 1:
                logger.warning("No finite values available for categorical interpolation")
                return class_data

            if np.all(finite_mask):
                # No interpolation needed
                return class_data

            # Extract finite values
            depth_finite = depth[finite_mask]
            class_finite = class_data[finite_mask]

            # Use nearest-neighbor interpolation for categorical data
            f = interpolate.interp1d(
                depth_finite,
                class_finite,
                kind='nearest',
                fill_value='extrapolate',
                bounds_error=False
            )

            # Interpolate all values
            interpolated_class = f(depth)

            # Preserve original finite values
            interpolated_class[finite_mask] = class_data[finite_mask]

            logger.debug(f"Interpolated {np.sum(~finite_mask)} missing categorical values")

            return interpolated_class

        except Exception as e:
            logger.error(f"Categorical interpolation failed: {str(e)}")
            return class_data

    @staticmethod
    def get_robust_limits(
        data_array: np.ndarray,
        padding_percent: float = 5.0,
        min_padding: Optional[float] = None
    ) -> Tuple[Optional[float], Optional[float]]:
        """
        Calculate robust axis limits for plotting with padding.

        Based on Xplot's get_robust_limits with enhanced handling.

        Args:
            data_array: Input data array
            padding_percent: Percentage padding to add to data range
            min_padding: Minimum absolute padding value

        Returns:
            Tuple of (min_limit, max_limit) or (None, None) if no valid data
        """
        try:
            # Remove non-finite values
            finite_data = data_array[np.isfinite(data_array)]

            if len(finite_data) == 0:
                logger.warning("No finite values in data for limit calculation")
                return None, None

            # Handle single value case
            if len(np.unique(finite_data)) == 1:
                single_value = finite_data[0]
                delta = max(abs(single_value) * 0.01, 1.0)
                return single_value - delta, single_value + delta

            # Calculate data range
            data_min, data_max = np.min(finite_data), np.max(finite_data)
            data_range = data_max - data_min

            # Calculate padding
            padding = data_range * (padding_percent / 100)

            # Apply minimum padding if specified
            if min_padding is not None:
                padding = max(padding, min_padding)
            else:
                # Default minimum padding based on data magnitude
                min_default_padding = max(abs(data_min) * 0.01, abs(data_max) * 0.01, 0.1)
                padding = max(padding, min_default_padding)

            return data_min - padding, data_max + padding

        except Exception as e:
            logger.error(f"Robust limits calculation failed: {str(e)}")
            return None, None

    @staticmethod
    def find_nearest_index(array: np.ndarray, value: float) -> int:
        """
        Find the index of the nearest value in an array.

        From XCorr module with enhanced validation.

        Args:
            array: Input array to search
            value: Target value to find

        Returns:
            Index of nearest value

        Raises:
            ValueError: If array is empty or invalid
        """
        if not isinstance(array, np.ndarray) or array.size == 0:
            raise ValueError("Array must be a non-empty numpy array")

        return int(np.abs(array - value).argmin())

    @staticmethod
    def validate_data_for_plotting(
        wells_data: Dict[str, Dict[str, np.ndarray]],
        x_col: str,
        y_col: str,
        depth_ranges: Dict[str, Tuple[float, float]],
        class_col: Optional[str] = None,
        z_col: Optional[str] = None,
        min_valid_points: int = 1,
        min_unique_values: int = 1
    ) -> ProcessingResult:
        """
        Validate data for plotting operations.

        Enhanced version of Xplot's validate_data_for_plotting with better structure.

        Args:
            wells_data: Dictionary of well data
            x_col: X-axis column name
            y_col: Y-axis column name
            depth_ranges: Depth ranges for each well
            class_col: Optional classification column name
            z_col: Optional Z-axis column name
            min_valid_points: Minimum required valid points
            min_unique_values: Minimum required unique values

        Returns:
            ProcessingResult with validation results and statistics
        """
        messages = []
        warnings = []
        errors = []

        stats = {
            'total_points': 0,
            'valid_points': 0,
            'x_unique_values': 0,
            'y_unique_values': 0,
            'class_unique_values': None,
            'z_unique_values': None,
            'wells_with_data': [],
            'wells_without_data': []
        }

        all_x_data = []
        all_y_data = []
        all_class_data = []
        all_z_data = []

        try:
            for well_name, well_data in wells_data.items():
                # Check if well has depth range specified
                if well_name not in depth_ranges:
                    warnings.append(f"Depth range not specified for well {well_name}")
                    stats['wells_without_data'].append(well_name)
                    continue

                # Check required columns
                missing_curves = []
                if 'depth' not in well_data:
                    missing_curves.append('depth')
                if x_col not in well_data:
                    missing_curves.append(x_col)
                if y_col not in well_data:
                    missing_curves.append(y_col)
                if z_col and z_col not in well_data:
                    missing_curves.append(z_col)

                if missing_curves:
                    warnings.append(f"Well {well_name} missing required curves: {', '.join(missing_curves)}")
                    stats['wells_without_data'].append(well_name)
                    continue

                # Extract data within depth range
                top_depth, bottom_depth = depth_ranges[well_name]
                depth = well_data['depth']
                mask = (depth >= top_depth) & (depth <= bottom_depth)

                x_data = well_data[x_col][mask]
                y_data = well_data[y_col][mask]
                class_data = well_data[class_col][mask] if class_col else np.array([])
                z_data = well_data[z_col][mask] if z_col else np.array([])

                # Create validity mask
                valid_mask = np.isfinite(x_data) & np.isfinite(y_data)
                if class_col:
                    valid_mask &= np.isfinite(class_data)
                if z_col:
                    valid_mask &= np.isfinite(z_data)

                # Apply validity mask
                x_data = x_data[valid_mask]
                y_data = y_data[valid_mask]
                class_data = class_data[valid_mask] if class_col else class_data
                z_data = z_data[valid_mask] if z_col else z_data

                # Check minimum data requirements
                if len(x_data) < min_valid_points or len(y_data) < min_valid_points:
                    warnings.append(f"Not enough valid data in well {well_name} ({len(x_data)} points)")
                    stats['wells_without_data'].append(well_name)
                    continue

                # Well has valid data
                stats['wells_with_data'].append(well_name)

                # Accumulate data
                all_x_data.extend(x_data)
                all_y_data.extend(y_data)
                if class_col:
                    all_class_data.extend(class_data)
                if z_col:
                    all_z_data.extend(z_data)

                messages.append(f"Well {well_name}: {len(x_data)} valid points")

            # Calculate overall statistics
            all_x_np = np.array(all_x_data)
            all_y_np = np.array(all_y_data)

            stats['total_points'] = len(all_x_np)
            stats['valid_points'] = len(all_x_np)
            stats['x_unique_values'] = len(np.unique(all_x_np)) if len(all_x_np) > 0 else 0
            stats['y_unique_values'] = len(np.unique(all_y_np)) if len(all_y_np) > 0 else 0

            if class_col and all_class_data:
                all_class_np = np.array(all_class_data)
                stats['class_unique_values'] = len(np.unique(all_class_np))

            if z_col and all_z_data:
                all_z_np = np.array(all_z_data)
                stats['z_unique_values'] = len(np.unique(all_z_np))

            # Validate overall requirements
            success = True

            if len(stats['wells_with_data']) == 0:
                errors.append("No wells contain valid data for the selection")
                success = False

            if (stats['x_unique_values'] < min_unique_values or
                stats['y_unique_values'] < min_unique_values):
                errors.append("Insufficient variation in data for plotting")
                success = False

            return ProcessingResult(
                success=success,
                data=stats,
                messages=messages,
                warnings=warnings,
                errors=errors
            )

        except Exception as e:
            logger.error(f"Data validation failed: {str(e)}")
            return ProcessingResult(
                success=False,
                data=stats,
                messages=messages,
                warnings=warnings,
                errors=[f"Validation failed: {str(e)}"]
            )


# Convenience functions for backward compatibility
def merge_well_data(wells_data, depth_ranges=None):
    """Convenience function for DataProcessor.merge_well_data()"""
    return DataProcessor.merge_well_data(wells_data, depth_ranges)


def interpolate_nan(depth, data):
    """Convenience function for DataProcessor.interpolate_missing_values()"""
    return DataProcessor.interpolate_missing_values(depth, data, method='linear')


def interpolate_class(depth, class_data):
    """Convenience function for DataProcessor.interpolate_categorical_data()"""
    return DataProcessor.interpolate_categorical_data(depth, class_data)


def get_robust_limits(data_array, padding_percent=5):
    """Convenience function for DataProcessor.get_robust_limits()"""
    return DataProcessor.get_robust_limits(data_array, padding_percent)


def find_nearest_index(array, value):
    """Convenience function for DataProcessor.find_nearest_index()"""
    return DataProcessor.find_nearest_index(array, value)
