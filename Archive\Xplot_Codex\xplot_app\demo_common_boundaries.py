"""Demo script showing how to use the new common boundaries feature."""

import tkinter as tk
import pandas as pd
from dialogs.common_boundaries_dialog import select_common_boundaries_for_all_wells


def create_demo_data():
    """Create realistic demo data for the common boundaries feature."""
    print("Creating demo data...")
    
    # Create sample geological boundary data
    data = {
        'Well': [
            # Well A boundaries
            'B-G-6', 'B-G-6', 'B-G-6', 'B-G-6', 'B-G-6',
            # Well B boundaries  
            'B-G-10', 'B-G-10', 'B-G-10', 'B-G-10', 'B-G-10',
            # Well C boundaries
            'B-L-1', 'B-L-1', 'B-L-1', 'B-L-1', 'B-L-1',
            # Well D boundaries
            'B-L-2.G1', 'B-L-2.G1', 'B-L-2.G1', 'B-L-2.G1', 'B-L-2.G1'
        ],
        'Surface': [
            # Well A surfaces
            'E8', 'F19', 'F18-5', 'F17-3', 'F20-2',
            # Well B surfaces
            'E8', 'F19', 'F18-5', 'F17-3', 'F20-2', 
            # Well C surfaces
            'E8', 'F19-1', 'F24', 'F17-3', 'F20-4',
            # Well D surfaces
            'E8-1', 'F24', 'F17-14', 'F23-2', 'F20-4'
        ],
        'MD': [
            # Well A depths (matching the image data)
            560.51, 3759.85, 3585.80, 2625.35, 2986.37,
            # Well B depths
            553.28, 3379.25, 3604.87, 2593.15, 3495.25,
            # Well C depths  
            553.74, 617.89, 557.82, 550.66, 555.12,
            # Well D depths
            533.23, 533.18, 553.12, 3878.66, 2593.15
        ]
    }
    
    df = pd.DataFrame(data)
    print(f"Created demo data with {len(df)} boundary entries")
    print(f"Wells: {df['Well'].unique().tolist()}")
    print(f"Surfaces: {sorted(df['Surface'].unique().tolist())}")
    
    return df


def create_demo_las_wells():
    """Create demo LAS well names that match the Excel data."""
    return ['B-G-6', 'B-G-10', 'B-L-1', 'B-L-2.G1']


def demo_common_boundaries():
    """Demonstrate the common boundaries feature."""
    print("\n" + "="*60)
    print("COMMON BOUNDARIES FEATURE DEMO")
    print("="*60)
    
    # Create demo data
    df = create_demo_data()
    las_well_names = create_demo_las_wells()
    
    print(f"\nDemo setup:")
    print(f"- {len(las_well_names)} wells loaded: {las_well_names}")
    print(f"- {len(df)} boundary entries in Excel data")
    print(f"- {df['Surface'].nunique()} unique geological surfaces")
    
    print(f"\nAvailable surfaces for common boundaries:")
    for surface in sorted(df['Surface'].unique()):
        wells_with_surface = df[df['Surface'] == surface]['Well'].unique()
        print(f"  {surface}: available in {len(wells_with_surface)} wells ({list(wells_with_surface)})")
    
    print(f"\nLaunching Common Boundaries Dialog...")
    print(f"Instructions for demo:")
    print(f"1. Try checking 'Use common boundaries for all wells'")
    print(f"2. Select 'E8' as Common Top Boundary")
    print(f"3. Select 'F19' as Common Bottom Boundary (if available)")
    print(f"4. Click 'Apply to All Wells' to see the feature in action")
    print(f"5. Notice how the Status column updates to show 'Common'")
    print(f"6. You can also uncheck common mode and set individual boundaries")
    print(f"7. Click OK to complete the selection")
    
    try:
        # Launch the dialog
        result = select_common_boundaries_for_all_wells(df, las_well_names)
        
        if result:
            print(f"\n" + "="*60)
            print(f"DEMO RESULTS")
            print(f"="*60)
            print(f"Successfully selected boundaries for {len(result)} wells:")
            
            for well_name, (top_depth, bottom_depth) in result.items():
                depth_range = bottom_depth - top_depth
                print(f"  {well_name:12} | Top: {top_depth:8.2f} | Bottom: {bottom_depth:8.2f} | Range: {depth_range:8.2f}")
            
            # Analyze the results
            all_tops = [top for top, _ in result.values()]
            all_bottoms = [bottom for _, bottom in result.values()]
            
            print(f"\nBoundary Analysis:")
            print(f"  Top depths    - Min: {min(all_tops):8.2f}, Max: {max(all_tops):8.2f}")
            print(f"  Bottom depths - Min: {min(all_bottoms):8.2f}, Max: {max(all_bottoms):8.2f}")
            
            # Check if common boundaries were used
            unique_tops = len(set(all_tops))
            unique_bottoms = len(set(all_bottoms))
            
            if unique_tops == 1 and unique_bottoms == 1:
                print(f"  ✓ Common boundaries were successfully applied to all wells!")
            elif unique_tops == 1 or unique_bottoms == 1:
                print(f"  ⚠ Partial common boundaries detected")
            else:
                print(f"  ℹ Individual boundaries were used for each well")
                
        else:
            print(f"\nDemo was cancelled or no boundaries were selected.")
            
    except Exception as e:
        print(f"\nError during demo: {str(e)}")
        import traceback
        traceback.print_exc()


def show_feature_overview():
    """Show an overview of the common boundaries feature."""
    print("COMMON BOUNDARIES FEATURE OVERVIEW")
    print("="*60)
    
    print("""
The Common Boundaries feature allows you to:

1. COMMON MODE:
   ✓ Select a single top boundary that applies to all wells
   ✓ Select a single bottom boundary that applies to all wells  
   ✓ Apply these boundaries to all wells with one click
   ✓ Automatically validate that boundaries exist for all wells
   ✓ Get clear feedback about which wells succeeded/failed

2. INDIVIDUAL MODE:
   ✓ Set custom boundaries for each well individually
   ✓ Mix and match different geological surfaces per well
   ✓ Fine-tune boundaries based on well-specific geology

3. VALIDATION & ERROR HANDLING:
   ✓ Ensures selected surfaces exist for each well
   ✓ Validates that top depth < bottom depth
   ✓ Provides detailed error messages for any issues
   ✓ Option to continue with valid wells if some fail

4. STATUS INDICATORS:
   ✓ Manual (blue) - Individual boundary selection
   ✓ Common (green) - Applied via common boundaries
   ✓ Common Mode (orange) - Common mode active but not applied

5. INTEGRATION:
   ✓ Works with existing Excel boundary data
   ✓ Seamlessly integrates with current workflow
   ✓ Backward compatible with existing functionality
""")


def main():
    """Main demo function."""
    show_feature_overview()
    
    print("\nPress Enter to start the interactive demo...")
    input()
    
    demo_common_boundaries()
    
    print(f"\n" + "="*60)
    print("DEMO COMPLETED")
    print("="*60)
    print("The Common Boundaries feature is now ready for use in your application!")
    print("You can access it through:")
    print("1. Enhanced Depth Dialog -> 'Use Enhanced Boundary Selection'")
    print("2. Direct import: from dialogs.common_boundaries_dialog import select_common_boundaries_for_all_wells")


if __name__ == "__main__":
    main()
