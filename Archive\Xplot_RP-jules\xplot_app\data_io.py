"""Data input/output operations for XPlot App, including LAS and Excel file handling."""

import tkinter as tk
from tkinter import filedialog, messagebox
import lasio
import numpy as np # For np.sum, np.isnan
import pandas as pd
from typing import List, Optional, Any # For type hints

def load_multiple_las_files(parent: Optional[tk.Widget] = None) -> Optional[List[Any]]: # Using Any for lasio.LASFile
    """
    Open a dialog for the user to select multiple LAS files.
    Reads the selected files using lasio and returns a list of LASFile objects.
    Includes basic error handling and debug prints for the loading process.

    Args:
        parent: Optional parent widget for the dialog.

    Returns:
        A list of lasio.LASFile objects, or None if no files are selected or an error occurs.
    """
    # If no parent is provided, a new Tk root might be implicitly created by filedialog.
    # It's generally better if the main application creates the root window.
    # For now, this matches original behavior if parent is None.
    file_paths = filedialog.askopenfilenames(
        title="Select LAS files",
        filetypes=[("LAS files", "*.las")],
        parent=parent
    )

    if not file_paths:
        print("No files selected by user.")
        return None

    las_files: List[Any] = [] 
    print("\n=== DEBUG: LAS FILE LOADING ===")
    print(f"Number of files selected: {len(file_paths)}")

    for file_path in file_paths:
        try:
            las = lasio.read(file_path)
            print(f"Loaded file: {file_path}")
            print(f"  Well name: {las.well.WELL.value if las.well.WELL else 'N/A'}")
            print(f"  Curves available: {len(las.curves)}")
            if 'DEPTH' in las.curves:
                depth_data = las['DEPTH'].data
                if len(depth_data) > 0:
                    print(f"  Depth range: {min(depth_data)} to {max(depth_data)}")
                else:
                    print("  Depth curve is empty.")
            else:
                print("  DEPTH curve not found.")
            
            print(f"  Sample curves with non-NaN values:")
            for curve_name in list(las.curves.keys())[:5]:
                data = las[curve_name].data
                if len(data) > 0:
                    non_nan_count = np.sum(~np.isnan(data))
                    total_count = len(data)
                    print(f"    {curve_name}: {non_nan_count}/{total_count} non-NaN values ({non_nan_count/total_count*100:.1f}%)")
                else:
                    print(f"    {curve_name}: Empty data array.")
            las_files.append(las)
        except Exception as e:
            print(f"ERROR loading file {file_path}: {str(e)}")
            messagebox.showerror("File Load Error", f"Could not load {file_path}:\n{e}", parent=parent)

    if not las_files:
        print("No LAS files were successfully loaded.")
        messagebox.showwarning("Load Error", "No LAS files could be successfully loaded.", parent=parent)
        return None

    print(f"Successfully loaded {len(las_files)} LAS files")
    print("================================\n")
    return las_files

def load_boundaries_from_excel(
    title: str = "Select Excel file with boundary information",
    parent: Optional[tk.Widget] = None 
) -> Optional[pd.DataFrame]:
    """
    Load boundary information from an Excel file using a file dialog.

    Args:
        title: The title to display in the file dialog.
        parent: Optional parent widget for message boxes to ensure proper layering.

    Returns:
        A pandas DataFrame containing well names, surface names, and measured depths,
        or None if no file was selected or an error occurred.
    """
    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")],
        parent=parent 
    )

    if not file_path:
        print("No Excel file selected.")
        return None

    try:
        df: pd.DataFrame = pd.read_excel(file_path)

        required_columns: List[str] = ['Well', 'Surface', 'MD']
        missing_columns: List[str] = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            messagebox.showerror(
                "Missing Columns",
                f"The Excel file is missing the following required columns: {', '.join(missing_columns)}.\n"
                f"Please ensure the file contains columns named: {', '.join(required_columns)}",
                parent=parent 
            )
            return None

        if df.empty:
            messagebox.showerror("Empty File", "The Excel file contains no data.", parent=parent) 
            return None

        print(f"Successfully loaded boundary data from {file_path}")
        print(f"Found {len(df)} boundary entries for {df['Well'].nunique()} wells")
        return df

    except Exception as e:
        messagebox.showerror("Error Loading File", f"An error occurred while loading the Excel file:\n{str(e)}", parent=parent) 
        print(f"Error loading Excel file: {str(e)}")
        return None

def filter_excel_data_for_las_wells(df: Optional[pd.DataFrame], las_files: List[Any]) -> Optional[pd.DataFrame]: 
    """
    Filter Excel DataFrame to only include boundaries for wells present in the loaded LAS files.

    Args:
        df: DataFrame containing well names, surface names, and measured depths. 
            Can be None if no data was loaded.
        las_files: List of lasio.LASFile objects.

    Returns:
        A filtered pandas DataFrame containing only boundaries for wells found in the LAS files,
        or None if the input DataFrame is None or if no matching wells are found.
    """
    if df is None:
        return None

    las_well_names: List[str] = [las.well.WELL.value for las in las_files if las.well.WELL and las.well.WELL.value]

    filtered_df: pd.DataFrame = df[df['Well'].astype(str).isin(las_well_names)]

    if filtered_df.empty:
        print("Warning: No matching wells found in Excel file. Excel wells:",
              ", ".join(df['Well'].unique() if not df.empty else []))
        print("LAS file wells:", ", ".join(las_well_names))
        return None

    original_well_count: int = df['Well'].nunique()
    filtered_well_count: int = filtered_df['Well'].nunique()
    print(f"Filtered Excel data from {original_well_count} wells to {filtered_well_count} wells that match loaded LAS files")
    print(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")

    return filtered_df

def load_excel_depth_ranges(las_files: List[Any], parent: Optional[tk.Widget] = None) -> Optional[pd.DataFrame]: 
    """
    Prompts the user to load an Excel file containing depth ranges (well, surface, MD).
    Filters the loaded data to include only boundaries for wells present in the provided LAS files.

    Args:
        las_files: A list of lasio.LASFile objects.
        parent: Optional parent widget for dialogs.

    Returns:
        A pandas DataFrame containing filtered well names, surface names, and measured depths,
        or None if no file is selected, the user cancels, or an error occurs.
    """
    load_excel: bool = messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\n"
        "The file should have columns named 'Well', 'Surface', and 'MD'.",
        parent=parent
    )

    if not load_excel:
        print("User chose not to load Excel file with depth ranges at this time.")
        return None

    df: Optional[pd.DataFrame] = load_boundaries_from_excel(
        title="Select Excel file with depth ranges",
        parent=parent
    )

    if df is None:
        return None

    return filter_excel_data_for_las_wells(df, las_files)
