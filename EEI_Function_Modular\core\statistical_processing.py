# -*- coding: utf-8 -*-
"""
Core Statistical Processing Module

This module contains statistical calculation utilities for the EEI Function Modular framework.
It consolidates statistical functions from both EEI_XCorr_Modular and EEI_Xplot_Modular.

Functions include:
- Correlation calculations
- Array statistics
- Statistical validation
- Distribution analysis

Author: EEI Function Modular Integration
Created: 2025-10-08
Version: 1.0.0
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from scipy import stats

# Configure module-specific logger
logger = logging.getLogger(__name__)


@dataclass
class StatisticalResult:
    """
    Container for statistical calculation results.
    
    Attributes:
        value: Primary statistical value (correlation, mean, etc.)
        statistics: Dictionary of additional statistics
        metadata: Dictionary containing calculation metadata
        warnings: List of warning messages
        errors: List of error messages
    """
    value: Union[float, np.ndarray, Dict[str, Any]]
    statistics: Dict[str, Any]
    metadata: Dict[str, Any]
    warnings: List[str]
    errors: List[str]


class StatisticalProcessor:
    """
    Statistical processing operations for well log data.
    
    This class provides stateless methods for statistical calculations
    including correlations, descriptive statistics, and validation.
    """

    @staticmethod
    def calculate_correlation_safe(
        x: np.ndarray, 
        y: np.ndarray,
        method: str = 'pearson'
    ) -> StatisticalResult:
        """
        Calculate correlation coefficient with comprehensive error handling.
        
        Enhanced version of XCorr's calculate_correlation_safe with additional methods.
        
        Args:
            x: First data array
            y: Second data array
            method: Correlation method ('pearson', 'spearman', 'kendall')
            
        Returns:
            StatisticalResult containing correlation and metadata
        """
        warnings = []
        errors = []
        metadata = {
            'method': method,
            'original_size': 0,
            'valid_size': 0,
            'removed_points': 0
        }
        
        try:
            # Convert inputs to numpy arrays
            x = np.asarray(x, dtype=float)
            y = np.asarray(y, dtype=float)
            
            metadata['original_size'] = len(x)
            
            # Validate inputs
            if x.size == 0 or y.size == 0:
                errors.append("Empty arrays provided")
                return StatisticalResult(
                    value=np.nan, statistics={}, metadata=metadata, 
                    warnings=warnings, errors=errors
                )
            
            if x.shape != y.shape:
                errors.append("Arrays must have the same shape")
                return StatisticalResult(
                    value=np.nan, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )
            
            # Check for finite values
            if not np.isfinite(x).any() or not np.isfinite(y).any():
                warnings.append("Arrays contain no finite values")
                return StatisticalResult(
                    value=np.nan, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )
            
            # Create mask for valid (finite) values
            mask = np.isfinite(x) & np.isfinite(y)
            valid_count = np.sum(mask)
            
            metadata['valid_size'] = valid_count
            metadata['removed_points'] = metadata['original_size'] - valid_count
            
            if valid_count < 2:
                warnings.append("Less than 2 valid data points for correlation")
                return StatisticalResult(
                    value=np.nan, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )
            
            x_clean = x[mask]
            y_clean = y[mask]
            
            # Check for constant arrays (no variance)
            if np.all(x_clean == x_clean[0]) or np.all(y_clean == y_clean[0]):
                warnings.append("One or both arrays are constant (no variance)")
                return StatisticalResult(
                    value=np.nan, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )
            
            # Calculate correlation based on method
            if method.lower() == 'pearson':
                correlation = np.corrcoef(x_clean, y_clean)[0, 1]
                # Calculate additional Pearson statistics
                slope, intercept, r_value, p_value, std_err = stats.linregress(x_clean, y_clean)
                statistics = {
                    'slope': slope,
                    'intercept': intercept,
                    'r_squared': r_value**2,
                    'p_value': p_value,
                    'std_error': std_err
                }
            elif method.lower() == 'spearman':
                correlation, p_value = stats.spearmanr(x_clean, y_clean)
                statistics = {'p_value': p_value}
            elif method.lower() == 'kendall':
                correlation, p_value = stats.kendalltau(x_clean, y_clean)
                statistics = {'p_value': p_value}
            else:
                errors.append(f"Unknown correlation method: {method}")
                return StatisticalResult(
                    value=np.nan, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )
            
            # Add data quality metrics
            statistics.update({
                'x_mean': np.mean(x_clean),
                'y_mean': np.mean(y_clean),
                'x_std': np.std(x_clean),
                'y_std': np.std(y_clean),
                'data_quality': valid_count / metadata['original_size']
            })
            
            return StatisticalResult(
                value=correlation,
                statistics=statistics,
                metadata=metadata,
                warnings=warnings,
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Correlation calculation failed: {str(e)}")
            errors.append(f"Calculation failed: {str(e)}")
            return StatisticalResult(
                value=np.nan, statistics={}, metadata=metadata,
                warnings=warnings, errors=errors
            )

    @staticmethod
    def calculate_array_statistics(
        array: np.ndarray,
        percentiles: Optional[List[float]] = None,
        include_distribution: bool = False
    ) -> StatisticalResult:
        """
        Calculate comprehensive statistics for an array.
        
        Enhanced version of XCorr's calculate_array_statistics with distribution analysis.
        
        Args:
            array: Input numpy array
            percentiles: List of percentiles to calculate (default: [2, 25, 50, 75, 98])
            include_distribution: Whether to include distribution analysis
            
        Returns:
            StatisticalResult containing comprehensive statistics
        """
        if percentiles is None:
            percentiles = [2, 25, 50, 75, 98]
        
        warnings = []
        errors = []
        metadata = {
            'original_size': len(array) if hasattr(array, '__len__') else 0,
            'percentiles_calculated': percentiles,
            'include_distribution': include_distribution
        }
        
        try:
            # Convert to numpy array
            array = np.asarray(array, dtype=float)
            
            # Remove non-finite values
            finite_data = array[np.isfinite(array)]
            finite_count = len(finite_data)
            
            metadata['finite_count'] = finite_count
            metadata['removed_points'] = metadata['original_size'] - finite_count
            
            if finite_count == 0:
                warnings.append("No finite values in array")
                base_stats = {
                    'count': metadata['original_size'],
                    'finite_count': 0,
                    'mean': np.nan,
                    'std': np.nan,
                    'min': np.nan,
                    'max': np.nan,
                    'range': np.nan,
                    **{f'p{p}': np.nan for p in percentiles}
                }
                return StatisticalResult(
                    value=base_stats, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )
            
            # Calculate basic statistics
            stats_dict = {
                'count': metadata['original_size'],
                'finite_count': finite_count,
                'mean': np.mean(finite_data),
                'std': np.std(finite_data),
                'var': np.var(finite_data),
                'min': np.min(finite_data),
                'max': np.max(finite_data),
                'range': np.max(finite_data) - np.min(finite_data),
                'median': np.median(finite_data)
            }
            
            # Add percentiles
            for p in percentiles:
                stats_dict[f'p{p}'] = np.percentile(finite_data, p)
            
            # Add distribution analysis if requested
            distribution_stats = {}
            if include_distribution:
                try:
                    # Skewness and kurtosis
                    distribution_stats['skewness'] = stats.skew(finite_data)
                    distribution_stats['kurtosis'] = stats.kurtosis(finite_data)
                    
                    # Normality test (if enough data)
                    if finite_count >= 8:
                        shapiro_stat, shapiro_p = stats.shapiro(finite_data[:5000])  # Limit for performance
                        distribution_stats['shapiro_stat'] = shapiro_stat
                        distribution_stats['shapiro_p'] = shapiro_p
                        distribution_stats['is_normal'] = shapiro_p > 0.05
                    
                    # Outlier detection using IQR method
                    q1, q3 = np.percentile(finite_data, [25, 75])
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    outliers = finite_data[(finite_data < lower_bound) | (finite_data > upper_bound)]
                    
                    distribution_stats['outlier_count'] = len(outliers)
                    distribution_stats['outlier_percentage'] = (len(outliers) / finite_count) * 100
                    distribution_stats['iqr'] = iqr
                    
                except Exception as e:
                    warnings.append(f"Distribution analysis failed: {str(e)}")
            
            return StatisticalResult(
                value=stats_dict,
                statistics=distribution_stats,
                metadata=metadata,
                warnings=warnings,
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Statistics calculation failed: {str(e)}")
            errors.append(f"Calculation failed: {str(e)}")
            return StatisticalResult(
                value={}, statistics={}, metadata=metadata,
                warnings=warnings, errors=errors
            )

    @staticmethod
    def validate_array_compatibility(
        arrays: List[np.ndarray],
        array_names: Optional[List[str]] = None,
        check_finite_percentage: float = 0.5
    ) -> StatisticalResult:
        """
        Validate that arrays are compatible for analysis.

        Enhanced version of XCorr's validate_array_compatibility.

        Args:
            arrays: List of numpy arrays to validate
            array_names: Optional names for arrays (for error reporting)
            check_finite_percentage: Minimum percentage of finite values required

        Returns:
            StatisticalResult with validation results and array statistics
        """
        warnings = []
        errors = []
        metadata = {
            'array_count': len(arrays),
            'finite_percentage_threshold': check_finite_percentage
        }

        try:
            if not arrays:
                errors.append("No arrays provided for validation")
                return StatisticalResult(
                    value=False, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )

            names = array_names if array_names else [f"Array_{i+1}" for i in range(len(arrays))]
            array_stats = {}

            # Check each array individually
            for i, (arr, name) in enumerate(zip(arrays, names)):
                if not isinstance(arr, np.ndarray):
                    errors.append(f"{name} is not a numpy array")
                    continue

                if arr.size == 0:
                    errors.append(f"{name} is empty")
                    continue

                # Calculate statistics for this array
                finite_mask = np.isfinite(arr)
                finite_count = np.sum(finite_mask)
                finite_percentage = (finite_count / arr.size) * 100 if arr.size > 0 else 0

                array_stats[name] = {
                    'shape': arr.shape,
                    'size': arr.size,
                    'finite_count': finite_count,
                    'finite_percentage': finite_percentage,
                    'dtype': str(arr.dtype),
                    'mean': np.mean(arr[finite_mask]) if finite_count > 0 else np.nan,
                    'std': np.std(arr[finite_mask]) if finite_count > 0 else np.nan
                }

                # Check finite value requirements
                if finite_count == 0:
                    errors.append(f"{name} contains no finite values")
                elif finite_percentage < check_finite_percentage * 100:
                    warnings.append(
                        f"{name} has {finite_percentage:.1f}% finite values "
                        f"(below {check_finite_percentage*100}% threshold)"
                    )

            # Check shape compatibility if multiple arrays
            if len(arrays) > 1:
                reference_shape = None
                reference_name = None

                for arr, name in zip(arrays, names):
                    if isinstance(arr, np.ndarray) and arr.size > 0:
                        if reference_shape is None:
                            reference_shape = arr.shape
                            reference_name = name
                        elif arr.shape != reference_shape:
                            errors.append(
                                f"{name} shape {arr.shape} doesn't match "
                                f"{reference_name} shape {reference_shape}"
                            )

            # Overall validation result
            is_valid = len(errors) == 0

            # Calculate summary statistics
            summary_stats = {
                'total_arrays': len(arrays),
                'valid_arrays': sum(1 for stats in array_stats.values()
                                  if stats['finite_count'] > 0),
                'average_finite_percentage': np.mean([
                    stats['finite_percentage'] for stats in array_stats.values()
                ]) if array_stats else 0
            }

            return StatisticalResult(
                value=is_valid,
                statistics=summary_stats,
                metadata={**metadata, 'array_statistics': array_stats},
                warnings=warnings,
                errors=errors
            )

        except Exception as e:
            logger.error(f"Array validation failed: {str(e)}")
            errors.append(f"Validation failed: {str(e)}")
            return StatisticalResult(
                value=False, statistics={}, metadata=metadata,
                warnings=warnings, errors=errors
            )

    @staticmethod
    def calculate_distribution_comparison(
        array1: np.ndarray,
        array2: np.ndarray,
        array1_name: str = "Array1",
        array2_name: str = "Array2"
    ) -> StatisticalResult:
        """
        Compare distributions of two arrays using various statistical tests.

        Args:
            array1: First array for comparison
            array2: Second array for comparison
            array1_name: Name for first array
            array2_name: Name for second array

        Returns:
            StatisticalResult with comparison statistics
        """
        warnings = []
        errors = []
        metadata = {
            'array1_name': array1_name,
            'array2_name': array2_name
        }

        try:
            # Clean data
            arr1_clean = array1[np.isfinite(array1)]
            arr2_clean = array2[np.isfinite(array2)]

            if len(arr1_clean) == 0 or len(arr2_clean) == 0:
                errors.append("One or both arrays have no finite values")
                return StatisticalResult(
                    value={}, statistics={}, metadata=metadata,
                    warnings=warnings, errors=errors
                )

            comparison_stats = {}

            # Basic descriptive comparison
            comparison_stats['descriptive'] = {
                f'{array1_name}_mean': np.mean(arr1_clean),
                f'{array2_name}_mean': np.mean(arr2_clean),
                f'{array1_name}_std': np.std(arr1_clean),
                f'{array2_name}_std': np.std(arr2_clean),
                'mean_difference': np.mean(arr1_clean) - np.mean(arr2_clean),
                'std_ratio': np.std(arr1_clean) / np.std(arr2_clean) if np.std(arr2_clean) != 0 else np.inf
            }

            # Statistical tests
            try:
                # Two-sample t-test
                t_stat, t_p = stats.ttest_ind(arr1_clean, arr2_clean)
                comparison_stats['t_test'] = {
                    'statistic': t_stat,
                    'p_value': t_p,
                    'significant': t_p < 0.05
                }

                # Mann-Whitney U test (non-parametric)
                u_stat, u_p = stats.mannwhitneyu(arr1_clean, arr2_clean, alternative='two-sided')
                comparison_stats['mann_whitney'] = {
                    'statistic': u_stat,
                    'p_value': u_p,
                    'significant': u_p < 0.05
                }

                # Kolmogorov-Smirnov test
                ks_stat, ks_p = stats.ks_2samp(arr1_clean, arr2_clean)
                comparison_stats['kolmogorov_smirnov'] = {
                    'statistic': ks_stat,
                    'p_value': ks_p,
                    'significant': ks_p < 0.05
                }

            except Exception as e:
                warnings.append(f"Some statistical tests failed: {str(e)}")

            return StatisticalResult(
                value=comparison_stats,
                statistics={},
                metadata=metadata,
                warnings=warnings,
                errors=errors
            )

        except Exception as e:
            logger.error(f"Distribution comparison failed: {str(e)}")
            errors.append(f"Comparison failed: {str(e)}")
            return StatisticalResult(
                value={}, statistics={}, metadata=metadata,
                warnings=warnings, errors=errors
            )


# Convenience functions for backward compatibility
def calculate_correlation_safe(x, y):
    """Convenience function for StatisticalProcessor.calculate_correlation_safe()"""
    result = StatisticalProcessor.calculate_correlation_safe(x, y)
    return result.value


def calculate_array_statistics(array, percentiles=None):
    """Convenience function for StatisticalProcessor.calculate_array_statistics()"""
    result = StatisticalProcessor.calculate_array_statistics(array, percentiles)
    return result.value


def validate_array_compatibility(arrays, array_names=None):
    """Convenience function for StatisticalProcessor.validate_array_compatibility()"""
    result = StatisticalProcessor.validate_array_compatibility(arrays, array_names)
    return {
        'valid': result.value,
        'errors': result.errors,
        'warnings': result.warnings,
        'statistics': result.metadata.get('array_statistics', {})
    }
