# Xplot_RP

Xplot_RP is a simple refactoring experiment that organizes a legacy crossplotting script into a modular Python package.

## Features

- Load multiple LAS files
- Optional custom log calculations
- Column and depth selection dialogs
- Basic plotting with optional color mapping

## Installation

```bash
pip install -r requirements.txt
```

## Usage

Run the application with:

```bash
python -m xplot_app.main
```

## Modules Overview

- `xplot_app.config` – configuration constants and colormap helpers
- `xplot_app.data_io` – LAS/Excel loading utilities
- `xplot_app.dialogs` – Tkinter dialog classes
- `xplot_app.processing` – data processing helpers
- `xplot_app.plotting` – plotting utilities

