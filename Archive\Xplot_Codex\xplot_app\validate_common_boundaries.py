"""Validation script for the common boundaries implementation."""

import sys
import os

def validate_imports():
    """Validate that all required modules can be imported."""
    print("Validating imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter imported successfully")
    except ImportError as e:
        print(f"✗ tkinter import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ pandas imported successfully")
    except ImportError as e:
        print(f"✗ pandas import failed: {e}")
        return False
    
    try:
        from dialogs.common_boundaries_dialog import CommonBoundariesDialog, select_common_boundaries_for_all_wells
        print("✓ common_boundaries_dialog imported successfully")
    except ImportError as e:
        print(f"✗ common_boundaries_dialog import failed: {e}")
        return False
    
    try:
        from dialogs.enhanced_depth_dialog import EnhancedDepthDialog
        print("✓ enhanced_depth_dialog imported successfully")
    except ImportError as e:
        print(f"✗ enhanced_depth_dialog import failed: {e}")
        return False
    
    return True


def validate_file_structure():
    """Validate that all required files exist."""
    print("\nValidating file structure...")
    
    required_files = [
        "dialogs/__init__.py",
        "dialogs/common_boundaries_dialog.py",
        "dialogs/enhanced_depth_dialog.py",
        "dialogs/column_select_dialog.py",
        "dialogs/depth_dialog.py",
        "main.py",
        "data_io.py",
        "config.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
            all_exist = False
    
    return all_exist


def validate_class_structure():
    """Validate the class structure without instantiating GUI components."""
    print("\nValidating class structure...")
    
    try:
        from dialogs.common_boundaries_dialog import CommonBoundariesDialog
        
        # Check if the class has the required methods
        required_methods = [
            '_build_ui',
            '_create_common_boundaries_section',
            '_create_individual_boundaries_section',
            '_toggle_common_mode',
            '_apply_common_boundaries',
            '_on_ok',
            '_on_cancel',
            'get_boundaries'
        ]
        
        for method_name in required_methods:
            if hasattr(CommonBoundariesDialog, method_name):
                print(f"✓ CommonBoundariesDialog.{method_name} exists")
            else:
                print(f"✗ CommonBoundariesDialog.{method_name} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error validating class structure: {e}")
        return False


def validate_integration():
    """Validate that the integration with existing code is correct."""
    print("\nValidating integration...")
    
    try:
        # Check if the enhanced depth dialog imports the new common boundaries dialog
        with open("dialogs/enhanced_depth_dialog.py", "r") as f:
            content = f.read()
            
        if "from .common_boundaries_dialog import select_common_boundaries_for_all_wells" in content:
            print("✓ Enhanced depth dialog imports common boundaries dialog")
        else:
            print("✗ Enhanced depth dialog missing import for common boundaries dialog")
            return False
        
        if "use_common_boundaries_dialog" in content:
            print("✓ Enhanced depth dialog has function to use common boundaries dialog")
        else:
            print("✗ Enhanced depth dialog missing function to use common boundaries dialog")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error validating integration: {e}")
        return False


def create_sample_data():
    """Create sample data for testing without GUI."""
    print("\nCreating sample data...")
    
    try:
        import pandas as pd
        
        data = {
            'Well': ['WELL_A', 'WELL_A', 'WELL_A', 'WELL_B', 'WELL_B', 'WELL_B'],
            'Surface': ['Top_Reservoir', 'Base_Reservoir', 'Top_Seal', 'Top_Reservoir', 'Base_Reservoir', 'Top_Seal'],
            'MD': [2100.5, 2250.8, 2050.2, 2150.3, 2280.6, 2080.1]
        }
        
        df = pd.DataFrame(data)
        print(f"✓ Sample DataFrame created with {len(df)} rows")
        print(f"  Wells: {df['Well'].unique().tolist()}")
        print(f"  Surfaces: {df['Surface'].unique().tolist()}")
        
        return df
        
    except Exception as e:
        print(f"✗ Error creating sample data: {e}")
        return None


def validate_data_processing():
    """Validate data processing logic without GUI."""
    print("\nValidating data processing logic...")
    
    df = create_sample_data()
    if df is None:
        return False
    
    try:
        # Test well filtering
        las_well_names = ['WELL_A', 'WELL_B', 'WELL_C']
        available_wells = [well for well in las_well_names if well in df['Well'].values]
        expected_wells = ['WELL_A', 'WELL_B']
        
        if available_wells == expected_wells:
            print(f"✓ Well filtering works correctly: {available_wells}")
        else:
            print(f"✗ Well filtering failed. Expected {expected_wells}, got {available_wells}")
            return False
        
        # Test surface extraction
        all_surfaces = sorted(df['Surface'].unique().tolist())
        expected_surfaces = ['Base_Reservoir', 'Top_Reservoir', 'Top_Seal']
        
        if all_surfaces == expected_surfaces:
            print(f"✓ Surface extraction works correctly: {all_surfaces}")
        else:
            print(f"✗ Surface extraction failed. Expected {expected_surfaces}, got {all_surfaces}")
            return False
        
        # Test depth lookup
        well_a_data = df[df['Well'] == 'WELL_A']
        top_reservoir_depth = well_a_data[well_a_data['Surface'] == 'Top_Reservoir']['MD'].values
        
        if len(top_reservoir_depth) > 0 and top_reservoir_depth[0] == 2100.5:
            print(f"✓ Depth lookup works correctly: Top_Reservoir for WELL_A = {top_reservoir_depth[0]}")
        else:
            print(f"✗ Depth lookup failed for Top_Reservoir in WELL_A")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error validating data processing: {e}")
        return False


def main():
    """Main validation function."""
    print("=" * 60)
    print("COMMON BOUNDARIES IMPLEMENTATION VALIDATION")
    print("=" * 60)
    
    all_passed = True
    
    # Run all validation tests
    tests = [
        ("File Structure", validate_file_structure),
        ("Imports", validate_imports),
        ("Class Structure", validate_class_structure),
        ("Integration", validate_integration),
        ("Data Processing", validate_data_processing)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name} validation...")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            if result:
                print(f"✓ {test_name} validation PASSED")
            else:
                print(f"✗ {test_name} validation FAILED")
                all_passed = False
        except Exception as e:
            print(f"✗ {test_name} validation ERROR: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("The common boundaries implementation is ready for use.")
    else:
        print("❌ SOME VALIDATIONS FAILED!")
        print("Please review the errors above before using the implementation.")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
