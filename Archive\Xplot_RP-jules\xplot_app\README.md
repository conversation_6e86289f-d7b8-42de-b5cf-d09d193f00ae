# XPlot - Well Log Crossplot Analysis Tool

A modular Python application for creating crossplots with marginal distributions from well log data (LAS files). This tool provides statistical visualization capabilities including histograms, kernel density estimation (KDE), and customizable plotting options.

## 🚀 Features

- **Multi-well LAS file support**: Load and analyze multiple LAS files simultaneously
- **Custom calculations**: Create new log curves using mathematical expressions
- **Interactive column selection**: Choose X, Y, Class, and Z-axis data from available logs
- **Flexible depth ranges**: Define analysis intervals manually or import from Excel
- **Statistical visualizations**: Histograms and KDE plots for marginal distributions
- **Customizable plotting**: Extensive options for colors, symbols, axis limits, and styling
- **Data validation**: Comprehensive validation with statistics reporting
- **Excel integration**: Import depth boundaries and export results

## 📁 Project Structure

```
xplot_app/
├── main.py                          # Main application entry point
├── config.py                        # Configuration constants and utilities
├── data_io.py                       # Data input/output operations
├── processing.py                    # Core data processing and validation
├── statistical_analyzers.py         # Statistical analysis classes
├── plotting.py                      # Main plotting functionality
├── gui_utils.py                     # GUI utility functions
├── dialogs/                         # User interface dialogs
│   ├── __init__.py
│   ├── batch_boundaries_dialog.py   # Batch boundary selection
│   ├── calculator_dialog.py         # Custom calculation input
│   ├── column_select_dialog.py      # Column selection interface
│   ├── depth_dialog.py              # Depth range configuration
│   ├── plot_settings_dialog.py      # Plot customization settings
│   ├── post_plot_dialog.py          # Post-plot action options
│   ├── single_well_boundaries_dialog.py  # Single well boundary selection
│   └── statistics_dialog.py         # Data statistics display
├── initial/                         # Original monolithic implementation
│   └── Xplot_HIST_KDE_FUNCT_Init.py # Original script (reference)
├── test_imports.py                  # Import verification script
├── Refactor_plan.md                 # Detailed refactoring plan
├── REFACTORING_ANALYSIS_REPORT.md   # Analysis of refactoring implementation
└── README.md                        # This file
```

## 🏗️ Architecture Overview

The application follows a modular architecture with clear separation of concerns:

### Core Modules

#### `main.py` - Application Orchestration
- **Purpose**: Main entry point and workflow orchestration
- **Key Functions**:
  - `main()`: Primary application loop
  - `get_calculations()`: Handle custom calculations
  - `get_column_names_and_depths()`: Manage column and depth selection
  - `get_plot_settings()`: Configure plot appearance
  - `show_data_statistics()`: Display validation results

#### `config.py` - Configuration Management
- **Purpose**: Centralized configuration and constants
- **Key Components**:
  - `log_keywords`: Dictionary mapping log types to common mnemonics
  - `COLORMAP_CATEGORIES`: Organized colormap options
  - Colormap utility functions for validation and manipulation

#### `data_io.py` - Data Input/Output
- **Purpose**: Handle file operations and data loading
- **Key Functions**:
  - `load_multiple_las_files()`: LAS file loading with error handling
  - `load_boundaries_from_excel()`: Excel boundary data import
  - `filter_excel_data_for_las_wells()`: Data filtering for loaded wells

#### `processing.py` - Data Processing
- **Purpose**: Core data processing, validation, and analysis
- **Key Functions**:
  - `validate_data_for_plotting()`: Comprehensive data validation
  - `analyze_log_availability()`: Log availability analysis across wells
  - `find_default_columns()`: Automatic column detection
  - `validate_calculation_inputs()`: Custom calculation validation

#### `statistical_analyzers.py` - Statistical Analysis
- **Purpose**: Statistical analysis and visualization classes
- **Key Classes**:
  - `StatisticalAnalyzer`: Abstract base class for analysis
  - `HistogramAnalyzer`: Histogram creation and plotting
  - `KDEAnalyzer`: Kernel density estimation
  - `StatisticalVisualizer`: Combined histogram and KDE visualization

#### `plotting.py` - Visualization
- **Purpose**: Main plotting functionality and chart creation
- **Key Functions**:
  - `create_plot()`: Primary plotting function
  - Layout management for main plots and marginal distributions
  - Colormap handling and data aggregation

#### `gui_utils.py` - GUI Utilities
- **Purpose**: Reusable GUI components and utilities
- **Key Functions**:
  - `create_scrollable_frame()`: Scrollable interface creation

### Dialog Modules (`dialogs/`)

#### `column_select_dialog.py` - Column Selection
- **Purpose**: Interactive column selection interface
- **Features**: X/Y axis selection, optional class/Z-axis columns, mutual exclusivity logic

#### `depth_dialog.py` - Depth Range Configuration
- **Purpose**: Depth range selection for analysis
- **Features**: Manual input, Excel import, batch processing, default fallbacks

#### `plot_settings_dialog.py` - Plot Customization
- **Purpose**: Comprehensive plot appearance configuration
- **Features**: Point size, colors, symbols, axis limits, legend settings, colormap options

#### `calculator_dialog.py` - Custom Calculations
- **Purpose**: Interface for creating custom log calculations
- **Features**: Mathematical expression input, variable validation, syntax checking

#### `statistics_dialog.py` - Data Statistics
- **Purpose**: Display data validation and analysis statistics
- **Features**: Formatted statistics display, data quality metrics

#### `post_plot_dialog.py` - Post-Plot Actions
- **Purpose**: Options after plot creation
- **Features**: Restart application, exit, or continue workflow

## 🔄 Application Workflow

1. **Initialization**: Load and validate LAS files
2. **Calculations** (Optional): Apply custom mathematical expressions
3. **Excel Import** (Optional): Load depth ranges from Excel files
4. **Column Selection**: Choose X, Y, Class, and Z-axis data
5. **Depth Configuration**: Set analysis depth ranges
6. **Data Validation**: Validate selected data and show statistics
7. **Plot Settings**: Configure appearance and styling options
8. **Visualization**: Create and display the crossplot
9. **Post-Plot**: Choose to restart, exit, or continue

## 🛠️ Installation and Setup

### Prerequisites
- Python 3.7+
- Required packages (install via pip):
  ```bash
  pip install numpy pandas matplotlib scipy lasio tkinter openpyxl
  ```

### Running the Application
```bash
python main.py
```

## 📊 Data Requirements

### LAS Files
- Standard LAS format (version 1.2 or 2.0)
- Must contain DEPTH curve
- Common log mnemonics supported (see `config.py` for full list)

### Excel Files (Optional)
- Columns: `Well`, `Surface`, `MD`
- Well names must match LAS file well names
- Used for automated depth range selection

## 🎨 Customization Options

### Plot Settings
- **Point size and transparency**
- **Histogram bin counts**
- **Marginal plot types** (Histogram, KDE, Both, None)
- **Color schemes and symbols**
- **Axis titles and limits**
- **Legend styles and positioning**
- **Colormap selection for Z-axis data**

### Statistical Options
- **Kernel density estimation bandwidth**
- **Data downsampling factors**
- **Class-based analysis**
- **Robust statistical limits**

## 🧪 Testing

Run the import verification test:
```bash
python test_imports.py
```

This verifies that all modules can be imported correctly and the refactoring is properly implemented.

## 📈 Key Features in Detail

### Statistical Analysis
- **Histogram Analysis**: Automatic bin optimization, class-based histograms
- **KDE Analysis**: Gaussian kernel density estimation with bandwidth control
- **Data Validation**: Comprehensive validation with detailed statistics
- **Robust Statistics**: Outlier-resistant statistical calculations

### Data Handling
- **Multi-well Support**: Simultaneous analysis of multiple wells
- **Depth Filtering**: Flexible depth range selection per well
- **Missing Data Handling**: Intelligent NaN handling and interpolation
- **Custom Calculations**: Mathematical expression evaluation for new curves

### Visualization
- **Crossplots**: Scatter plots with optional class coloring or Z-axis colormaps
- **Marginal Distributions**: Histograms and KDE plots on plot margins
- **Customizable Styling**: Extensive appearance customization options
- **Interactive Legends**: Multiple legend styles and configurations

## 🔧 Development Notes

### Code Organization
- **Modular Design**: Clear separation of concerns across modules
- **Type Hints**: Comprehensive type annotations for better code clarity
- **Error Handling**: Robust error handling with user-friendly messages
- **Documentation**: Detailed docstrings and inline comments

### Extension Points
- **New Statistical Analyzers**: Extend `StatisticalAnalyzer` base class
- **Custom Dialogs**: Add new dialogs following existing patterns
- **Additional File Formats**: Extend `data_io.py` for new formats
- **Plot Types**: Add new visualization types to `plotting.py`

## 📝 License

This project is part of a well log analysis toolkit. Please refer to your organization's licensing terms.

## 🤝 Contributing

When contributing to this project:
1. Follow the existing modular architecture
2. Add appropriate type hints and documentation
3. Test imports using `test_imports.py`
4. Update this README for significant changes

## 📞 Support

For issues or questions about the XPlot application, please refer to the original implementation in `initial/Xplot_HIST_KDE_FUNCT_Init.py` or consult the detailed refactoring documentation in `Refactor_plan.md` and `REFACTORING_ANALYSIS_REPORT.md`.