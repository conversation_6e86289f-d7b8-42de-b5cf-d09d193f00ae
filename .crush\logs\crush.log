{"time":"2025-10-08T17:27:51.444989+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"github.com/charmbracelet/crush/internal/config/provider.go","line":194},"msg":"Cache is not available or is stale. Fetching providers from Catwalk.","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-10-08T17:27:52.3388403+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"github.com/charmbracelet/crush/internal/config/provider.go","line":52},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-10-08T17:27:53.2603192+08:00","level":"INFO","msg":"OK   20250424200609_initial.sql (2.66ms)"}
{"time":"2025-10-08T17:27:53.2613542+08:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (522.2µs)"}
{"time":"2025-10-08T17:27:53.2618777+08:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (523.5µs)"}
{"time":"2025-10-08T17:27:53.2623966+08:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (518.9µs)"}
{"time":"2025-10-08T17:27:53.2623966+08:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-10-08T17:27:53.2629159+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"github.com/charmbracelet/crush/internal/app/lsp.go","line":21},"msg":"LSP clients initialization started in background"}
{"time":"2025-10-08T17:27:53.2787633+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func2","file":"github.com/charmbracelet/crush/internal/llm/agent/agent.go","line":175},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-10-08T17:27:55.9133751+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.createAndInitializeClient","file":"github.com/charmbracelet/crush/internal/llm/agent/mcp-tools.go","line":364},"msg":"Initialized mcp client","name":"sequentialthinking"}
{"time":"2025-10-08T17:27:55.9351092+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.createAndInitializeClient","file":"github.com/charmbracelet/crush/internal/llm/agent/mcp-tools.go","line":364},"msg":"Initialized mcp client","name":"memory"}
{"time":"2025-10-08T17:27:55.9563906+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.createAndInitializeClient","file":"github.com/charmbracelet/crush/internal/llm/agent/mcp-tools.go","line":364},"msg":"Initialized mcp client","name":"brave-search"}
{"time":"2025-10-08T17:28:00.7805866+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func2","file":"github.com/charmbracelet/crush/internal/llm/agent/agent.go","line":175},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-10-08T17:28:03.7883591+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/cmd.init.func5","file":"github.com/charmbracelet/crush/internal/cmd/root.go","line":95},"msg":"TUI run error","error":"program was killed: No process is on the other end of the pipe."}
{"time":"2025-10-08T17:28:03.7883591+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"github.com/charmbracelet/crush/internal/llm/agent/agent.go","line":247},"msg":"Request cancellation initiated","session_id":"bef96c8f-902b-47b5-9aec-24cd3557235b"}
{"time":"2025-10-08T17:28:03.7883591+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"github.com/charmbracelet/crush/internal/llm/agent/agent.go","line":258},"msg":"Clearing queued prompts","session_id":"bef96c8f-902b-47b5-9aec-24cd3557235b"}
{"time":"2025-10-08T17:28:03.8439106+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).Shutdown","file":"github.com/charmbracelet/crush/internal/app/app.go","line":338},"msg":"Failed to cleanup app properly on shutdown","error":"close mcp: memory: exit status 0xc000013a\nclose mcp: brave-search: exit status 0xc000013a"}
