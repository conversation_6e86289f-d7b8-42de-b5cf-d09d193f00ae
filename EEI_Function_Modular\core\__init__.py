# -*- coding: utf-8 -*-
"""
Core Package for EEI Function Modular

This package provides core functionality for data I/O, validation,
and processing operations.

Author: Integration of EEI_XCorr_Modular and EEI_Xplot_Modular
Created: 2025
Version: 1.0.0
"""

from .data_io import (
    load_las_files,
    load_excel_boundaries,
    filter_excel_for_wells,
    find_default_columns,
)

from .data_validation import (
    DataValidator,
    ArrayValidator,
    CalculationValidator,
    ValidationResult,
)

__all__ = [
    'load_las_files',
    'load_excel_boundaries',
    'filter_excel_for_wells',
    'find_default_columns',
    'DataValidator',
    'ArrayValidator',
    'CalculationValidator',
    'ValidationResult',
]
