# -*- coding: utf-8 -*-
"""
Base Configuration Module

This module contains common configuration data used across all EEI Function
Modular operations. It merges and consolidates settings from both EEI_XCorr_Modular
and EEI_Xplot_Modular to provide a unified configuration interface.

Author: Integration of EEI_XCorr_Modular and EEI_Xplot_Modular
Created: 2025
Version: 1.0.0
"""

from typing import Dict, List, Any

# ============================================================================
# LOG KEYWORD MAPPINGS
# ============================================================================
# Merged from both modules with comprehensive coverage
# Priority: XCorr keywords + unique Xplot additions

LOG_KEYWORDS: Dict[str, List[str]] = {
    # Sonic logs
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC', 'AC', 'DT4P'],
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC', 'DT4S', 'SHEAR_DT'],
    
    # Porosity logs
    'PHIT': ['PHIT', 'PHID', 'PHI_D', 'POR', 'TOTAL_POR'],
    'PHIE': ['PHIE', 'PHIE_D', 'EFF_POR', 'EFFECTIVE_POR'],
    'NPHI': ['NPHI', 'NEUTRON', 'NEUTRON_POROSITY', 'NPOR'],
    
    # Density logs
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO', 'BULK_DEN'],
    'DRHO': ['DRHO', 'DENSITY_CORRECTION', 'DELTARHO'],
    
    # Saturation logs
    'SWT': ['SWT', 'SW', 'WATER_SAT', 'SAT_WATER'],
    'SWE': ['SWE', 'SWE_D', 'SW_EFF'],
    
    # Depth
    'DEPTH': ['DEPTH', 'DEPT', 'MD', 'MEASURED_DEPTH'],
    
    # Velocity logs
    'P-WAVE': ['P-WAVE', 'P_VELOCITY', 'VP', 'VELP'],
    'S-WAVE': ['S-WAVE', 'S_VELOCITY', 'VS', 'VELS'],
    
    # Classification logs
    'FLUID_CODE': ['FLUID_CODE', 'FLUID', 'FLUID_ID'],
    'FLUID_PETREL': ['FLUID_PETREL'],
    'LITHO_CODE': ['LITHO_CODE', 'LITHOLOGY', 'LITHO_PETREL', 'LITH'],
    
    # Gamma Ray
    'GR': ['GR', 'GAMMA_RAY', 'GR_LOG', 'CGR', 'SGR'],
    
    # Elastic moduli
    'KSOLID': ['KSOLID', 'K_SOLID'],
    'GSOLID': ['GSOLID', 'G_SOLID'],
    'KSAT': ['KSAT', 'K_SATURATION'],
    'GSAT': ['GSAT', 'G_SATURATION'],
    'KDRY': ['KDRY', 'K_DRY'],
    'GDRY': ['GDRY', 'G_DRY'],
    
    # Clay volume
    'VCL': ['VCL', 'VOL_WETCLAY', 'V_CLAY', 'VSH', 'SHALE_VOL'],
    
    # Resistivity
    'RT': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD', 'AT90', 'DEEP_RES'],
    
    # Other logs
    'CALI': ['CALI', 'CAL', 'CALIPER'],
    'PEF': ['PEF', 'PE', 'PHOTOELECTRIC'],
    'SP': ['SP', 'SPONTANEOUS_POTENTIAL'],
}

# ============================================================================
# FILE FORMAT SPECIFICATIONS
# ============================================================================

FILE_FORMATS: Dict[str, Dict[str, Any]] = {
    'las': {
        'extensions': ['.las'],
        'description': 'Log ASCII Standard files',
        'required_curves': ['DEPTH', 'DT', 'DTS', 'RHOB'],
        'optional_curves': ['GR', 'NPHI', 'PHIT', 'PHIE', 'SWT', 'VCL', 'RT']
    },
    'excel': {
        'extensions': ['.xls', '.xlsx'],
        'description': 'Excel boundary/depth range files',
        'required_columns': ['Well', 'Surface', 'MD'],
        'optional_columns': ['Formation', 'Zone', 'Lithology']
    }
}

# ============================================================================
# ERROR AND SUCCESS MESSAGES
# ============================================================================

ERROR_MESSAGES: Dict[str, str] = {
    'missing_essential_logs': "Essential logs (Vp, Vs, Density) are missing from one or more wells.",
    'invalid_depth_range': "Invalid depth range specified. Top depth must be less than bottom depth.",
    'insufficient_data': "Insufficient valid data points for analysis. Minimum {min_points} required.",
    'calculation_failed': "Calculation failed due to invalid input data or parameters.",
    'correlation_failed': "Correlation calculation failed. Check data quality and compatibility.",
    'optimization_failed': "Parameter optimization failed to converge. Try different parameter ranges.",
    'file_load_error': "Error loading file {filename}: {error}",
    'missing_columns': "The Excel file is missing required columns: {columns}",
    'no_matching_wells': "No wells in the Excel file match the loaded LAS files.",
    'empty_file': "The file contains no data.",
}

SUCCESS_MESSAGES: Dict[str, str] = {
    'calculation_complete': "Calculation completed successfully.",
    'optimization_complete': "Parameter optimization completed successfully.",
    'data_loaded': "Data loaded successfully from {count} wells.",
    'validation_passed': "All validation checks passed.",
    'export_complete': "Results exported successfully to {filename}.",
    'boundaries_loaded': "Loaded {count} boundary entries for {wells} wells.",
}

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

LOGGING_CONFIG: Dict[str, Any] = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'date_format': '%Y-%m-%d %H:%M:%S',
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO'
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'eei_function_modular.log',
            'level': 'DEBUG',
            'mode': 'a'
        }
    }
}


# ============================================================================
# BASE CONFIGURATION CLASS
# ============================================================================

class BaseConfig:
    """
    Base configuration manager for EEI Function Modular.
    
    Provides centralized access to common configuration parameters
    used across all modules with validation and type checking.
    """
    
    @staticmethod
    def get_log_keywords() -> Dict[str, List[str]]:
        """
        Get log keyword mappings for automatic log detection.
        
        Returns:
            Dict mapping standard log names to possible aliases
        """
        return LOG_KEYWORDS.copy()
    
    @staticmethod
    def get_file_formats() -> Dict[str, Dict[str, Any]]:
        """
        Get supported file format specifications.
        
        Returns:
            Dict containing file format details and requirements
        """
        return FILE_FORMATS.copy()
    
    @staticmethod
    def get_error_message(error_key: str, **kwargs) -> str:
        """
        Get formatted error message.
        
        Args:
            error_key: Key for error message
            **kwargs: Format parameters for message
            
        Returns:
            Formatted error message
        """
        if error_key not in ERROR_MESSAGES:
            return f"Unknown error: {error_key}"
        
        try:
            return ERROR_MESSAGES[error_key].format(**kwargs)
        except KeyError:
            return ERROR_MESSAGES[error_key]
    
    @staticmethod
    def get_success_message(success_key: str, **kwargs) -> str:
        """
        Get formatted success message.
        
        Args:
            success_key: Key for success message
            **kwargs: Format parameters for message
            
        Returns:
            Formatted success message
        """
        if success_key not in SUCCESS_MESSAGES:
            return f"Operation completed: {success_key}"
        
        try:
            return SUCCESS_MESSAGES[success_key].format(**kwargs)
        except KeyError:
            return SUCCESS_MESSAGES[success_key]
    
    @staticmethod
    def get_logging_config() -> Dict[str, Any]:
        """
        Get logging configuration.
        
        Returns:
            Dict containing logging configuration
        """
        return LOGGING_CONFIG.copy()
    
    @staticmethod
    def find_log_alias(log_type: str, available_logs: List[str]) -> str:
        """
        Find the first matching log alias from available logs.
        
        Args:
            log_type: Standard log type (e.g., 'DT', 'RHOB')
            available_logs: List of available log names
            
        Returns:
            Matching log name or None if not found
        """
        if log_type not in LOG_KEYWORDS:
            return None
        
        # Convert available logs to uppercase for case-insensitive comparison
        available_upper = {log.upper(): log for log in available_logs}
        
        # Check each alias
        for alias in LOG_KEYWORDS[log_type]:
            if alias.upper() in available_upper:
                return available_upper[alias.upper()]
        
        return None
