# -*- coding: utf-8 -*-
"""
Specialized Processing Module

This package contains specialized processing functions for specific analysis types
in the EEI Function Modular framework.

Modules:
- eei_processing: Extended Elastic Impedance specific processing

Author: EEI Function Modular Integration
Created: 2025-10-08
Version: 1.0.0
"""

from .eei_processing import (
    EEIProcessor,
    EEIAnalysisResult,
    prepare_eei_data,
    validate_eei_inputs
)

__all__ = [
    'EEIProcessor',
    'EEIAnalysisResult', 
    'prepare_eei_data',
    'validate_eei_inputs'
]
