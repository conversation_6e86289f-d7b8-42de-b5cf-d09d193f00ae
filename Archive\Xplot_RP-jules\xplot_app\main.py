"""Main application entry point for XPlot App."""
import tkinter as tk
from tkinter import messagebox
from typing import Optional, List, Dict, Any, Tuple
import traceback

# Import refactored modules
from config import log_keywords
from data_io import load_multiple_las_files, load_excel_depth_ranges
from processing import (
    analyze_log_availability, 
    find_default_columns, 
    validate_calculation_inputs, 
    validate_data_for_plotting, 
    execute_custom_calculations
)
from plotting import create_plot
from dialogs.column_select_dialog import ColumnSelectionDialog
from dialogs.depth_dialog import DepthRangeDialog
from dialogs.plot_settings_dialog import PlotSettingsDialog
from dialogs.post_plot_dialog import PostPlotDialog
from dialogs.calculator_dialog import CalculatorDialog
from dialogs.statistics_dialog import StatisticsDialog


def get_calculations(root: tk.Tk, las_files: List[Any]) -> Optional[str]:
    """
    Present a calculator dialog for custom calculations.
    
    Args:
        root: The main Tkinter root window.
        las_files: List of loaded LAS files.
        
    Returns:
        Calculation text or None if cancelled.
    """
    dialog = CalculatorDialog(root, las_files)
    calculation_text = dialog.get_calculation_string()
    
    return calculation_text


def get_column_names_and_depths(
    root: tk.Tk, 
    las_files: List[Any], 
    preloaded_excel_df: Optional[Any] = None
) -> Optional[Tuple[str, str, Optional[str], Optional[str], Dict[str, Tuple[float, float]]]]:
    """
    Get column selections and depth ranges from user.
    
    Args:
        root: The main Tkinter root window.
        las_files: List of loaded LAS files.
        preloaded_excel_df: Optional preloaded Excel DataFrame.
        
    Returns:
        Tuple of (x_col, y_col, class_col, z_col, depth_ranges) or None if cancelled.
    """
    # Analyze log availability
    log_analysis = analyze_log_availability(las_files)
    common_columns = log_analysis['common_logs']
    
    if not common_columns:
        messagebox.showerror("No Common Columns", "No common columns found across all LAS files.")
        return None
    
    # Column selection dialog
    column_dialog = ColumnSelectionDialog(root, common_columns, las_files)
    selected_columns = column_dialog.get_selected_columns()
    
    if not selected_columns:
        return None
    
    x_col = selected_columns['x_col']
    y_col = selected_columns['y_col'] 
    class_col = selected_columns.get('class_col')
    z_col = selected_columns.get('z_col')
    
    # Depth range selection dialog
    depth_dialog = DepthRangeDialog(root, las_files, preloaded_excel_df)
    depth_ranges = depth_dialog.get_selected_depth_ranges()
    
    if not depth_ranges:
        return None
        
    return x_col, y_col, class_col, z_col, depth_ranges


def get_plot_settings(
    root: tk.Tk, 
    las_files: List[Any], 
    x_col: str, 
    y_col: str, 
    class_col: Optional[str], 
    depth_ranges: Dict[str, Tuple[float, float]], 
    z_col: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Get plot settings from user.
    
    Args:
        root: The main Tkinter root window.
        las_files: List of loaded LAS files.
        x_col: X column name.
        y_col: Y column name.
        class_col: Optional class column name.
        depth_ranges: Depth ranges for each well.
        z_col: Optional Z column name.

    Returns:
        Plot settings dictionary or None if cancelled.
    """
    settings_dialog = PlotSettingsDialog(root, las_files, x_col, y_col, class_col, depth_ranges, z_col)
    settings = settings_dialog.get_settings()
    
    return settings


def show_post_plot_options(root: tk.Tk) -> str:
    """
    Show post-plot options dialog.
    
    Args:
        root: The main Tkinter root window.
    
    Returns:
        User choice ('restart', 'exit', or 'cancel').
    """
    dialog = PostPlotDialog(root)
    choice = dialog.get_choice()
    
    return choice


def show_data_statistics(root: tk.Tk, validation_stats: Dict[str, Any]) -> None:
    """
    Show data validation statistics dialog.
    
    Args:
        root: The main Tkinter root window.
        validation_stats: Dictionary containing validation statistics.
    """
    dialog = StatisticsDialog(root, validation_stats)
    dialog.wait_window()  # Wait for dialog to close


def main() -> None:
    """Main function to run the XPlot application."""
    # Create a single root window for the entire application
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    
    try:
        while True:
            print("\n" + "="*50)
            print("XPlot Application - Well Log Crossplot Tool")
            print("="*50)
            
            # Step 1: Load LAS files
            print("\nStep 1: Loading LAS files...")
            las_files = load_multiple_las_files()
            if not las_files:
                print("No LAS files loaded. Exiting.")
                break
            
            # Step 2: Optional calculations
            print("\nStep 2: Custom calculations (optional)...")
            calculation_text = get_calculations(root, las_files)
            if calculation_text:
                # Validate and apply calculations
                if validate_calculation_inputs(las_files, calculation_text):
                    # Execute the calculations
                    if execute_custom_calculations(las_files, calculation_text):
                        print("Custom calculations applied successfully.")
                    else:
                        print("Calculation execution failed.")
                        continue
                else:
                    print("Calculation validation failed.")
                    continue
            
            # Step 3: Load Excel depth ranges (optional)
            print("\nStep 3: Loading depth ranges...")
            preloaded_excel_df = load_excel_depth_ranges(las_files)
            
            # Step 4: Column and depth selection
            print("\nStep 4: Column and depth selection...")
            selection_result = get_column_names_and_depths(root, las_files, preloaded_excel_df)
            if not selection_result:
                print("Column/depth selection cancelled.")
                continue
                
            x_col, y_col, class_col, z_col, depth_ranges = selection_result
            
            # Step 5: Data validation
            print("\nStep 5: Validating data...")
            validation_stats = validate_data_for_plotting(las_files, x_col, y_col, class_col, depth_ranges, z_col)
            if not validation_stats:
                print("Data validation failed.")
                continue
            
            # Show validation statistics
            show_data_statistics(root, validation_stats)
            
            # Step 6: Plot settings
            print("\nStep 6: Plot settings...")
            settings = get_plot_settings(root, las_files, x_col, y_col, class_col, depth_ranges, z_col)
            if not settings:
                print("Plot settings cancelled.")
                continue
            
            # Step 7: Create and display plot
            print("\nStep 7: Creating plot...")
            create_plot(las_files, x_col, y_col, class_col, depth_ranges, settings, z_col)
            
            # Step 8: Post-plot options
            print("\nStep 8: Post-plot options...")
            choice = show_post_plot_options(root)
            
            if choice == 'exit':
                print("Exiting application.")
                break
            elif choice == 'restart':
                print("Restarting application...")
                continue
            else:
                print("Exiting application.")
                break
                
    except Exception as e:
        print(f"An error occurred: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
    finally:
        root.destroy()


if __name__ == '__main__':
    main()