"""Main plotting logic for XPlot App."""

import matplotlib.pyplot as plt
import numpy as np
from scipy import stats # Kept as it's used in the fallback marginal plots
from typing import List, Dict, Optional, Tuple, Any

# Imports from other custom modules
from processing import validate_data_for_plotting, get_robust_limits
from statistical_analyzers import StatisticalVisualizer, HistogramAnalyzer, KDEAnalyzer
from config import apply_colormap_reversal, validate_colormap, COLORMAP_CATEGORIES
import lasio

# Helper Functions Start

def _setup_plot_layout(
    fig: plt.Figure, 
    class_col: Optional[str], 
    z_col: Optional[str],
    marginal_plot_type: str
) -> Tuple[plt.Axes, Optional[plt.Axes], Optional[plt.Axes]]:
    """
    Sets up the GridSpec and main/marginal axes for the plot.

    Args:
        fig: The main matplotlib Figure object.
        class_col: Optional mnemonic for class data.
        z_col: Optional mnemonic for Z-axis data.
        marginal_plot_type: Type of marginal plot requested ('Histogram', 'KDE', 'Both', 'None').

    Returns:
        A tuple containing:
            - ax_main (plt.Axes): The main scatter plot axis.
            - ax_histx (Optional[plt.Axes]): The X-axis marginal plot axis, or None.
            - ax_histy (Optional[plt.Axes]): The Y-axis marginal plot axis, or None.
    """
    ax_histx, ax_histy = None, None
    if class_col and not z_col and marginal_plot_type != "None":
        gs = fig.add_gridspec(4, 4, hspace=0.05, wspace=0.05)
        ax_main = fig.add_subplot(gs[1:, :-1])
        ax_histx = fig.add_subplot(gs[0, :-1], sharex=ax_main)
        ax_histy = fig.add_subplot(gs[1:, -1], sharey=ax_main)
        plt.setp(ax_histx.get_xticklabels(), visible=False)
        plt.setp(ax_histx.get_yticklabels(), visible=False)
        ax_histx.set_yticks([])
        plt.setp(ax_histy.get_xticklabels(), visible=False)
        plt.setp(ax_histy.get_yticklabels(), visible=False)
        ax_histy.set_xticks([])
        print("Layout: Main plot with marginals.")
    else:
        gs = fig.add_gridspec(1, 1)
        ax_main = fig.add_subplot(gs[0, 0])
        print("Layout: Main plot only.")
    return ax_main, ax_histx, ax_histy

def _aggregate_plot_data(
    las_files: List[lasio.LASFile], 
    x_col: str, y_col: str, 
    class_col: Optional[str], 
    z_col: Optional[str], 
    depth_ranges: Dict[str, Tuple[float, float]], 
    downsample_factor: int
) -> Dict[str, Any]:
    """
    Aggregates and filters data from LAS files for plotting.

    Args:
        las_files: List of loaded lasio.LASFile objects.
        x_col: Mnemonic for X-axis data.
        y_col: Mnemonic for Y-axis data.
        class_col: Optional mnemonic for class data.
        z_col: Optional mnemonic for Z-axis data.
        depth_ranges: Dictionary mapping well names to (top, bottom) depth tuples.
        downsample_factor: Factor for downsampling the data (1 = no downsampling).

    Returns:
        A dictionary containing aggregated data arrays, overall min/max values,
        and a list of wells with issues encountered during processing.
    """
    all_x_data, all_y_data, all_class_data_for_plot, all_z_data = [], [], [], []
    overall_x_min, overall_x_max = float('inf'), float('-inf')
    overall_y_min, overall_y_max = float('inf'), float('-inf')
    wells_with_issues = []

    print("Processing data for each well...")
    for las in las_files:
        well_name = las.well.WELL.value if las.well.WELL else "UnknownWell"
        if well_name not in depth_ranges:
            wells_with_issues.append(f"{well_name}: No depth range specified.")
            continue
        
        top_depth, bottom_depth = depth_ranges[well_name]
        depth_curve = las.curves.get('DEPTH')
        if depth_curve is None:
            wells_with_issues.append(f"{well_name}: Missing DEPTH curve.")
            continue
        depth = np.array(depth_curve.data)

        missing_cols_current_well = []
        if x_col not in las: missing_cols_current_well.append(x_col)
        if y_col not in las: missing_cols_current_well.append(y_col)
        if class_col and class_col not in las: missing_cols_current_well.append(class_col)
        if z_col and z_col not in las: missing_cols_current_well.append(z_col)

        if missing_cols_current_well:
            wells_with_issues.append(f"{well_name}: Missing columns: {', '.join(missing_cols_current_well)}")
            continue
            
        mask = (depth >= top_depth) & (depth <= bottom_depth)
        if not np.any(mask):
            wells_with_issues.append(f"{well_name}: No data in specified depth range.")
            continue

        x_data_full = np.array(las[x_col].data)[mask]
        y_data_full = np.array(las[y_col].data)[mask]
        z_data_full = np.array(las[z_col].data)[mask] if z_col else None
        current_las_class_data = np.array(las[class_col].data)[mask] if class_col else None

        valid_mask_xy = ~np.isnan(x_data_full) & ~np.isnan(y_data_full)
        if z_data_full is not None:
            valid_mask_xy &= ~np.isnan(z_data_full)
        
        x_data_clean = x_data_full[valid_mask_xy]
        y_data_clean = y_data_full[valid_mask_xy]
        z_data_clean = z_data_full[valid_mask_xy] if z_data_full is not None else None
        class_data_for_xy_clean = current_las_class_data[valid_mask_xy] if current_las_class_data is not None else np.full(x_data_clean.shape, np.nan)
        
        if x_data_clean.size == 0:
            wells_with_issues.append(f"{well_name}: No valid (non-NaN) data points for selected columns in range.")
            continue

        if x_data_clean.size > 0:
            overall_x_min = min(overall_x_min, np.nanmin(x_data_clean))
            overall_x_max = max(overall_x_max, np.nanmax(x_data_clean))
        if y_data_clean.size > 0:
            overall_y_min = min(overall_y_min, np.nanmin(y_data_clean))
            overall_y_max = max(overall_y_max, np.nanmax(y_data_clean))

        if downsample_factor > 1 and len(x_data_clean) > downsample_factor:
            indices = np.arange(len(x_data_clean))
            sampled_indices = indices[::downsample_factor]
            x_data_plot, y_data_plot = x_data_clean[sampled_indices], y_data_clean[sampled_indices]
            z_data_plot = z_data_clean[sampled_indices] if z_data_clean is not None else None
            class_data_to_extend = class_data_for_xy_clean[sampled_indices]
        else:
            x_data_plot, y_data_plot = x_data_clean, y_data_clean
            z_data_plot = z_data_clean
            class_data_to_extend = class_data_for_xy_clean
        
        all_x_data.extend(x_data_plot)
        all_y_data.extend(y_data_plot)
        all_class_data_for_plot.extend(class_data_to_extend)
        if z_data_plot is not None: all_z_data.extend(z_data_plot)
    
    return {
        'all_x_data_np': np.array(all_x_data),
        'all_y_data_np': np.array(all_y_data),
        'all_class_data_np': np.array(all_class_data_for_plot) if all_class_data_for_plot else None,
        'all_z_data_np': np.array(all_z_data) if all_z_data else None,
        'overall_x_min': overall_x_min, 'overall_x_max': overall_x_max,
        'overall_y_min': overall_y_min, 'overall_y_max': overall_y_max,
        'wells_with_issues': wells_with_issues
    }

def _draw_scatter_points(
    ax_main: plt.Axes, 
    x_data: np.ndarray, y_data: np.ndarray, 
    settings: Dict[str, Any], 
    class_data: Optional[np.ndarray] = None, 
    z_data: Optional[np.ndarray] = None,
    x_col_name: Optional[str] = None, # Added for default label
    y_col_name: Optional[str] = None, # Added for default label
    z_col_name: Optional[str] = None, # For colorbar label
    class_col_name: Optional[str] = None # For legend title (though settings might override)
) -> None:
    """
    Draws scatter points on the main plot axis.

    Handles Z-axis colormaps, class-based coloring/symbols, or default scatter plot.
    """
    point_size = settings.get('point_size', 50)

    if z_col_name and z_data is not None and len(z_data) > 0:
        cmap_name = settings.get('colormap', 'viridis')
        reversed_cmap = settings.get('reverse_colormap', False)
        actual_cmap = apply_colormap_reversal(cmap_name, reversed_cmap)
        # Ensure colormap_category is passed, default to 'Sequential' if not in settings
        colormap_category = settings.get('colormap_category', 'Sequential')
        validated_cmap_name = validate_colormap(actual_cmap, COLORMAP_CATEGORIES.get(colormap_category))
        
        scatter = ax_main.scatter(x_data, y_data, c=z_data, s=point_size, cmap=validated_cmap_name, alpha=0.7)
        if settings.get('show_colorbar', True):
            cbar = plt.colorbar(scatter, ax=ax_main, 
                                location=settings.get('colorbar_position', 'right'), 
                                shrink=settings.get('colorbar_shrink', 0.8), 
                                aspect=settings.get('colorbar_aspect', 20), 
                                pad=settings.get('colorbar_pad', 0.02),
                                fraction=settings.get('colorbar_fraction', 0.05),
                                orientation=settings.get('colorbar_orientation','vertical'))
            cbar.set_label(settings.get('colorbar_label', z_col_name), fontsize=settings.get('z_title_size', 12), fontweight=settings.get('z_title_weight', 'normal'))
            cbar.ax.tick_params(labelsize=settings.get('tick_label_size', 10))
    elif class_col_name and class_data is not None and settings.get('symbols') and settings.get('colors') and settings.get('class_names'):
        unique_plot_classes = np.unique(class_data[~np.isnan(class_data)])
        for class_val in unique_plot_classes:
            class_mask = (class_data == class_val)
            ax_main.scatter(x_data[class_mask], y_data[class_mask], s=point_size, 
                            color=settings['colors'].get(str(class_val), '#000000'), 
                            marker=settings['symbols'].get(str(class_val), 'o'), 
                            label=settings['class_names'].get(str(class_val), f'Class {class_val}'), 
                            alpha=0.7)
        if 'NaN' in settings['symbols'] and 'NaN' in settings['colors']:
            nan_mask = np.isnan(class_data)
            if np.any(nan_mask):
                ax_main.scatter(x_data[nan_mask], y_data[nan_mask], s=point_size, 
                                color=settings['colors'].get('NaN', '#808080'), 
                                marker=settings['symbols'].get('NaN', 'x'), 
                                label=settings['class_names'].get('NaN', 'No Data/NaN'), 
                                alpha=0.7)
    else:
        ax_main.scatter(x_data, y_data, s=point_size, color=settings.get('default_point_color', 'blue'), marker=settings.get('default_point_marker', 'o'), alpha=0.7, label=f'{x_col_name} vs {y_col_name}')

def _draw_marginal_plots(
    ax_histx: Optional[plt.Axes], ax_histy: Optional[plt.Axes], 
    x_data_marginal: np.ndarray, y_data_marginal: np.ndarray, 
    settings: Dict[str, Any], 
    class_data_marginal: Optional[np.ndarray],
    x_main_lim: Tuple[float, float], 
    y_main_lim: Tuple[float, float]
) -> None:
    """
    Draws marginal histogram/KDE plots on the provided axes.
    """
    if not ax_histx or not ax_histy : return # Should not happen if called correctly

    marginal_plot_type = settings.get('marginal_plot_type', 'Both')
    bin_size = settings.get('bin_size', 30)

    hist_analyzer = HistogramAnalyzer(bins=bin_size)
    kde_analyzer = KDEAnalyzer()
    
    # Determine which analyzers to use based on plot type
    x_analyzer_instance = None
    y_analyzer_instance = None
    if marginal_plot_type == 'Histogram':
        x_analyzer_instance = hist_analyzer
        y_analyzer_instance = hist_analyzer
    elif marginal_plot_type == 'KDE':
        x_analyzer_instance = kde_analyzer
        y_analyzer_instance = kde_analyzer
    elif marginal_plot_type == 'Both':
        # For 'Both', StatisticalVisualizer handles passing data to both
        x_analyzer_instance = hist_analyzer # Primary for hist, KDE is added
        y_analyzer_instance = hist_analyzer # Primary for hist, KDE is added
    
    visualizer = StatisticalVisualizer(
        x_data=x_data_marginal,
        y_data=y_data_marginal,
        class_data=class_data_marginal,
        colors=settings.get('colors', {}),
        class_names=settings.get('class_names', {}),
        x_analyzer=x_analyzer_instance,
        y_analyzer=y_analyzer_instance
    )
    # For 'Both', StatisticalVisualizer internally also calls KDE.
    # If only KDE, it should use kde_analyzer.
    visualizer.plot_marginal_distributions(ax_histx, ax_histy, marginal_plot_type, x_range=x_main_lim, y_range=y_main_lim, bins=bin_size)


def _apply_plot_formatting(
    fig: plt.Figure, ax_main: plt.Axes, 
    settings: Dict[str, Any], 
    x_col_name: str, y_col_name: str, 
    z_col_name: Optional[str], class_col_name: Optional[str],
    overall_x_min: Optional[float], overall_x_max: Optional[float],
    overall_y_min: Optional[float], overall_y_max: Optional[float],
    x_data_all: np.ndarray, y_data_all: np.ndarray
) -> None:
    """
    Applies axis limits, titles, labels, legend, and other styling to the plot.
    """
    final_x_min, final_x_max = get_robust_limits(x_data_all, settings.get('x_axis_min_user'), settings.get('x_axis_max_user'), overall_x_min, overall_x_max)
    final_y_min, final_y_max = get_robust_limits(y_data_all, settings.get('y_axis_min_user'), settings.get('y_axis_max_user'), overall_y_min, overall_y_max)

    if final_x_min is not None and final_x_max is not None and final_x_min < final_x_max: ax_main.set_xlim(final_x_min, final_x_max)
    if final_y_min is not None and final_y_max is not None and final_y_min < final_y_max: ax_main.set_ylim(final_y_min, final_y_max)

    ax_main.set_xlabel(settings.get('x_title_text', x_col_name), fontsize=settings.get('x_title_size', 12), fontweight=settings.get('x_title_weight', 'normal'), fontstyle=settings.get('x_title_style', 'normal'), fontfamily=settings.get('x_title_family', 'Arial'))
    ax_main.set_ylabel(settings.get('y_title_text', y_col_name), fontsize=settings.get('y_title_size', 12), fontweight=settings.get('y_title_weight', 'normal'), fontstyle=settings.get('y_title_style', 'normal'), fontfamily=settings.get('y_title_family', 'Arial'))
    
    ax_main.tick_params(axis='both', which='major', labelsize=settings.get('tick_label_size', 10))
    for tick in ax_main.get_xticklabels(): tick.set_fontweight(settings.get('tick_label_weight', 'normal')); tick.set_fontstyle(settings.get('tick_label_style', 'normal'))
    for tick in ax_main.get_yticklabels(): tick.set_fontweight(settings.get('tick_label_weight', 'normal')); tick.set_fontstyle(settings.get('tick_label_style', 'normal'))

    handles, labels = ax_main.get_legend_handles_labels()
    if handles:
        legend_title = settings.get('legend_title', class_col_name if class_col_name and settings.get('class_names') else None)
        ax_main.legend(handles, labels, 
                       fontsize=settings.get('legend_font_size', 10), 
                       title=legend_title, 
                       title_fontsize=str(int(settings.get('legend_font_size', 10)) + 1) if legend_title else None,
                       loc=settings.get('legend_location', 'best'),
                       frameon=settings.get('legend_frame', True))

    title_parts = [f"Crossplot: {settings.get('x_title_text', x_col_name)} vs {settings.get('y_title_text', y_col_name)}"]
    if z_col_name:
        cmap_name = settings.get('colormap', 'viridis')
        reversed_cmap = settings.get('reverse_colormap', False)
        actual_cmap_name = apply_colormap_reversal(cmap_name, reversed_cmap)
        title_parts.append(f"(colored by {settings.get('colorbar_label', z_col_name)}, {actual_cmap_name})")
    
    fig.suptitle(" ".join(title_parts), fontsize=settings.get('suptitle_fontsize',16), fontweight=settings.get('suptitle_fontweight','bold'))
    fig.tight_layout(rect=[0, 0.03, 1, 0.95])

# Helper Functions End


def create_plot(
    las_files: List[lasio.LASFile],
    x_col: str,
    y_col: str,
    class_col: Optional[str],
    depth_ranges: Dict[str, Tuple[float, float]],
    settings: Dict[str, Any], 
    z_col: Optional[str] = None
) -> None:
    """
    Generates and displays a crossplot by orchestrating calls to helper functions.
    Handles data validation, layout setup, data aggregation, scatter plotting,
    marginal plots, and plot formatting.
    """
    print("\n=== XPlot - Orchestrating Plot Creation ===")
    print(f"X-Col: {x_col}, Y-Col: {y_col}, Class-Col: {class_col}, Z-Col: {z_col}")
    # print(f"Full Settings: {settings}") # Can be verbose

    validation_result = validate_data_for_plotting(las_files, x_col, y_col, class_col, depth_ranges, z_col)
    fig = plt.figure(figsize=settings.get('figure_size', (12, 10)))

    if not validation_result['valid']:
        print("Validation failed - cannot create plot.")
        ax = fig.add_subplot(111)
        error_message = "Cannot create plot:\n\n" + "\n".join(f"• {issue}" for issue in validation_result['issues'])
        if validation_result['warnings']:
            error_message += "\n\nWarnings:\n" + "\n".join(f"• {warning}" for warning in validation_result['warnings'])
        ax.text(0.5, 0.5, error_message, horizontalalignment='center', verticalalignment='center', transform=ax.transAxes, wrap=True, fontsize=10, color='red')
        ax.set_xticks([]); ax.set_yticks([])
        plt.title(f"Plot Error: {settings.get('x_title_text', x_col)} vs {settings.get('y_title_text', y_col)}", fontsize=14, fontweight='bold')
        plt.show()
        return

    print("Validation passed.")
    marginal_plot_type = settings.get('marginal_plot_type', 'None')
    ax_main, ax_histx, ax_histy = _setup_plot_layout(fig, class_col, z_col, marginal_plot_type)
    
    plot_data_package = _aggregate_plot_data(
        las_files, x_col, y_col, class_col, z_col, 
        depth_ranges, settings.get('downsampling_factor', 1)
    )

    if plot_data_package['all_x_data_np'].size == 0:
        ax_main.text(0.5, 0.5, "No data to display for the selected criteria.", horizontalalignment='center', verticalalignment='center', transform=ax_main.transAxes, wrap=True, fontsize=12)
        if plot_data_package['wells_with_issues']:
             print("Wells with issues during data aggregation:\n" + "\n".join(f"• {issue}" for issue in plot_data_package['wells_with_issues']))
        plt.show()
        return

    _draw_scatter_points(
        ax_main, 
        plot_data_package['all_x_data_np'], 
        plot_data_package['all_y_data_np'],
        settings,
        class_data=plot_data_package['all_class_data_np'],
        z_data=plot_data_package['all_z_data_np'],
        x_col_name=x_col, 
        y_col_name=y_col,
        z_col_name=z_col,
        class_col_name=class_col
    )
    
    if ax_histx and ax_histy and marginal_plot_type != 'None':
        # Ensure data passed to marginal plots is only the valid, non-NaN data for those axes
        x_marginal_clean = plot_data_package['all_x_data_np'][~np.isnan(plot_data_package['all_x_data_np'])]
        y_marginal_clean = plot_data_package['all_y_data_np'][~np.isnan(plot_data_package['all_y_data_np'])]
        
        class_marginal_clean = None
        if plot_data_package['all_class_data_np'] is not None:
             # Align class data with valid x and y for marginals
            valid_xy_mask_for_class = ~np.isnan(plot_data_package['all_x_data_np']) & ~np.isnan(plot_data_package['all_y_data_np'])
            class_marginal_clean = plot_data_package['all_class_data_np'][valid_xy_mask_for_class]
            class_marginal_clean = class_marginal_clean[~np.isnan(class_marginal_clean)] # remove NaNs from class data itself

        if x_marginal_clean.size > 0 and y_marginal_clean.size > 0 :
            _draw_marginal_plots(
                ax_histx, ax_histy, 
                x_marginal_clean, y_marginal_clean,
                settings, 
                class_data_marginal=class_marginal_clean,
                x_main_lim=ax_main.get_xlim(), 
                y_main_lim=ax_main.get_ylim()
            )
        
    _apply_plot_formatting(
        fig, ax_main, settings, x_col, y_col, z_col, class_col,
        plot_data_package['overall_x_min'], plot_data_package['overall_x_max'],
        plot_data_package['overall_y_min'], plot_data_package['overall_y_max'],
        plot_data_package['all_x_data_np'], plot_data_package['all_y_data_np']
    )
    
    plt.show()
