"""Test script for the new common boundaries functionality."""

import tkinter as tk
import pandas as pd
import numpy as np
from dialogs.common_boundaries_dialog import select_common_boundaries_for_all_wells


def create_sample_data():
    """Create sample Excel boundary data for testing."""
    data = {
        'Well': ['WELL_A', 'WELL_A', 'WELL_A', 'WELL_A', 'WELL_A', 'WELL_A',
                 'WELL_B', 'WELL_B', 'WELL_B', 'WELL_B', 'WELL_B', 'WELL_B',
                 'WELL_C', 'WELL_C', 'WELL_C', 'WELL_C', 'WELL_C', 'WELL_C'],
        'Surface': ['Top_Reservoir', 'Base_Reservoir', 'Top_Seal', 'Base_Seal', 'Top_Source', 'Base_Source',
                   'Top_Reservoir', 'Base_Reservoir', 'Top_Seal', 'Base_Seal', 'Top_Source', 'Base_Source',
                   'Top_Reservoir', 'Base_Reservoir', 'Top_Seal', 'Base_Seal', 'Top_Source', 'Base_Source'],
        'MD': [2100.5, 2250.8, 2050.2, 2100.5, 2300.1, 2450.7,
               2150.3, 2280.6, 2080.1, 2150.3, 2320.4, 2480.9,
               2200.7, 2350.2, 2120.5, 2200.7, 2380.3, 2520.1]
    }
    return pd.DataFrame(data)


def create_mock_las_files():
    """Create mock LAS file objects for testing."""
    class MockWell:
        def __init__(self, well_name):
            self.WELL = MockValue(well_name)
    
    class MockValue:
        def __init__(self, value):
            self.value = value
    
    class MockLAS:
        def __init__(self, well_name):
            self.well = MockWell(well_name)
    
    return [MockLAS("WELL_A"), MockLAS("WELL_B"), MockLAS("WELL_C")]


def test_common_boundaries_dialog():
    """Test the common boundaries dialog."""
    print("Testing Common Boundaries Dialog...")
    
    # Create sample data
    df = create_sample_data()
    mock_las_files = create_mock_las_files()
    las_well_names = [las.well.WELL.value for las in mock_las_files]
    
    print(f"Sample data created with {len(df)} boundary entries")
    print(f"Wells in data: {df['Well'].unique().tolist()}")
    print(f"Surfaces in data: {df['Surface'].unique().tolist()}")
    print(f"LAS well names: {las_well_names}")
    
    # Test the dialog
    try:
        result = select_common_boundaries_for_all_wells(df, las_well_names)
        
        if result:
            print(f"\nDialog completed successfully!")
            print(f"Selected boundaries for {len(result)} wells:")
            for well_name, (top, bottom) in result.items():
                print(f"  {well_name}: {top:.2f} - {bottom:.2f}")
        else:
            print("\nDialog was cancelled or no boundaries were selected.")
            
    except Exception as e:
        print(f"\nError during dialog test: {str(e)}")
        import traceback
        traceback.print_exc()


def test_data_validation():
    """Test the data validation logic."""
    print("\nTesting data validation...")
    
    df = create_sample_data()
    
    # Test 1: Valid data
    print("Test 1: Valid data structure")
    required_columns = ['Well', 'Surface', 'MD']
    has_required = all(col in df.columns for col in required_columns)
    print(f"  Has required columns: {has_required}")
    
    # Test 2: Well matching
    print("Test 2: Well matching")
    las_well_names = ['WELL_A', 'WELL_B', 'WELL_C']
    wells_in_excel = df['Well'].unique()
    matching_wells = [well for well in las_well_names if well in wells_in_excel]
    print(f"  LAS wells: {las_well_names}")
    print(f"  Excel wells: {wells_in_excel.tolist()}")
    print(f"  Matching wells: {matching_wells}")
    
    # Test 3: Surface availability
    print("Test 3: Surface availability per well")
    for well in matching_wells:
        well_data = df[df['Well'] == well]
        surfaces = well_data['Surface'].unique()
        print(f"  {well}: {surfaces.tolist()}")
    
    # Test 4: Depth validation
    print("Test 4: Depth validation")
    for well in matching_wells:
        well_data = df[df['Well'] == well].sort_values('MD')
        min_depth = well_data['MD'].min()
        max_depth = well_data['MD'].max()
        print(f"  {well}: depth range {min_depth:.2f} - {max_depth:.2f}")


def main():
    """Main test function."""
    print("=" * 60)
    print("COMMON BOUNDARIES DIALOG TEST")
    print("=" * 60)
    
    # Test data validation first
    test_data_validation()
    
    print("\n" + "=" * 60)
    
    # Test the actual dialog
    test_common_boundaries_dialog()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETED")
    print("=" * 60)


if __name__ == "__main__":
    main()
