import unittest
from unittest.mock import Mock # For mocking lasio.LASFile and lasio.CurveItem
from typing import Dict, List, Optional, Any # Added Any

# Adjust import path based on how the application will be structured and run
# Assuming xplot_app is in PYTHONPATH or installed
from xplot_app.processing import find_default_columns
# If running tests from the root directory and xplot_app is a direct subdir:
# from xplot_app.processing import find_default_columns

class TestProcessing(unittest.TestCase):

    def create_mock_las_file(self, curve_mnemonics: List[str]) -> Any: # Using Any for lasio.LASFile
        """Helper to create a mock LASFile object with specified curve mnemonics."""
        mock_las = Mock()
        mock_las.well.WELL.value = "TestWell" # Mock well name
        
        mock_curves = []
        for mnem in curve_mnemonics:
            curve_item = Mock()
            curve_item.mnemonic = mnem
            mock_curves.append(curve_item)
        
        mock_las.curves = mock_curves # Directly assign the list of mocks
        return mock_las

    def test_find_default_columns_exact_match(self):
        """Test finding columns with exact matches to aliases."""
        mock_las = self.create_mock_las_file(['GR', 'DT', 'RHOB_LOG'])
        keywords = {
            'GAMMA_RAY': ['GR', 'CGR'],
            'SONIC': ['DT', 'AC'],
            'DENSITY': ['RHOB', 'RHOZ', 'RHOB_LOG']
        }
        expected_columns = {
            'GAMMA_RAY': 'GR',
            'SONIC': 'DT',
            'DENSITY': 'RHOB_LOG'
        }
        result_columns = find_default_columns(mock_las, keywords)
        self.assertEqual(result_columns, expected_columns)

    def test_find_default_columns_alias_match(self):
        """Test finding columns using alternative aliases."""
        mock_las = self.create_mock_las_file(['CGR', 'AC', 'RHOZ'])
        keywords = {
            'GAMMA_RAY': ['GR', 'CGR'],
            'SONIC': ['DT', 'AC'],
            'DENSITY': ['RHOB', 'RHOZ']
        }
        expected_columns = {
            'GAMMA_RAY': 'CGR',
            'SONIC': 'AC',
            'DENSITY': 'RHOZ'
        }
        result_columns = find_default_columns(mock_las, keywords)
        self.assertEqual(result_columns, expected_columns)

    def test_find_default_columns_no_match(self):
        """Test when some keywords have no matching columns."""
        mock_las = self.create_mock_las_file(['GR', 'NPHI'])
        keywords = {
            'GAMMA_RAY': ['GR'],
            'SONIC': ['DT', 'AC'], # No SONIC log in mock_las
            'NEUTRON': ['NPHI']
        }
        expected_columns = {
            'GAMMA_RAY': 'GR',
            'SONIC': None,
            'NEUTRON': 'NPHI'
        }
        result_columns = find_default_columns(mock_las, keywords)
        self.assertEqual(result_columns, expected_columns)

    def test_find_default_columns_case_insensitivity(self):
        """Test case-insensitive matching of mnemonics and aliases."""
        mock_las = self.create_mock_las_file(['gr', 'Dt', 'rhob'])
        keywords = {
            'GAMMA_RAY': ['GR', 'CGR'],
            'SONIC': ['dt', 'AC'], # Alias 'dt' in lowercase
            'DENSITY': ['RHOB', 'RHOZ']
        }
        # The function should return the mnemonic as it appears in the LAS file
        expected_columns = {
            'GAMMA_RAY': 'gr',
            'SONIC': 'Dt',
            'DENSITY': 'rhob'
        }
        result_columns = find_default_columns(mock_las, keywords)
        self.assertEqual(result_columns, expected_columns)

    def test_find_default_columns_empty_keywords(self):
        """Test with an empty keywords dictionary."""
        mock_las = self.create_mock_las_file(['GR', 'DT'])
        keywords = {}
        expected_columns = {}
        result_columns = find_default_columns(mock_las, keywords)
        self.assertEqual(result_columns, expected_columns)

    def test_find_default_columns_empty_las_curves(self):
        """Test with a LAS file that has no curves."""
        mock_las = self.create_mock_las_file([])
        keywords = {
            'GAMMA_RAY': ['GR'],
            'SONIC': ['DT']
        }
        expected_columns = {
            'GAMMA_RAY': None,
            'SONIC': None
        }
        result_columns = find_default_columns(mock_las, keywords)
        self.assertEqual(result_columns, expected_columns)

if __name__ == '__main__':
    unittest.main()
