#!/usr/bin/env python3
"""Validate the enhanced color customization implementation."""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    try:
        import numpy as np
        print("✓ numpy imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import numpy: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✓ matplotlib imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import matplotlib: {e}")
        return False
    
    try:
        import config
        print("✓ config module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import config: {e}")
        return False
    
    try:
        import statistical_analyzers
        print("✓ statistical_analyzers module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import statistical_analyzers: {e}")
        return False
    
    try:
        import plotting
        print("✓ plotting module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import plotting: {e}")
        return False
    
    return True

def test_class_functionality():
    """Test the class-based functionality without GUI."""
    try:
        import numpy as np
        from statistical_analyzers import StatisticalVisualizer
        
        # Create test data
        x_data = np.random.normal(0, 1, 100)
        y_data = np.random.normal(0, 1, 100)
        class_data = np.random.choice([1, 2, 3], 100)
        
        # Test colors and class names
        colors = {1: "#ff0000", 2: "#00ff00", 3: "#0000ff"}
        class_names = {1: "Class A", 2: "Class B", 3: "Class C"}
        
        # Create visualizer
        visualizer = StatisticalVisualizer(x_data, y_data, class_data, colors, class_names)
        print("✓ StatisticalVisualizer created successfully with class data")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to test class functionality: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_colormap_functionality():
    """Test colormap functionality."""
    try:
        import matplotlib.pyplot as plt
        
        # Test colormap access
        cmap = plt.colormaps['viridis']
        rgba_color = cmap(0.5)
        hex_color = "#{:02x}{:02x}{:02x}".format(
            int(rgba_color[0] * 255),
            int(rgba_color[1] * 255),
            int(rgba_color[2] * 255)
        )
        print(f"✓ Colormap functionality works, generated color: {hex_color}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to test colormap functionality: {e}")
        return False

def main():
    """Run all validation tests."""
    print("Validating enhanced color customization implementation...")
    print("=" * 60)
    
    all_passed = True
    
    print("\n1. Testing imports...")
    if not test_imports():
        all_passed = False
    
    print("\n2. Testing class functionality...")
    if not test_class_functionality():
        all_passed = False
    
    print("\n3. Testing colormap functionality...")
    if not test_colormap_functionality():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ All validation tests passed!")
        print("\nImplementation Summary:")
        print("- Enhanced PlotSettingsDialog with class customization")
        print("- Individual color selection for each class")
        print("- Symbol customization for each class")
        print("- Class name customization")
        print("- NaN value handling as special class")
        print("- Class-based histogram and KDE coloring")
        return True
    else:
        print("✗ Some validation tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
