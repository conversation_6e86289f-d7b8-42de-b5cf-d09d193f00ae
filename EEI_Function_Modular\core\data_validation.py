# -*- coding: utf-8 -*-
"""
Data Validation Module

This module provides comprehensive validation functionality for data arrays,
calculations, and plotting operations. It consolidates validation logic from
both EEI_XCorr_Modular and EEI_Xplot_Modular.

Author: Integration of EEI_XCorr_Modular and EEI_Xplot_Modular
Created: 2025
Version: 1.0.0
"""

from __future__ import annotations

from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass
import logging

import numpy as np
import pandas as pd

from ..config import BaseConfig, EEIConfig

logger = logging.getLogger(__name__)

# ============================================================================
# VALIDATION RESULT CLASS
# ============================================================================

@dataclass
class ValidationResult:
    """Container for validation results."""
    
    is_valid: bool
    message: str
    details: Optional[Dict[str, Any]] = None
    warnings: Optional[List[str]] = None
    
    def __bool__(self) -> bool:
        """Allow ValidationResult to be used in boolean context."""
        return self.is_valid
    
    def add_warning(self, warning: str) -> None:
        """Add a warning message."""
        if self.warnings is None:
            self.warnings = []
        self.warnings.append(warning)
    
    def get_full_message(self) -> str:
        """Get complete message including warnings."""
        msg = self.message
        if self.warnings:
            msg += "\n\nWarnings:\n" + "\n".join(f"- {w}" for w in self.warnings)
        return msg


# ============================================================================
# BASE VALIDATOR CLASS
# ============================================================================

class DataValidator:
    """Base validator class with common validation methods."""
    
    @staticmethod
    def validate_not_none(value: Any, name: str = "Value") -> ValidationResult:
        """Validate that value is not None."""
        if value is None:
            return ValidationResult(
                is_valid=False,
                message=f"{name} cannot be None"
            )
        return ValidationResult(is_valid=True, message="Valid")
    
    @staticmethod
    def validate_not_empty(data: Any, name: str = "Data") -> ValidationResult:
        """Validate that data is not empty."""
        if data is None:
            return ValidationResult(
                is_valid=False,
                message=f"{name} is None"
            )
        
        if isinstance(data, (list, tuple, np.ndarray, pd.Series)):
            if len(data) == 0:
                return ValidationResult(
                    is_valid=False,
                    message=f"{name} is empty"
                )
        
        return ValidationResult(is_valid=True, message="Valid")
    
    @staticmethod
    def validate_numeric(data: Any, name: str = "Data") -> ValidationResult:
        """Validate that data is numeric."""
        try:
            if isinstance(data, (int, float, np.number)):
                return ValidationResult(is_valid=True, message="Valid")
            
            if isinstance(data, (list, tuple)):
                data = np.array(data)
            
            if isinstance(data, np.ndarray):
                if not np.issubdtype(data.dtype, np.number):
                    return ValidationResult(
                        is_valid=False,
                        message=f"{name} must contain numeric values"
                    )
                return ValidationResult(is_valid=True, message="Valid")
            
            if isinstance(data, pd.Series):
                if not pd.api.types.is_numeric_dtype(data):
                    return ValidationResult(
                        is_valid=False,
                        message=f"{name} must contain numeric values"
                    )
                return ValidationResult(is_valid=True, message="Valid")
            
            return ValidationResult(
                is_valid=False,
                message=f"{name} is not a recognized numeric type"
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                message=f"Error validating {name}: {str(e)}"
            )


# ============================================================================
# ARRAY VALIDATOR CLASS
# ============================================================================

class ArrayValidator(DataValidator):
    """Validator for array data."""
    
    @staticmethod
    def validate_array_compatibility(
        *arrays: np.ndarray,
        names: Optional[List[str]] = None
    ) -> ValidationResult:
        """
        Validate that multiple arrays are compatible for operations.
        
        Args:
            *arrays: Variable number of arrays to validate
            names: Optional names for arrays
            
        Returns:
            ValidationResult indicating compatibility
        """
        if len(arrays) == 0:
            return ValidationResult(
                is_valid=False,
                message="No arrays provided for validation"
            )
        
        if names is None:
            names = [f"Array {i+1}" for i in range(len(arrays))]
        
        # Check all arrays are not None
        for arr, name in zip(arrays, names):
            result = DataValidator.validate_not_none(arr, name)
            if not result:
                return result
        
        # Check all arrays have same length
        lengths = [len(arr) for arr in arrays]
        if len(set(lengths)) > 1:
            length_info = ", ".join(f"{name}: {length}" 
                                   for name, length in zip(names, lengths))
            return ValidationResult(
                is_valid=False,
                message=f"Arrays have incompatible lengths: {length_info}"
            )
        
        # Check minimum data points
        validation_params = EEIConfig.get_validation_params()
        min_points = validation_params['min_data_points']
        
        if lengths[0] < min_points:
            return ValidationResult(
                is_valid=False,
                message=f"Insufficient data points. Found {lengths[0]}, "
                       f"minimum required: {min_points}"
            )
        
        return ValidationResult(
            is_valid=True,
            message="Arrays are compatible",
            details={'length': lengths[0], 'count': len(arrays)}
        )
    
    @staticmethod
    def validate_finite_values(
        data: np.ndarray,
        name: str = "Data",
        min_percentage: Optional[float] = None
    ) -> ValidationResult:
        """
        Validate that array has sufficient finite values.
        
        Args:
            data: Array to validate
            name: Name of data for messages
            min_percentage: Minimum percentage of finite values required
            
        Returns:
            ValidationResult
        """
        # Check not None
        result = DataValidator.validate_not_none(data, name)
        if not result:
            return result
        
        # Convert to numpy array
        if not isinstance(data, np.ndarray):
            data = np.array(data)
        
        # Count finite values
        finite_mask = np.isfinite(data)
        finite_count = np.sum(finite_mask)
        total_count = len(data)
        
        if total_count == 0:
            return ValidationResult(
                is_valid=False,
                message=f"{name} is empty"
            )
        
        finite_percentage = (finite_count / total_count) * 100
        
        # Get minimum percentage from config if not provided
        if min_percentage is None:
            validation_params = EEIConfig.get_validation_params()
            min_percentage = validation_params['min_finite_percentage']
        
        if finite_percentage < min_percentage:
            return ValidationResult(
                is_valid=False,
                message=f"{name} has insufficient finite values: "
                       f"{finite_percentage:.1f}% (minimum: {min_percentage}%)",
                details={
                    'finite_count': finite_count,
                    'total_count': total_count,
                    'percentage': finite_percentage
                }
            )
        
        result = ValidationResult(
            is_valid=True,
            message=f"{name} has {finite_percentage:.1f}% finite values",
            details={
                'finite_count': finite_count,
                'total_count': total_count,
                'percentage': finite_percentage
            }
        )
        
        # Add warning if percentage is low but acceptable
        if finite_percentage < 75:
            result.add_warning(
                f"{name} has only {finite_percentage:.1f}% finite values. "
                "Results may be affected."
            )
        
        return result
    
    @staticmethod
    def validate_range(
        data: np.ndarray,
        name: str = "Data",
        min_val: Optional[float] = None,
        max_val: Optional[float] = None
    ) -> ValidationResult:
        """
        Validate that data values are within specified range.
        
        Args:
            data: Array to validate
            name: Name of data
            min_val: Minimum allowed value
            max_val: Maximum allowed value
            
        Returns:
            ValidationResult
        """
        # Check not None
        result = DataValidator.validate_not_none(data, name)
        if not result:
            return result
        
        # Convert to numpy array
        if not isinstance(data, np.ndarray):
            data = np.array(data)
        
        # Get finite values
        finite_data = data[np.isfinite(data)]
        
        if len(finite_data) == 0:
            return ValidationResult(
                is_valid=False,
                message=f"{name} has no finite values"
            )
        
        data_min = np.min(finite_data)
        data_max = np.max(finite_data)
        
        # Check minimum
        if min_val is not None and data_min < min_val:
            return ValidationResult(
                is_valid=False,
                message=f"{name} contains values below minimum: "
                       f"{data_min} < {min_val}"
            )
        
        # Check maximum
        if max_val is not None and data_max > max_val:
            return ValidationResult(
                is_valid=False,
                message=f"{name} contains values above maximum: "
                       f"{data_max} > {max_val}"
            )
        
        return ValidationResult(
            is_valid=True,
            message=f"{name} values are within valid range",
            details={'min': data_min, 'max': data_max}
        )


# ============================================================================
# CALCULATION VALIDATOR CLASS
# ============================================================================

class CalculationValidator(DataValidator):
    """Validator for calculation inputs and outputs."""
    
    @staticmethod
    def validate_calculation_inputs(
        inputs: Dict[str, np.ndarray],
        required_keys: List[str]
    ) -> ValidationResult:
        """
        Validate inputs for calculations.
        
        Args:
            inputs: Dict of input arrays
            required_keys: List of required key names
            
        Returns:
            ValidationResult
        """
        # Check all required keys present
        missing_keys = [key for key in required_keys if key not in inputs]
        if missing_keys:
            return ValidationResult(
                is_valid=False,
                message=f"Missing required inputs: {', '.join(missing_keys)}"
            )
        
        # Validate each input
        arrays = []
        names = []
        for key in required_keys:
            arr = inputs[key]
            
            # Check not None
            result = DataValidator.validate_not_none(arr, key)
            if not result:
                return result
            
            # Check numeric
            result = DataValidator.validate_numeric(arr, key)
            if not result:
                return result
            
            arrays.append(arr)
            names.append(key)
        
        # Check compatibility
        return ArrayValidator.validate_array_compatibility(*arrays, names=names)
    
    @staticmethod
    def validate_calculation_output(
        output: np.ndarray,
        name: str = "Output"
    ) -> ValidationResult:
        """
        Validate calculation output.
        
        Args:
            output: Output array
            name: Name of output
            
        Returns:
            ValidationResult
        """
        # Check not None
        result = DataValidator.validate_not_none(output, name)
        if not result:
            return result
        
        # Check numeric
        result = DataValidator.validate_numeric(output, name)
        if not result:
            return result
        
        # Check for finite values
        return ArrayValidator.validate_finite_values(output, name)


# ============================================================================
# PLOT DATA VALIDATOR CLASS
# ============================================================================

class PlotDataValidator(DataValidator):
    """Validator for plotting data."""
    
    @staticmethod
    def validate_plot_data(
        x_data: np.ndarray,
        y_data: np.ndarray,
        z_data: Optional[np.ndarray] = None,
        class_data: Optional[np.ndarray] = None
    ) -> ValidationResult:
        """
        Validate data for plotting.
        
        Args:
            x_data: X-axis data
            y_data: Y-axis data
            z_data: Optional Z-axis (color) data
            class_data: Optional classification data
            
        Returns:
            ValidationResult
        """
        # Collect arrays and names
        arrays = [x_data, y_data]
        names = ['X data', 'Y data']
        
        if z_data is not None:
            arrays.append(z_data)
            names.append('Z data')
        
        if class_data is not None:
            arrays.append(class_data)
            names.append('Classification data')
        
        # Validate compatibility
        result = ArrayValidator.validate_array_compatibility(*arrays, names=names)
        if not result:
            return result
        
        # Validate finite values for X and Y
        for arr, name in [(x_data, 'X data'), (y_data, 'Y data')]:
            result = ArrayValidator.validate_finite_values(arr, name)
            if not result:
                return result
        
        return ValidationResult(
            is_valid=True,
            message="Plot data is valid",
            details={'point_count': len(x_data)}
        )
    
    @staticmethod
    def validate_depth_range(
        top_depth: float,
        bottom_depth: float
    ) -> ValidationResult:
        """
        Validate depth range.
        
        Args:
            top_depth: Top depth value
            bottom_depth: Bottom depth value
            
        Returns:
            ValidationResult
        """
        if top_depth >= bottom_depth:
            return ValidationResult(
                is_valid=False,
                message=BaseConfig.get_error_message('invalid_depth_range')
            )
        
        return ValidationResult(
            is_valid=True,
            message="Depth range is valid",
            details={'top': top_depth, 'bottom': bottom_depth, 
                    'thickness': bottom_depth - top_depth}
        )
