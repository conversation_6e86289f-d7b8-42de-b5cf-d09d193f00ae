# -*- coding: utf-8 -*-
"""
Visualization Configuration Module

This module contains configuration for plotting, colormaps, and
visualization styling used throughout EEI Function Modular.

Author: Migrated from EEI_Xplot_Modular
Created: 2025
Version: 1.0.0
"""

from typing import Dict, List, Optional, Union
import matplotlib.pyplot as plt

# ============================================================================
# COLORMAP CATEGORIES
# ============================================================================

COLORMAP_CATEGORIES: Dict[str, Union[Dict[str, List[str]], List[str]]] = {
    'Sequential': {
        'Perceptually Uniform': ['viridis', 'plasma', 'inferno', 'magma', 'cividis'],
        'Single Hue': [
            'Blues', 'BuGn', 'BuPu', 'GnBu', 'Greens', 'Greys', 'Oranges',
            'OrRd', 'PuBu', 'PuBuGn', 'PuRd', 'Purples', 'RdPu', 'Reds',
            'YlGn', 'YlGnBu', 'YlOrBr', 'YlOrRd'
        ],
        'Multi Hue': [
            'rainbow', 'turbo', 'jet', 'hsv', 'gist_rainbow', 'gist_ncar',
            'nipy_spectral', 'CMRmap'
        ],
    },
    'Diverging': [
        'RdYlBu', 'RdBu', 'coolwarm', 'seismic', 'bwr', 'RdGy', 'PiYG',
        'PRGn', 'BrBG', 'RdYlGn', 'Spectral'
    ],
    'Qualitative': [
        'Set1', 'Set2', 'Set3', 'tab10', 'tab20', 'tab20b', 'tab20c',
        'Pastel1', 'Pastel2', 'Paired', 'Accent', 'Dark2'
    ],
}

# ============================================================================
# PLOT SETTINGS DEFAULTS
# ============================================================================

PLOT_DEFAULTS: Dict[str, any] = {
    'figure_size': (10, 8),
    'dpi': 100,
    'marker_size': 20,
    'marker_alpha': 0.6,
    'line_width': 1.5,
    'grid_alpha': 0.3,
    'title_fontsize': 14,
    'label_fontsize': 12,
    'tick_fontsize': 10,
    'legend_fontsize': 10,
    'colorbar_fontsize': 10,
    'default_colormap': 'viridis',
}

# ============================================================================
# PLOT STYLE PRESETS
# ============================================================================

PLOT_STYLES: Dict[str, Dict[str, any]] = {
    'default': {
        'style': 'seaborn-v0_8-darkgrid',
        'marker': 'o',
        'edgecolor': 'black',
        'linewidth': 0.5,
    },
    'presentation': {
        'style': 'seaborn-v0_8-talk',
        'marker': 'o',
        'edgecolor': 'white',
        'linewidth': 1.0,
    },
    'publication': {
        'style': 'seaborn-v0_8-paper',
        'marker': 's',
        'edgecolor': 'black',
        'linewidth': 0.3,
    },
    'minimal': {
        'style': 'seaborn-v0_8-white',
        'marker': '.',
        'edgecolor': 'none',
        'linewidth': 0,
    }
}

# ============================================================================
# STATISTICAL PLOT SETTINGS
# ============================================================================

STATISTICAL_PLOT_SETTINGS: Dict[str, any] = {
    'histogram': {
        'bins': 30,
        'alpha': 0.7,
        'edgecolor': 'black',
        'linewidth': 0.5,
    },
    'kde': {
        'linewidth': 2,
        'alpha': 0.8,
        'fill': True,
        'fill_alpha': 0.3,
    },
    'marginal': {
        'size': 0.2,  # Fraction of figure for marginal plots
        'space': 0.02,  # Space between main and marginal
    }
}

# ============================================================================
# VISUALIZATION CONFIGURATION CLASS
# ============================================================================

class VisualizationConfig:
    """
    Configuration manager for visualization and plotting.
    
    Provides centralized access to colormap, styling, and
    plot configuration parameters.
    """
    
    @staticmethod
    def get_colormap_categories() -> Dict[str, Union[Dict[str, List[str]], List[str]]]:
        """
        Get all colormap categories.
        
        Returns:
            Dict containing colormap categories and their options
        """
        return COLORMAP_CATEGORIES.copy()
    
    @staticmethod
    def get_colormap_options(category: str, subcategory: Optional[str] = None) -> List[str]:
        """
        Get available colormaps for a category and optional subcategory.
        
        Args:
            category: Main category ('Sequential', 'Diverging', 'Qualitative')
            subcategory: Optional subcategory for Sequential colormaps
            
        Returns:
            List of colormap names
        """
        if category not in COLORMAP_CATEGORIES:
            return ['viridis']
        
        if category == 'Sequential' and subcategory:
            return COLORMAP_CATEGORIES[category].get(subcategory, ['viridis'])
        
        if category == 'Sequential':
            all_maps: List[str] = []
            for maps in COLORMAP_CATEGORIES[category].values():
                all_maps.extend(maps)
            return all_maps
        
        return COLORMAP_CATEGORIES[category]
    
    @staticmethod
    def get_sequential_subcategories() -> List[str]:
        """
        Get list of subcategories for sequential colormaps.
        
        Returns:
            List of subcategory names
        """
        return list(COLORMAP_CATEGORIES['Sequential'].keys())
    
    @staticmethod
    def validate_colormap(colormap_name: str) -> bool:
        """
        Check if colormap exists in Matplotlib.
        
        Args:
            colormap_name: Name of colormap to validate
            
        Returns:
            True if colormap exists, False otherwise
        """
        try:
            plt.colormaps[colormap_name]
            return True
        except KeyError:
            return False
    
    @staticmethod
    def apply_colormap_reversal(colormap_name: str, reverse: bool = False) -> str:
        """
        Apply or remove reversal suffix to colormap name.
        
        Args:
            colormap_name: Base colormap name
            reverse: Whether to reverse the colormap
            
        Returns:
            Colormap name with appropriate suffix
        """
        if reverse and not colormap_name.endswith('_r'):
            return f"{colormap_name}_r"
        if not reverse and colormap_name.endswith('_r'):
            return colormap_name[:-2]
        return colormap_name
    
    @staticmethod
    def get_plot_defaults() -> Dict[str, any]:
        """
        Get default plot settings.
        
        Returns:
            Dict containing default plot parameters
        """
        return PLOT_DEFAULTS.copy()
    
    @staticmethod
    def get_plot_style(style_name: str = 'default') -> Dict[str, any]:
        """
        Get plot style preset.
        
        Args:
            style_name: Name of style preset
            
        Returns:
            Dict containing style parameters
        """
        if style_name not in PLOT_STYLES:
            return PLOT_STYLES['default'].copy()
        return PLOT_STYLES[style_name].copy()
    
    @staticmethod
    def get_statistical_settings(plot_type: str) -> Dict[str, any]:
        """
        Get settings for statistical plots.
        
        Args:
            plot_type: Type of plot ('histogram', 'kde', 'marginal')
            
        Returns:
            Dict containing plot-specific settings
        """
        if plot_type not in STATISTICAL_PLOT_SETTINGS:
            return {}
        return STATISTICAL_PLOT_SETTINGS[plot_type].copy()
    
    @staticmethod
    def get_available_styles() -> List[str]:
        """
        Get list of available plot style presets.
        
        Returns:
            List of style names
        """
        return list(PLOT_STYLES.keys())
