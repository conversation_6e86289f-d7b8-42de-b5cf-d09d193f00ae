import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, MagicMock, call
import tkinter as tk
from tkinter import messagebox
import lasio

import matplotlib.pyplot as plt

# Import the functions to test
from Xplot_HIST_KDE_FUNCT_Init import (
    get_colormap_options, get_sequential_subcategories, validate_colormap,
    apply_colormap_reversal, analyze_log_availability, validate_calculation_inputs,
    interpolate_nan, interpolate_class, validate_data_for_plotting,
    get_robust_limits, find_default_columns, filter_excel_data_for_las_wells,
    COLORMAP_CATEGORIES, log_keywords
)


class TestColormapFunctions:
    """Test colormap-related utility functions."""
    
    def test_get_colormap_options_sequential_with_subcategory(self):
        """Test getting sequential colormap options with subcategory."""
        result = get_colormap_options("Sequential", "Perceptually Uniform")
        expected = ['viridis', 'plasma', 'inferno', 'magma', 'cividis']
        assert result == expected
    
    def test_get_colormap_options_sequential_without_subcategory(self):
        """Test getting all sequential colormap options."""
        result = get_colormap_options("Sequential")
        # Should return all sequential colormaps
        assert 'viridis' in result
        assert 'Blues' in result
        assert 'rainbow' in result
        assert len(result) > 10  # Should have many colormaps
    
    def test_get_colormap_options_diverging(self):
        """Test getting diverging colormap options."""
        result = get_colormap_options("Diverging")
        expected = COLORMAP_CATEGORIES['Diverging']
        assert result == expected
    
    def test_get_colormap_options_invalid_category(self):
        """Test getting colormap options for invalid category."""
        result = get_colormap_options("InvalidCategory")
        assert result == ['viridis']  # Should return fallback
    
    def test_get_sequential_subcategories(self):
        """Test getting sequential subcategories."""
        result = get_sequential_subcategories()
        expected = ['Perceptually Uniform', 'Single Hue', 'Multi Hue']
        assert result == expected
    
    @patch('matplotlib.pyplot.colormaps')
    def test_validate_colormap_valid(self, mock_colormaps):
        """Test validating a valid colormap."""
        mock_colormaps.__getitem__.return_value = Mock()
        result = validate_colormap('viridis')
        assert result is True
    
    @patch('matplotlib.pyplot.colormaps')
    def test_validate_colormap_invalid(self, mock_colormaps):
        """Test validating an invalid colormap."""
        mock_colormaps.__getitem__.side_effect = KeyError("Invalid colormap")
        result = validate_colormap('invalid_colormap')
        assert result is False
    
    def test_apply_colormap_reversal_add_reverse(self):
        """Test applying reversal to a colormap."""
        result = apply_colormap_reversal('viridis', reverse=True)
        assert result == 'viridis_r'
    
    def test_apply_colormap_reversal_remove_reverse(self):
        """Test removing reversal from a colormap."""
        result = apply_colormap_reversal('viridis_r', reverse=False)
        assert result == 'viridis'
    
    def test_apply_colormap_reversal_no_change(self):
        """Test no change when reversal matches current state."""
        result = apply_colormap_reversal('viridis', reverse=False)
        assert result == 'viridis'


class TestDataValidationFunctions:
    """Test data validation and processing functions."""
    
    def test_interpolate_nan_sufficient_data(self):
        """Test interpolating NaN values with sufficient data."""
        depth = np.array([1, 2, 3, 4, 5])
        data = np.array([10, np.nan, 30, np.nan, 50])
        result = interpolate_nan(depth, data)
        
        # Should interpolate the NaN values
        assert not np.isnan(result).any()
        assert result[0] == 10
        assert result[2] == 30
        assert result[4] == 50
        # Check interpolated values are reasonable
        assert 10 < result[1] < 30
        assert 30 < result[3] < 50
    
    def test_interpolate_nan_insufficient_data(self):
        """Test interpolating NaN values with insufficient data."""
        depth = np.array([1, 2, 3])
        data = np.array([np.nan, np.nan, np.nan])
        result = interpolate_nan(depth, data)
        
        # Should return original data unchanged
        np.testing.assert_array_equal(result, data)
    
    def test_interpolate_class_valid_data(self):
        """Test interpolating class values with valid data."""
        depth = np.array([1, 2, 3, 4, 5])
        class_data = np.array([1, np.nan, np.nan, 2, np.nan])
        result = interpolate_class(depth, class_data)
        
        # Should use nearest neighbor interpolation
        assert not np.isnan(result).any()
        assert result[0] == 1
        assert result[3] == 2
    
    def test_interpolate_class_no_valid_data(self):
        """Test interpolating class values with no valid data."""
        depth = np.array([1, 2, 3])
        class_data = np.array([np.nan, np.nan, np.nan])
        result = interpolate_class(depth, class_data)
        
        # Should return original data unchanged
        np.testing.assert_array_equal(result, class_data)
    
    def test_get_robust_limits_normal_data(self):
        """Test robust limits calculation with normal data."""
        data = np.array([1, 2, 3, 4, 5, 100])  # Outlier at 100
        min_val, max_val = get_robust_limits(data, padding_percent=10)
        
        assert min_val < 1
        assert max_val > 100
        assert min_val is not None
        assert max_val is not None
    
    def test_get_robust_limits_single_value(self):
        """Test robust limits calculation with single unique value."""
        data = np.array([5, 5, 5, 5])
        min_val, max_val = get_robust_limits(data)
        
        assert min_val < 5
        assert max_val > 5
        assert max_val - min_val > 0
    
    def test_get_robust_limits_empty_data(self):
        """Test robust limits calculation with empty data."""
        data = np.array([])
        min_val, max_val = get_robust_limits(data)
        
        assert min_val is None
        assert max_val is None
    
    def test_find_default_columns(self):
        """Test finding default columns from LAS file."""
        # Create mock LAS file
        mock_las = Mock()
        mock_curve1 = Mock()
        mock_curve1.mnemonic = 'DT'
        mock_curve2 = Mock()
        mock_curve2.mnemonic = 'RHOB'
        mock_curve3 = Mock()
        mock_curve3.mnemonic = 'PHIE'
        
        mock_las.curves = [mock_curve1, mock_curve2, mock_curve3]
        
        # Test with subset of log_keywords
        test_keywords = {
            'DT': ['DT', 'DTCO'],
            'RHOB': ['RHOB', 'DEN'],
            'MISSING': ['MISSING_LOG']
        }
        
        result = find_default_columns(mock_las, test_keywords)
        
        assert result['DT'] == 'DT'
        assert result['RHOB'] == 'RHOB'
        assert result['MISSING'] is None


class TestAnalyzeLogAvailability:
    """Test log availability analysis."""
    
    def test_analyze_log_availability_common_logs(self):
        """Test analyzing log availability with common logs."""
        # Create mock LAS files
        mock_las1 = Mock()
        mock_las1.well.WELL.value = 'Well1'
        mock_las1.curves.keys.return_value = ['DEPTH', 'DT', 'RHOB']
        
        mock_las2 = Mock()
        mock_las2.well.WELL.value = 'Well2'
        mock_las2.curves.keys.return_value = ['DEPTH', 'DT', 'GR']
        
        las_files = [mock_las1, mock_las2]
        result = analyze_log_availability(las_files)
        
        assert result['total_wells'] == 2
        assert 'DEPTH' in result['common_logs']
        assert 'DT' in result['common_logs']
        assert 'RHOB' in result['partial_logs']
        assert 'GR' in result['partial_logs']
        assert result['partial_logs']['RHOB']['count'] == 1
        assert result['partial_logs']['GR']['count'] == 1
    
    def test_analyze_log_availability_no_common_logs(self):
        """Test analyzing log availability with no common logs."""
        mock_las1 = Mock()
        mock_las1.well.WELL.value = 'Well1'
        mock_las1.curves.keys.return_value = ['DT', 'RHOB']
        
        mock_las2 = Mock()
        mock_las2.well.WELL.value = 'Well2'
        mock_las2.curves.keys.return_value = ['GR', 'NPHI']
        
        las_files = [mock_las1, mock_las2]
        result = analyze_log_availability(las_files)
        
        assert result['total_wells'] == 2
        assert len(result['common_logs']) == 0
        assert len(result['partial_logs']) == 4


class TestValidateCalculationInputs:
    """Test calculation input validation."""
    
    def test_validate_calculation_inputs_valid(self):
        """Test validation with valid calculation inputs."""
        # Create mock LAS files with common curves
        mock_las1 = Mock()
        mock_las1.curves.keys.return_value = ['DEPTH', 'DT', 'RHOB']
        mock_las2 = Mock()
        mock_las2.curves.keys.return_value = ['DEPTH', 'DT', 'RHOB']
        
        las_files = [mock_las1, mock_las2]
        calculation_text = "AI = RHOB * (304800/DT)"
        
        result = validate_calculation_inputs(las_files, calculation_text)
        
        assert result['valid'] is True
        assert len(result['missing_logs']) == 0
        assert 'DT' in result['available_logs']
        assert 'RHOB' in result['available_logs']
    
    def test_validate_calculation_inputs_missing_logs(self):
        """Test validation with missing input logs."""
        # Create mock LAS files with different curves
        mock_las1 = Mock()
        mock_las1.well.WELL.value = 'Well1'
        mock_las1.curves.keys.return_value = ['DEPTH', 'DT']
        mock_las2 = Mock()
        mock_las2.well.WELL.value = 'Well2'
        mock_las2.curves.keys.return_value = ['DEPTH', 'RHOB']
        
        las_files = [mock_las1, mock_las2]
        calculation_text = "AI = RHOB * DT"
        
        result = validate_calculation_inputs(las_files, calculation_text)
        
        assert result['valid'] is False
        assert len(result['missing_logs']) > 0
        assert 'Missing Input Logs' in result['error_details']
    
    def test_validate_calculation_inputs_output_variables(self):
        """Test validation correctly identifies output variables."""
        mock_las1 = Mock()
        mock_las1.curves.keys.return_value = ['DEPTH', 'DT', 'RHOB']
        mock_las2 = Mock()
        mock_las2.curves.keys.return_value = ['DEPTH', 'DT', 'RHOB']
        
        las_files = [mock_las1, mock_las2]
        calculation_text = """
        AI = RHOB * (304800/DT)
        VP_VS_RATIO = DT / DTS
        """
        
        result = validate_calculation_inputs(las_files, calculation_text)
        
        # Should fail because DTS is not available, but AI and VP_VS_RATIO are output variables
        assert result['valid'] is False
        assert 'DTS' in result['error_details']


class TestFilterExcelData:
    """Test Excel data filtering functions."""
    
    def test_filter_excel_data_for_las_wells_matching_wells(self):
        """Test filtering Excel data for matching wells."""
        # Create sample Excel data
        df = pd.DataFrame({
            'Well': ['Well1', 'Well2', 'Well3', 'Well1'],
            'Surface': ['Top', 'Top', 'Top', 'Bottom'],
            'MD': [1000, 1500, 2000, 1200]
        })
        
        # Create mock LAS files
        mock_las1 = Mock()
        mock_las1.well.WELL.value = 'Well1'
        mock_las2 = Mock()
        mock_las2.well.WELL.value = 'Well2'
        
        las_files = [mock_las1, mock_las2]
        
        result = filter_excel_data_for_las_wells(df, las_files)
        
        assert result is not None
        assert len(result) == 3  # Well1 (2 entries) + Well2 (1 entry)
        assert 'Well3' not in result['Well'].values
        assert 'Well1' in result['Well'].values
        assert 'Well2' in result['Well'].values
    
    def test_filter_excel_data_for_las_wells_no_matching(self):
        """Test filtering Excel data with no matching wells."""
        df = pd.DataFrame({
            'Well': ['Well3', 'Well4'],
            'Surface': ['Top', 'Top'],
            'MD': [1000, 1500]
        })
        
        mock_las1 = Mock()
        mock_las1.well.WELL.value = 'Well1'
        mock_las2 = Mock()
        mock_las2.well.WELL.value = 'Well2'
        
        las_files = [mock_las1, mock_las2]
        
        result = filter_excel_data_for_las_wells(df, las_files)
        
        assert result is None
    
    def test_filter_excel_data_for_las_wells_none_input(self):
        """Test filtering Excel data with None input."""
        mock_las = Mock()
        las_files = [mock_las]
        
        result = filter_excel_data_for_las_wells(None, las_files)
        
        assert result is None


class TestValidateDataForPlotting:
    """Test data validation for plotting."""
    
    def create_mock_las_file(self, well_name, curves_data):
        """Helper to create mock LAS file."""
        mock_las = Mock()
        mock_las.well.WELL.value = well_name
        
        # Mock curves
        mock_curves = Mock()
        mock_curves.keys.return_value = list(curves_data.keys())
        mock_curves.__contains__ = lambda self, key: key in curves_data
        mock_las.curves = mock_curves
        
        # Mock data access
        def getitem(key):
            if key in curves_data:
                mock_curve = Mock()
                mock_curve.data = curves_data[key]
                return mock_curve
            raise KeyError(key)
        
        mock_las.__getitem__ = getitem
        return mock_las
    
    def test_validate_data_for_plotting_valid_data(self):
        """Test validation with valid plotting data."""
        # Create mock LAS file with valid data
        curves_data = {
            'DEPTH': np.array([1000, 1001, 1002, 1003, 1004]),
            'DT': np.array([100, 105, 110, 115, 120]),
            'RHOB': np.array([2.2, 2.3, 2.4, 2.5, 2.6])
        }
        
        mock_las = self.create_mock_las_file('Well1', curves_data)
        las_files = [mock_las]
        
        depth_ranges = {'Well1': (1000, 1004)}
        
        result = validate_data_for_plotting(las_files, 'DT', 'RHOB', None, depth_ranges)
        
        assert result['valid'] is True
        assert len(result['issues']) == 0
        assert result['stats']['total_points'] == 5
        assert result['stats']['valid_points'] == 5
        assert 'Well1' in result['stats']['wells_with_data']
    
    def test_validate_data_for_plotting_missing_curves(self):
        """Test validation with missing curves."""
        curves_data = {
            'DEPTH': np.array([1000, 1001, 1002]),
            'DT': np.array([100, 105, 110])
            # Missing RHOB
        }
        
        mock_las = self.create_mock_las_file('Well1', curves_data)
        las_files = [mock_las]
        
        depth_ranges = {'Well1': (1000, 1002)}
        
        result = validate_data_for_plotting(las_files, 'DT', 'RHOB', None, depth_ranges)
        
        assert result['valid'] is True  # Should still be valid but with warnings
        assert len(result['warnings']) > 0
        assert 'Well1' in result['stats']['wells_without_data']
    
    def test_validate_data_for_plotting_nan_values(self):
        """Test validation with NaN values in data."""
        curves_data = {
            'DEPTH': np.array([1000, 1001, 1002, 1003, 1004]),
            'DT': np.array([100, np.nan, np.nan, 115, 120]),
            'RHOB': np.array([2.2, 2.3, np.nan, np.nan, 2.6])
        }
        
        mock_las = self.create_mock_las_file('Well1', curves_data)
        las_files = [mock_las]
        
        depth_ranges = {'Well1': (1000, 1004)}
        
        result = validate_data_for_plotting(las_files, 'DT', 'RHOB', None, depth_ranges)
        
        assert result['valid'] is True
        # Should have fewer valid points due to NaN values
        assert result['stats']['valid_points'] < result['stats']['total_points']
    
    def test_validate_data_for_plotting_no_depth_range(self):
        """Test validation with missing depth range."""
        curves_data = {
            'DEPTH': np.array([1000, 1001, 1002]),
            'DT': np.array([100, 105, 110]),
            'RHOB': np.array([2.2, 2.3, 2.4])
        }
        
        mock_las = self.create_mock_las_file('Well1', curves_data)
        las_files = [mock_las]
        
        depth_ranges = {}  # No depth range for Well1
        
        result = validate_data_for_plotting(las_files, 'DT', 'RHOB', None, depth_ranges)
        
        assert len(result['warnings']) > 0
        assert 'Well1' in result['stats']['wells_without_data']


class TestLogKeywords:
    """Test the log_keywords global variable."""
    
    def test_log_keywords_structure(self):
        """Test that log_keywords has expected structure."""
        assert isinstance(log_keywords, dict)
        assert 'DT' in log_keywords
        assert 'RHOB' in log_keywords
        assert 'DEPTH' in log_keywords
        
        # Test that each entry is a list
        for key, aliases in log_keywords.items():
            assert isinstance(aliases, list)
            assert len(aliases) > 0
            # Test that all aliases are strings
            for alias in aliases:
                assert isinstance(alias, str)
    
    def test_log_keywords_common_logs(self):
        """Test that common log types are present."""
        expected_logs = ['DT', 'DTS', 'RHOB', 'PHIT', 'PHIE', 'GR', 'NPHI']
        for log_type in expected_logs:
            assert log_type in log_keywords
    
    def test_log_keywords_aliases(self):
        """Test that aliases contain expected variations."""
        # DT should include common P-wave slowness mnemonics
        assert 'DT' in log_keywords['DT']
        assert 'DTCO' in log_keywords['DT']
        
        # RHOB should include common density mnemonics
        assert 'RHOB' in log_keywords['RHOB']
        assert 'DEN' in log_keywords['RHOB']


@pytest.fixture
def sample_las_files():
    """Fixture providing sample LAS files for testing."""
    # Create mock LAS files
    mock_las1 = Mock()
    mock_las1.well.WELL.value = 'Well1'
    
    mock_las2 = Mock()
    mock_las2.well.WELL.value = 'Well2'
    
    return [mock_las1, mock_las2]


@pytest.fixture
def sample_depth_ranges():
    """Fixture providing sample depth ranges."""
    return {
        'Well1': (1000, 1500),
        'Well2': (2000, 2500)
    }


class TestColormapCategories:
    """Test the COLORMAP_CATEGORIES global variable."""
    
    def test_colormap_categories_structure(self):
        """Test that COLORMAP_CATEGORIES has expected structure."""
        assert isinstance(COLORMAP_CATEGORIES, dict)
        assert 'Sequential' in COLORMAP_CATEGORIES
        assert 'Diverging' in COLORMAP_CATEGORIES
        assert 'Qualitative' in COLORMAP_CATEGORIES
    
    def test_sequential_subcategories(self):
        """Test Sequential category has subcategories."""
        sequential = COLORMAP_CATEGORIES['Sequential']
        assert isinstance(sequential, dict)
        assert 'Perceptually Uniform' in sequential
        assert 'Single Hue' in sequential
        assert 'Multi Hue' in sequential
    
    def test_colormap_lists(self):
        """Test that colormap lists contain expected colormaps."""
        # Test some common colormaps are present
        perceptually_uniform = COLORMAP_CATEGORIES['Sequential']['Perceptually Uniform']
        assert 'viridis' in perceptually_uniform
        assert 'plasma' in perceptually_uniform
        
        diverging = COLORMAP_CATEGORIES['Diverging']
        assert 'coolwarm' in diverging
        assert 'RdBu' in diverging


# Integration tests for functions that work together
class TestIntegrationTests:
    """Integration tests for related functions."""
    
    def test_colormap_workflow(self):
        """Test complete colormap selection workflow."""
        # Test getting options
        categories = list(COLORMAP_CATEGORIES.keys())
        assert len(categories) > 0
        
        # Test getting subcategories
        subcategories = get_sequential_subcategories()
        assert len(subcategories) > 0
        
        # Test getting specific options
        options = get_colormap_options('Sequential', subcategories[0])
        assert len(options) > 0
        
        # Test applying reversal
        reversed_cmap = apply_colormap_reversal(options[0], True)
        assert reversed_cmap.endswith('_r')
    
    @patch('matplotlib.pyplot.colormaps')
    def test_robust_limits_with_real_data(self, mock_colormaps):
        """Test robust limits calculation with realistic data."""
        # Simulate realistic log data with outliers
        np.random.seed(42)
        normal_data = np.random.normal(100, 10, 1000)
        outliers = np.array([200, 300, -50])
        data = np.concatenate([normal_data, outliers])
        
        min_val, max_val = get_robust_limits(data, padding_percent=5)
        
        # Should handle outliers reasonably
        assert min_val < np.min(normal_data)
        assert max_val > np.max(normal_data)
        # But shouldn't be dominated by extreme outliers
        assert max_val < 400  # Much less than extreme outlier