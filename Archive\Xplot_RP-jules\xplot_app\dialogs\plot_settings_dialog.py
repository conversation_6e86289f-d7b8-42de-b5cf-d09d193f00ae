import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import numpy as np
import matplotlib.pyplot as plt # For colormap access
from typing import List, Dict, Optional, Tuple, Any # Any for lasio.LASFile, pd.DataFrame

# Assuming these are available from the new structure
from config import COLORMAP_CATEGORIES, get_colormap_options, get_sequential_subcategories, validate_colormap, apply_colormap_reversal
from processing import get_robust_limits
from gui_utils import create_scrollable_frame # Added import

class PlotSettingsDialog(tk.Toplevel):
    """
    Dialog for configuring various plot settings.
    """

    def __init__(self, parent: tk.Widget, 
                 las_files: List[Any], # lasio.LASFile
                 x_col: str, y_col: str, 
                 class_col: Optional[str], 
                 depth_ranges: Dict[str, Tuple[float, float]], 
                 z_col: Optional[str] = None) -> None:
        super().__init__(parent)
        self.title("Plot Settings")
        # Initial size, might need adjustment based on content
        self.geometry("700x750") 

        self.las_files = las_files
        self.x_col = x_col
        self.y_col = y_col
        self.class_col = class_col
        self.depth_ranges = depth_ranges
        self.z_col = z_col
        
        self.settings: Dict[str, Any] = {}

        self._initial_x_min, self._initial_x_max = self._calculate_initial_axis_limits(x_col)
        self._initial_y_min, self._initial_y_max = self._calculate_initial_axis_limits(y_col)

        self._build_ui()

        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
        # self.wait_window(self) # Make it blocking by calling this in get_selected_settings

    def _calculate_initial_axis_limits(self, col_name: str) -> Tuple[Optional[float], Optional[float]]:
        """Calculates initial min/max for a given column based on selected depth ranges."""
        all_data_for_col = []
        if self.depth_ranges:
            for las in self.las_files:
                well_name = las.well.WELL.value if las.well.WELL else "UnknownWell"
                if well_name in self.depth_ranges and col_name in las.curves: # Check if col_name is in las.curves
                    top_depth, bottom_depth = self.depth_ranges[well_name]
                    depth_curve_data = las.curves.get('DEPTH', None) # Use .get for safety
                    if depth_curve_data is None: continue
                    
                    depth = np.array(depth_curve_data.data)
                    # Ensure col_name is a valid curve before accessing data
                    if col_name not in las: continue # Check if curve mnemonic exists
                    col_data = np.array(las[col_name].data)
                    
                    # Ensure all arrays are not empty and have consistent lengths for masking
                    if not (depth.size > 0 and col_data.size > 0 and depth.size == col_data.size):
                        print(f"Warning: Skipping {well_name} for {col_name} due to data inconsistency or empty arrays.")
                        continue

                    mask = (depth >= top_depth) & (depth <= bottom_depth) & (~np.isnan(col_data))
                    all_data_for_col.extend(col_data[mask])
        
        if not all_data_for_col:
            return None, None
        
        return float(np.nanmin(all_data_for_col)), float(np.nanmax(all_data_for_col))


    def _build_ui(self) -> None:
        """Builds the main UI components of the dialog."""
        # Use the new utility function to create a scrollable frame
        # The dialog 'self' is the parent for the scrollable area.
        # self.scrollable_frame is where all other widgets will be placed.
        self.scrollable_frame, _ = create_scrollable_frame(self)

        current_row = 0

        general_frame = ttk.LabelFrame(self.scrollable_frame, text="General Plot Settings", padding="10")
        general_frame.grid(row=current_row, column=0, columnspan=2, sticky="ew", padx=10, pady=5)
        current_row += 1

        ttk.Label(general_frame, text="Point Size:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.point_size_var = tk.StringVar(value="50")
        ttk.Entry(general_frame, textvariable=self.point_size_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(general_frame, text="Histogram Bins:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.bin_size_var = tk.StringVar(value="30")
        ttk.Entry(general_frame, textvariable=self.bin_size_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(general_frame, text="Marginal Plot Type:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.marginal_plot_type_var = tk.StringVar(value="Both")
        self.marginal_options_combo = ttk.Combobox(general_frame, textvariable=self.marginal_plot_type_var, 
                                                   values=["Histogram", "KDE", "Both", "None"], state="readonly", width=15) # Added None
        self.marginal_options_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        if not self.class_col or self.z_col: 
            self.marginal_options_combo.config(state=tk.DISABLED)
            self.marginal_plot_type_var.set("None (disabled)")


        ttk.Label(general_frame, text="Downsampling Factor (1=None):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.downsampling_factor_var = tk.StringVar(value="1")
        ttk.Entry(general_frame, textvariable=self.downsampling_factor_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        legend_frame = ttk.LabelFrame(self.scrollable_frame, text="Legend Settings", padding="10")
        legend_frame.grid(row=current_row, column=0, columnspan=2, sticky="ew", padx=10, pady=5)
        current_row += 1
        
        ttk.Label(legend_frame, text="Legend Style:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.legend_style_var = tk.StringVar()
        legend_style_options = ["Well-Class", "Class-Only", "Simple"] if self.class_col and not self.z_col else ["Simple"]
        self.legend_style_combo = ttk.Combobox(legend_frame, textvariable=self.legend_style_var, 
                                               values=legend_style_options, state="readonly", width=15)
        self.legend_style_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        self.legend_style_var.set(legend_style_options[0])

        ttk.Label(legend_frame, text="Legend Font Size:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.legend_font_size_var = tk.StringVar(value="10")
        ttk.Entry(legend_frame, textvariable=self.legend_font_size_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(legend_frame, text="Legend Font Weight:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.legend_font_weight_var = tk.StringVar(value="normal")
        ttk.Combobox(legend_frame, textvariable=self.legend_font_weight_var, 
                     values=["normal", "bold", "heavy", "light"], state="readonly", width=15).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        self.symbol_vars: Dict[Any, tk.StringVar] = {}
        self.color_vars: Dict[Any, tk.StringVar] = {}
        self.class_names_vars: Dict[Any, tk.StringVar] = {}

        if self.class_col and not self.z_col:
            class_frame = ttk.LabelFrame(self.scrollable_frame, text="Class Customization", padding="10")
            class_frame.grid(row=current_row, column=0, columnspan=2, sticky="ew", padx=10, pady=5)
            current_row +=1
            ttk.Label(class_frame, text="(Class symbol/color customization UI - Placeholder)").pack()


        if self.z_col:
            z_axis_frame = ttk.LabelFrame(self.scrollable_frame, text="Z-Axis Colormap Settings", padding="10")
            z_axis_frame.grid(row=current_row, column=0, columnspan=2, sticky="ew", padx=10, pady=5)
            current_row += 1
            ttk.Label(z_axis_frame, text="(Z-axis colormap settings UI - Placeholder)").pack()

        axis_frame = ttk.LabelFrame(self.scrollable_frame, text="Axis Titles & Limits", padding="10")
        axis_frame.grid(row=current_row, column=0, columnspan=2, sticky="ew", padx=10, pady=5)
        current_row += 1

        ttk.Label(axis_frame, text="X-Axis Title:").grid(row=0, column=0, sticky=tk.W)
        self.x_title_text_var = tk.StringVar(value=self.x_col)
        ttk.Entry(axis_frame, textvariable=self.x_title_text_var, width=20).grid(row=0, column=1, sticky=tk.W)
        ttk.Label(axis_frame, text="Min:").grid(row=0, column=2, sticky=tk.W)
        self.x_min_var = tk.StringVar(value=f"{self._initial_x_min:.2f}" if self._initial_x_min is not None else "")
        ttk.Entry(axis_frame, textvariable=self.x_min_var, width=10).grid(row=0, column=3, sticky=tk.W)
        ttk.Label(axis_frame, text="Max:").grid(row=0, column=4, sticky=tk.W)
        self.x_max_var = tk.StringVar(value=f"{self._initial_x_max:.2f}" if self._initial_x_max is not None else "")
        ttk.Entry(axis_frame, textvariable=self.x_max_var, width=10).grid(row=0, column=5, sticky=tk.W)

        ttk.Label(axis_frame, text="Y-Axis Title:").grid(row=1, column=0, sticky=tk.W)
        self.y_title_text_var = tk.StringVar(value=self.y_col)
        ttk.Entry(axis_frame, textvariable=self.y_title_text_var, width=20).grid(row=1, column=1, sticky=tk.W)
        ttk.Label(axis_frame, text="Min:").grid(row=1, column=2, sticky=tk.W)
        self.y_min_var = tk.StringVar(value=f"{self._initial_y_min:.2f}" if self._initial_y_min is not None else "")
        ttk.Entry(axis_frame, textvariable=self.y_min_var, width=10).grid(row=1, column=3, sticky=tk.W)
        ttk.Label(axis_frame, text="Max:").grid(row=1, column=4, sticky=tk.W)
        self.y_max_var = tk.StringVar(value=f"{self._initial_y_max:.2f}" if self._initial_y_max is not None else "")
        ttk.Entry(axis_frame, textvariable=self.y_max_var, width=10).grid(row=1, column=5, sticky=tk.W)
        
        ttk.Label(axis_frame, text="Tick Label Font Size:").grid(row=2, column=0, sticky=tk.W, pady=(10,0))
        self.tick_label_size_var = tk.StringVar(value="10")
        ttk.Entry(axis_frame, textvariable=self.tick_label_size_var, width=10).grid(row=2, column=1, sticky=tk.W, pady=(10,0))

        submit_button = ttk.Button(self.scrollable_frame, text="Apply Settings", command=self._on_submit)
        submit_button.grid(row=current_row, column=0, columnspan=2, pady=20)


    def _on_submit(self) -> None:
        """Collects all settings into the self.settings dictionary and closes."""
        try:
            self.settings['point_size'] = float(self.point_size_var.get())
            self.settings['bin_size'] = int(self.bin_size_var.get())
            if self.class_col and not self.z_col : 
                 self.settings['marginal_plot_type'] = self.marginal_plot_type_var.get()
            else:
                 self.settings['marginal_plot_type'] = "None"
            
            self.settings['legend_style'] = self.legend_style_var.get()
            self.settings['legend_font_size'] = float(self.legend_font_size_var.get())
            self.settings['legend_font_weight'] = self.legend_font_weight_var.get()
            
            self.settings['downsampling_factor'] = int(self.downsampling_factor_var.get())
            if self.settings['downsampling_factor'] < 1:
                messagebox.showerror("Input Error", "Downsampling factor must be 1 or greater.", parent=self)
                return

            self.settings['x_title_text'] = self.x_title_text_var.get()
            self.settings['y_title_text'] = self.y_title_text_var.get()
            self.settings['x_axis_min_user'] = float(self.x_min_var.get()) if self.x_min_var.get() else None
            self.settings['x_axis_max_user'] = float(self.x_max_var.get()) if self.x_max_var.get() else None
            self.settings['y_axis_min_user'] = float(self.y_min_var.get()) if self.y_min_var.get() else None
            self.settings['y_axis_max_user'] = float(self.y_max_var.get()) if self.y_max_var.get() else None
            
            self.settings['tick_label_size'] = float(self.tick_label_size_var.get())

            self.settings['symbols'] = {k: v.get() for k, v in self.symbol_vars.items()}
            self.settings['colors'] = {k: v.get() for k, v in self.color_vars.items()}
            self.settings['class_names'] = {k: v.get() for k, v in self.class_names_vars.items()}
            
            if self.z_col:
                self.settings['colormap'] = 'viridis' 
                self.settings['reverse_colormap'] = False 
                self.settings['show_colorbar'] = True 
                self.settings['colorbar_label'] = self.z_col 
                # Add more z-col settings from UI variables here when implemented

            self.destroy()
        except ValueError:
            messagebox.showerror("Input Error", "Please enter valid numbers for numeric fields (e.g., Point Size, Font Sizes, Bins).", parent=self)
    
    def _on_cancel(self) -> None:
        """Handles dialog cancellation."""
        self.settings = {} 
        self.destroy()

    def get_plot_settings(self) -> Dict[str, Any]: # Renamed for clarity
        """Returns the dictionary of plot settings."""
        self.wait_window(self) # Make it modal and wait for close
        return self.settings

if __name__ == '__main__':
    from unittest.mock import Mock 
    
    root = tk.Tk()
    root.withdraw() 
    
    class MockLAS: # Simplified Mock for testing
        def __init__(self, name, curves_dict):
            self.well = Mock()
            self.well.WELL.value = name
            self.curves = Mock() # Mock the curves attribute itself
            self.curves.keys = lambda: list(curves_dict.keys())
            self._curves_data = curves_dict
            
            # Mock __getitem__ on the LAS object itself for las[col_name]
            def getitem_curve(key):
                if key in self._curves_data:
                    curve_mock = Mock()
                    curve_mock.data = self._curves_data[key]
                    return curve_mock
                raise KeyError(key)
            self.__getitem__ = getitem_curve

            # Mock __contains__ on the LAS object for `col_name in las`
            def contains_curve(key):
                return key in self._curves_data
            self.__contains__ = contains_curve
            
            # Also mock las.curves.get('DEPTH', None)
            def get_curve_from_curves_attr(key, default=None):
                if key in self._curves_data:
                    curve_mock = Mock()
                    curve_mock.data = self._curves_data[key]
                    curve_mock.mnemonic = key
                    return curve_mock
                return default
            self.curves.get = get_curve_from_curves_attr


    mock_las_files_list = [
        MockLAS("Well-A", {'DEPTH': np.array([1,2,3,4,5]), 'GR': np.array([10,20,15,25,30]), 'DT': np.array([50,55,52,58,54]), 'RHOB': np.array([2.1,2.2,2.1,2.3,2.2]), 'LITH': np.array([1,1,2,2,1])}),
        MockLAS("Well-B", {'DEPTH': np.array([1,2,3,4,5]), 'GR': np.array([12,22,17,27,32]), 'DT': np.array([51,56,53,59,55]), 'RHOB': np.array([2.2,2.3,2.2,2.4,2.3]), 'LITH': np.array([2,2,1,1,2])})
    ]
    mock_depth_ranges = {"Well-A": (1,5), "Well-B": (1,5)}

    print("Launching PlotSettingsDialog with Class Column...")
    dialog_class = PlotSettingsDialog(root, mock_las_files_list, "DT", "RHOB", "LITH", mock_depth_ranges, z_col=None)
    settings_class = dialog_class.get_plot_settings()
    if settings_class:
        print("Plot Settings (Class):", settings_class)
    else:
        print("Plot settings dialog (Class) cancelled.")

    print("\nLaunching PlotSettingsDialog with Z Column...")
    dialog_z = PlotSettingsDialog(root, mock_las_files_list, "DT", "RHOB", None, mock_depth_ranges, z_col="GR")
    settings_z = dialog_z.get_plot_settings()
    if settings_z:
        print("Plot Settings (Z-Col):", settings_z)
    else:
        print("Plot settings dialog (Z-Col) cancelled.")

    if root.winfo_exists():
        root.destroy()
