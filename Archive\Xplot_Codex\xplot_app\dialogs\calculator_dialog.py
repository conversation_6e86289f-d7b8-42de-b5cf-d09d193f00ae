"""Enhanced calculator dialog for entering custom log calculations."""

from __future__ import annotations

from typing import Optional, List, Any, Dict

import tkinter as tk
from tkinter import ttk, messagebox
import lasio
import processing


class CalculatorDialog(tk.Toplevel):
    """Enhanced dialog window for entering calculations with log availability info."""

    def __init__(self, parent: tk.Misc, las_files: Optional[List[lasio.LASFile]] = None) -> None:
        super().__init__(parent)
        self.title("Custom Log Calculator - Enhanced with Availability Info")
        self.geometry("1000x800")
        self.result: Optional[str] = None
        self.las_files = las_files or []
        self.log_analysis: Dict[str, Any] = {}
        self.common_columns: List[str] = []
        self._analyze_logs()
        self._build_ui()

    def _analyze_logs(self) -> None:
        """Analyze log availability across all LAS files."""
        if not self.las_files:
            return
        
        # Get columns that are present in all LAS files
        common_columns = set(self.las_files[0].curves.keys())
        for las in self.las_files[1:]:
            common_columns.intersection_update(las.curves.keys())
        self.common_columns = sorted(common_columns)
        
        # Get detailed log availability analysis
        self.log_analysis = processing.analyze_log_availability(self.las_files)

    def _build_ui(self) -> None:
        if not self.las_files:
            self._build_simple_ui()
            return
            
        # Create PanedWindow
        paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)

        # Left Frame: Available variables with availability info
        left_frame = ttk.Frame(paned, width=300)
        paned.add(left_frame, weight=0)

        # Right Frame: Instructions and Text widget
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)

        # Enhanced variable list with availability indicators
        variables_label = tk.Label(left_frame, text="Available Variables:", anchor='w', font=('Arial', 12, 'bold'))
        variables_label.pack(fill=tk.X, pady=(10, 5))

        # Add availability legend with enhanced clarity
        legend_frame = tk.Frame(left_frame)
        legend_frame.pack(fill=tk.X, pady=5)
        tk.Label(legend_frame, text="✅ Available in all wells (SAFE to use)",
                 fg='green', font=('Arial', 9, 'bold')).pack(anchor='w')
        tk.Label(legend_frame, text="⚠️ Available in some wells (WILL CAUSE ERRORS)",
                 fg='red', font=('Arial', 9, 'bold')).pack(anchor='w')

        # Add explanatory note
        note_frame = tk.Frame(left_frame)
        note_frame.pack(fill=tk.X, pady=(0, 5))
        note_text = "Note: Only use ✅ logs for calculations that must work across all wells.\nUsing ⚠️ logs will cause failures in wells where they're missing."
        tk.Label(note_frame, text=note_text, fg='darkblue', font=('Arial', 8),
                 justify=tk.LEFT, wraplength=280).pack(anchor='w')

        # Create listbox with scrollbar
        listbox_frame = tk.Frame(left_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.variables_listbox = tk.Listbox(listbox_frame, font=('Courier', 10))
        scrollbar_vars = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.variables_listbox.yview)
        self.variables_listbox.config(yscrollcommand=scrollbar_vars.set)

        # Populate listbox with availability indicators
        all_columns = sorted(set(self.log_analysis['common_logs'] + list(self.log_analysis['partial_logs'].keys())))
        for idx, col in enumerate(all_columns, start=1):
            if col in self.log_analysis['common_logs']:
                display_text = f"✅ ({idx:2d}) {col}"
                self.variables_listbox.insert(tk.END, display_text)
            else:
                wells_with_log = self.log_analysis['partial_logs'].get(col, {}).get('wells', [])
                well_count = len(wells_with_log)
                total_wells = self.log_analysis['total_wells']
                display_text = f"⚠️ ({idx:2d}) {col} [{well_count}/{total_wells} wells]"
                self.variables_listbox.insert(tk.END, display_text)

        self.variables_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_vars.pack(side=tk.RIGHT, fill=tk.Y)

        # Enhanced instructions with practical examples and clear safety guidelines
        instructions = (
            "🧮 CUSTOM LOG CALCULATOR\n\n"
            "⚠️ IMPORTANT: Only use logs marked with ✅ (available in all wells)\n"
            "Using ⚠️ logs will cause calculation failures!\n\n"
            "📋 SAFE CALCULATION EXAMPLES:\n"
            "PHIE_HC = PHIE * (1 - SWE)  # Effective porosity with hydrocarbons\n"
            "VP_VS_RATIO = (304800/DT) / (304800/DTS)  # Vp/Vs ratio\n"
            "AI = RHOB * (304800/DT)  # Acoustic impedance\n"
            "POISSON = 0.5 * ((VP_VS_RATIO**2 - 2) / (VP_VS_RATIO**2 - 1))\n"
            "NORMALIZED_GR = (GR - np.nanmin(GR)) / (np.nanmax(GR) - np.nanmin(GR))\n\n"
            "🔧 AVAILABLE FUNCTIONS:\n"
            "• Numpy: np.log(), np.sqrt(), np.exp(), np.sin(), np.cos(), etc.\n"
            "• Statistics: np.nanmin(), np.nanmax(), np.nanmean(), np.nanstd()\n"
            "• Math: Basic operators (+, -, *, /, **), parentheses for grouping\n\n"
            "💡 TIP: Use 'Check Log Availability' before submitting!"
        )

        instructions_label = tk.Label(right_frame, text=instructions, justify=tk.LEFT,
                                     font=('Arial', 10), fg='darkblue')
        instructions_label.pack(anchor='w', padx=10, pady=(10, 5))

        # Text widget for calculations with scrollbar
        text_frame = tk.Frame(right_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10)

        self.text = tk.Text(text_frame, width=80, height=20, font=('Courier', 11))
        scrollbar_text = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text.yview)
        self.text.config(yscrollcommand=scrollbar_text.set)

        self.text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_text.pack(side=tk.RIGHT, fill=tk.Y)

        # Enhanced button frame
        button_frame = tk.Frame(self)
        button_frame.pack(pady=15)

        # Enhanced buttons with better styling
        tk.Button(button_frame, text="Check Syntax",
                  command=self._on_check_syntax, bg='lightblue', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Check Log Availability",
                  command=self._on_check_availability, bg='lightyellow', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Submit Calculations",
                  command=self._on_submit, bg='lightgreen', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel",
                  command=self._on_cancel, bg='lightcoral', width=18, height=2).pack(side=tk.LEFT, padx=5)

    def _build_simple_ui(self) -> None:
        """Build simple UI when no LAS files are provided."""
        text = tk.Text(self, width=80, height=20)
        text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        btn_frame = ttk.Frame(self)
        btn_frame.pack(pady=5)
        ttk.Button(btn_frame, text="Submit", command=lambda: self._on_submit_simple(text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Cancel", command=self._on_cancel).pack(side=tk.LEFT, padx=5)
        self.text = text

    def _on_submit_simple(self, text_widget: tk.Text) -> None:
        """Handle submit for simple UI."""
        self.result = text_widget.get("1.0", tk.END).strip() or None
        self.destroy()

    def _on_submit(self) -> None:
        """Handle submit with validation."""
        calc_text = self.text.get("1.0", tk.END).strip()
        
        if not calc_text:
            self.result = None
            self.destroy()
            return
            
        # Validate inputs before accepting
        validation_result = processing.validate_calculation_inputs(self.las_files, calc_text)
        
        if not validation_result['valid']:
            # Show detailed error information
            retry = messagebox.askretrycancel(
                "Log Availability Error",
                validation_result['error_details'] + "\n\nWould you like to:\n\n"
                "• Retry: Modify your calculations to use only ✅ logs\n"
                "• Cancel: Skip calculator and continue with existing logs"
            )
            if not retry:
                self.result = None
                self.destroy()
            # If retry, keep dialog open
            return
            
        self.result = calc_text
        self.destroy()

    def _on_cancel(self) -> None:
        self.result = None
        self.destroy()

    def _on_check_syntax(self) -> None:
        """Check syntax of the calculation text."""
        calc_text = self.text.get("1.0", tk.END).strip()
        if not calc_text:
            messagebox.showwarning("Syntax Check", "Please enter some calculations first.")
            return
        
        try:
            # Try to compile the code to check for syntax errors
            compile(calc_text, '<string>', 'exec')
            messagebox.showinfo("Syntax Check", "✅ Syntax is valid!")
        except SyntaxError as e:
            messagebox.showerror("Syntax Error", f"❌ Syntax error found:\n\n{str(e)}")
        except Exception as e:
            messagebox.showerror("Syntax Error", f"❌ Error in code:\n\n{str(e)}")

    def _on_check_availability(self) -> None:
        """Check log availability for the calculation text."""
        calc_text = self.text.get("1.0", tk.END).strip()
        if not calc_text:
            messagebox.showwarning("Log Availability", "Please enter some calculations first.")
            return

        validation_result = processing.validate_calculation_inputs(self.las_files, calc_text)

        if validation_result['valid']:
            messagebox.showinfo("Log Availability",
                              "✅ All referenced logs are available in all wells!\n\n"
                              "Your calculations should work correctly.")
        else:
            messagebox.showerror("Log Availability Issues", validation_result['error_details'])

    def show(self) -> Optional[str]:
        """Show the dialog and return the result."""
        self.transient(self.master)
        self.grab_set()
        self.wait_window()
        return self.result
