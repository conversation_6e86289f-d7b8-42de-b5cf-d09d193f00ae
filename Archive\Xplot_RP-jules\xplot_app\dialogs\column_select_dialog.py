import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Optional, Any # Any for lasio.LASFile

# Assuming analyze_log_availability might be used by the caller (main.py)
# and common_columns are passed in, or we call it here.
# For simplicity, let's assume common_columns are passed in.
# from xplot_app.processing import analyze_log_availability 

class ColumnSelectionDialog(tk.Toplevel):
    """
    Dialog for selecting X, Y, Class (optional), and Z (optional) columns for plotting.
    """

    def __init__(self, parent: tk.Widget, 
                 common_columns: List[str], 
                 las_files: List[Any] # Keep las_files for context if needed, though common_columns is primary
                ) -> None:
        super().__init__(parent)
        self.title("Select Plotting Columns")
        self.geometry("450x300") # Adjusted size

        self.common_columns = common_columns
        self.las_files = las_files # Stored for potential future use or context
        self.result_columns: Optional[Dict[str, Optional[str]]] = None

        if not self.common_columns:
            messagebox.showerror("No Common Columns", 
                                 "No common columns found across all LAS files to select from.",
                                 parent=self)
            # Ensure destroy is called after messagebox, which might affect parent focus
            self.after(100, self.destroy) # Safely destroy after messagebox
            return

        self._build_ui()
        
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
        # self.wait_window(self) # Make it blocking by calling this in get_selected_columns

    def _build_ui(self) -> None:
        """Builds the main UI components of the dialog."""
        frame = ttk.Frame(self, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        self.x_var = tk.StringVar()
        self.y_var = tk.StringVar()
        self.class_var = tk.StringVar()
        self.z_var = tk.StringVar()

        ttk.Label(frame, text="X Column:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.x_menu = ttk.Combobox(frame, textvariable=self.x_var, values=self.common_columns, width=30, state="readonly")
        self.x_menu.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(frame, text="Y Column:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.y_menu = ttk.Combobox(frame, textvariable=self.y_var, values=self.common_columns, width=30, state="readonly")
        self.y_menu.grid(row=1, column=1, padx=5, pady=5)

        class_options = ["(Optional) None"] + self.common_columns
        ttk.Label(frame, text="Class Column:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.class_menu = ttk.Combobox(frame, textvariable=self.class_var, values=class_options, width=30, state="readonly")
        self.class_menu.grid(row=2, column=1, padx=5, pady=5)
        self.class_var.set("(Optional) None")

        z_options = ["(Optional) None"] + self.common_columns
        ttk.Label(frame, text="Z Column (Colormap):").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.z_menu = ttk.Combobox(frame, textvariable=self.z_var, values=z_options, width=30, state="readonly")
        self.z_menu.grid(row=3, column=1, padx=5, pady=5)
        self.z_var.set("(Optional) None")

        # Set default values if common curves are present
        if 'DT' in self.common_columns: self.x_var.set('DT')
        elif self.common_columns: self.x_var.set(self.common_columns[0])
        
        if 'RHOB' in self.common_columns: self.y_var.set('RHOB')
        elif len(self.common_columns) > 1: self.y_var.set(self.common_columns[1])
        elif self.common_columns: self.y_var.set(self.common_columns[0])


        # Mutual exclusivity logic
        self.class_var.trace_add("write", self._on_class_change)
        self.z_var.trace_add("write", self._on_z_change)

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(20,0))
        ttk.Button(button_frame, text="OK", command=self._on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.LEFT, padx=5)

    def _on_class_change(self, *_args: Any) -> None:
        if self.class_var.get() != "(Optional) None":
            self.z_var.set("(Optional) None")
            if hasattr(self, 'z_menu'): self.z_menu.config(state="disabled")
        else:
            if hasattr(self, 'z_menu'): self.z_menu.config(state="readonly")

    def _on_z_change(self, *_args: Any) -> None:
        if self.z_var.get() != "(Optional) None":
            self.class_var.set("(Optional) None")
            if hasattr(self, 'class_menu'): self.class_menu.config(state="disabled")
        else:
            if hasattr(self, 'class_menu'): self.class_menu.config(state="readonly")

    def _on_ok(self) -> None:
        """Validates selections and stores them before closing."""
        x_col = self.x_var.get()
        y_col = self.y_var.get()
        class_col_val = self.class_var.get()
        z_col_val = self.z_var.get()

        if not x_col or not y_col:
            messagebox.showerror("Input Error", "X and Y columns must be selected.", parent=self)
            return
        
        if x_col == "(Optional) None" or y_col == "(Optional) None": # Should not happen with readonly combobox if populated
            messagebox.showerror("Input Error", "X and Y columns must be valid selections.", parent=self)
            return

        self.result_columns = {
            "x_col": x_col,
            "y_col": y_col,
            "class_col": None if class_col_val == "(Optional) None" else class_col_val,
            "z_col": None if z_col_val == "(Optional) None" else z_col_val,
        }
        
        self.destroy()

    def _on_cancel(self) -> None:
        """Handles dialog cancellation."""
        self.result_columns = None
        self.destroy()

    def get_selected_columns(self) -> Optional[Dict[str, Optional[str]]]:
        """Returns the dictionary of selected columns or None if cancelled."""
        self.wait_window(self) # Make dialog modal, wait for it to close
        return self.result_columns

if __name__ == '__main__':
    from unittest.mock import Mock 
    
    root = tk.Tk()
    root.withdraw() # Hide main window for test
    
    test_common_cols = ['GR', 'DT', 'RHOB', 'NPHI', 'SW', 'LITHO_CODE']
    mock_las_files_list = [] 

    dialog = ColumnSelectionDialog(root, test_common_cols, mock_las_files_list)
    # get_selected_columns now includes wait_window
    selected_cols = dialog.get_selected_columns() 
    
    if selected_cols:
        print("Selected columns:", selected_cols)
    else:
        print("Column selection cancelled.")
    
    # root.destroy() # Destroy root if it was not withdrawn and mainloop was run
    # If root was withdrawn and dialog handles its lifecycle, root might not need explicit destroy here
    # unless other tests or UI elements depend on it.
    # For a simple test like this, if root.mainloop() isn't called, destroying is good.
    if root.winfo_exists(): # Check if window still exists
        root.destroy()
