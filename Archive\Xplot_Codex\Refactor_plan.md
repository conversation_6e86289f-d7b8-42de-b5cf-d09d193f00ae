## Refactoring Plan for Xplot_HIST_KDE_FUNCT_Init.py

**Overall Goal:** Transform the monolithic script into a modular, maintainable, and testable Python application.

---

### Phase 0: Preparation & Setup

*   **Objective:** Establish a clean project foundation, version control, and essential development tools.
*   **Key Files Involved:** `.git/`, `.gitignore`, `requirements.txt`, `pyproject.toml` (optional, for `black`), initial project directory structure.

*   **Prompts/Tasks for Jules (Assistant):**
    1.  "Generate a standard Python `.gitignore` file suitable for a project using libraries like NumPy, Pandas, Matplotlib, and Tkinter. Include common IDE files and `__pycache__`."
    2.  "Based on the provided script's imports (`numpy`, `lasio`, `tkinter`, `matplotlib`, `scipy`, `pandas`), generate a `requirements.txt` file." (You might need to provide the script or its import section).
    3.  (Optional) "Generate a basic `pyproject.toml` configuration for `black` with a line length of 88."
*   **Verification Steps:**
    *   Git repository is initialized with the original script.
    *   Project directories exist.
    *   Virtual environment is active, and `requirements.txt` lists the necessary packages.
    *   `.gitignore` file is present.
    *   The global variable clearing block is removed.
*   **Considerations for Next Phase:** How to handle the initial large number of linting errors from the monolithic script (fix now or incrementally).

---

### Phase 1: Configuration Module and Basic Application Structure

*   **Objective:** Isolate constants, configuration data, and related utility functions into a dedicated `config.py` module. Set up the main application entry point.
*   **Key Files Involved:** `xplot_app/config.py`, `xplot_app/main.py`, `xplot_app/__init__.py`.
*   **Tasks for Developer (You):**
    1.  Create empty `xplot_app/config.py`, `xplot_app/main.py`, and `xplot_app/__init__.py`.
    2.  Identify the `log_keywords` and `COLORMAP_CATEGORIES` dictionaries in the original script.
    3.  Identify utility functions closely tied to these configurations (`get_colormap_options`, `get_sequential_subcategories`, etc.).
    4.  Decide if colormap utility functions belong in `config.py` or a new `plotting_utils.py`. For now, `config.py` is acceptable.
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "Given the following Python dictionaries (`log_keywords`, `COLORMAP_CATEGORIES` from the original script), move them into `xplot_app/config.py`."
    2.  "Given the following Python functions (`get_colormap_options`, `get_sequential_subcategories`, `validate_colormap`, `apply_colormap_reversal` from the original script), move them into `xplot_app/config.py`. Ensure they have appropriate imports (like `matplotlib.pyplot as plt`). Add module-level and function-level docstrings and type hints."
    3.  "Create a basic `main()` function in `xplot_app/main.py`. It should import `log_keywords` from `xplot_app.config` and print a confirmation message. Include the `if __name__ == '__main__':` guard to call `main()`."
    4.  "Review `xplot_app/config.py` and add any missing imports (e.g., `plt` from `matplotlib.pyplot`). Ensure all functions have docstrings and type hints."
*   **Verification Steps:**
    *   `xplot_app/config.py` contains the specified constants and helper functions with docstrings.
    *   `xplot_app/main.py` can be run and imports successfully from `config.py`.
    *   The original script (or a working copy) can be temporarily modified to import these items from `xplot_app.config` to verify paths.
*   **Considerations for Next Phase:** The `tkinter` dependency for file dialogs which will soon be moved.

---

### Phase 2: Data Input/Output Module

*   **Objective:** Encapsulate all LAS and Excel file loading and initial processing logic into `data_io.py`.
*   **Key Files Involved:** `xplot_app/data_io.py`.
*   **Tasks for Developer (You):**
    1.  Create an empty `xplot_app/data_io.py`.
    2.  Identify functions: `load_multiple_las_files`, `load_boundaries_from_excel`, `filter_excel_data_for_las_wells`, `load_excel_depth_ranges`.
    3.  Review the current implementation of these functions in the monolithic script.
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "Move the function `load_multiple_las_files` from the original script to `xplot_app/data_io.py`. Ensure it imports `tkinter`, `filedialog`, and `lasio`. Add a module-level docstring to `data_io.py` and function-level docstrings and type hints for the moved function. Include robust `try-except` blocks for file operations."
    2.  "Move the function `load_boundaries_from_excel` to `xplot_app/data_io.py`. Ensure it imports `tkinter`, `filedialog`, `messagebox`, and `pandas`. Add function-level docstrings, type hints, and robust error handling."
    3.  "Move the function `filter_excel_data_for_las_wells` to `xplot_app/data_io.py`. Ensure it imports `pandas`. Add function-level docstrings and type hints."
    4.  "Move the function `load_excel_depth_ranges` to `xplot_app/data_io.py`. This function calls `messagebox`, `load_boundaries_from_excel`, and `filter_excel_data_for_las_wells` (which should now be imported from within `data_io.py` or passed as arguments if preferred for testability, but internal calls are fine for now). Add function-level docstrings and type hints."
    5.  "Review `xplot_app/data_io.py` for consistent error handling, complete docstrings, type hints, and necessary imports at the top of the module."
*   **Verification Steps:**
    *   `data_io.py` contains the specified functions.
    *   Functions have docstrings, type hints, and error handling.
    *   The original script (or a working copy) can be temporarily modified to import and use these functions from `xplot_app.data_io`.
*   **Considerations for Next Phase:** The `tkinter` dependencies in `data_io.py` are a temporary compromise. GUI interactions will eventually be separated.

---

### Phase 3: Statistical Analyzer Classes Module

*   **Objective:** Group all statistical analysis and visualization classes (`StatisticalAnalyzer`, `HistogramAnalyzer`, `KDEAnalyzer`, `StatisticalVisualizer`) into `statistical_analyzers.py`.
*   **Key Files Involved:** `xplot_app/statistical_analyzers.py`.
*   **Tasks for Developer (You):**
    1.  Create an empty `xplot_app/statistical_analyzers.py`.
    2.  Identify the relevant class definitions in the original script.
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "Move the `StatisticalAnalyzer` ABC, `HistogramAnalyzer` class, `KDEAnalyzer` class, and `StatisticalVisualizer` class from the original script into `xplot_app/statistical_analyzers.py`."
    2.  "Ensure all necessary imports (e.g., `numpy`, `matplotlib.pyplot`, `scipy.stats`, `abc`, `typing`) are added to `xplot_app/statistical_analyzers.py`."
    3.  "Add a module-level docstring to `statistical_analyzers.py`. Add comprehensive class-level and method-level docstrings and type hints for all content in this new module."
    4.  "Review the moved classes for any internal inconsistencies or missing dependencies that might have arisen from the move."
*   **Verification Steps:**
    *   `statistical_analyzers.py` contains all four classes.
    *   Classes and methods are fully documented with docstrings and type hints.
    *   The original script (or a working copy) can be temporarily modified to import and use these classes from `xplot_app.statistical_analyzers`.
*   **Considerations for Next Phase:** These classes are fairly self-contained, making them good candidates for early unit testing.

---

### Phase 4: Core Data Processing and Validation Logic Module

*   **Objective:** Extract non-GUI data processing, calculation execution, and validation logic into `processing.py` to make it testable and separate from UI concerns.
*   **Key Files Involved:** `xplot_app/processing.py`, `tests/test_processing.py` (optional but recommended).
*   **Tasks for Developer (You):**
    1.  Create an empty `xplot_app/processing.py`.
    2.  Identify functions for moving: `find_default_columns`, `analyze_log_availability`, `validate_calculation_inputs` (core logic), `interpolate_nan`, `interpolate_class`, `validate_data_for_plotting` (core logic), `get_robust_limits`.
    3.  Identify the calculation execution logic within `get_calculations` (the `exec()` part and adding curves).
    4.  Strategize how `validate_calculation_inputs` and `validate_data_for_plotting` will return structured error/warning information instead of pre-formatted strings for `messagebox`.
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "Move the function `find_default_columns` from the original script to `xplot_app/processing.py`. Add docstrings, type hints, and ensure necessary imports (e.g., `re` if used)."
    2.  "Move the function `analyze_log_availability` to `xplot_app/processing.py`. Add docstrings and type hints."
    3.  "Refactor the `validate_calculation_inputs` function. Move its core logic (parsing calculation text, identifying input/output vars, checking against LAS file curves) to `xplot_app/processing.py`. Modify it to return a dictionary with keys like `'valid': bool`, `'missing_logs_detail': dict`, `'error_summary': str`, `'recommendation_info': str`, instead of a single formatted string for `messagebox`. Add docstrings and type hints."
    4.  "Move `interpolate_nan` and `interpolate_class` functions to `xplot_app/processing.py`. Add docstrings and type hints. Ensure `scipy.interpolate` is imported."
    5.  "Refactor the `validate_data_for_plotting` function. Move its core logic (checking data points, unique values, well data availability) to `xplot_app/processing.py`. Modify it to return a dictionary with keys like `'valid': bool`, `'issues': list_of_strings`, `'warnings': list_of_strings`, `'stats': dict` (as currently structured). Add docstrings and type hints."
    6.  "Move the `get_robust_limits` function to `xplot_app/processing.py`. Add docstrings and type hints."
    7.  "Extract the calculation execution logic (the part with `exec(calculations, {}, local_ns)` and `las.append_curve`) from the `get_calculations` function in the original script. Create a new function in `xplot_app/processing.py` named `execute_custom_calculations(las_file, calculations_string, global_namespace={'np': np})`. This function should modify the `las_file` object in place by adding new curves. It should return a list of newly added curve names or raise an exception on error. Add docstrings and type hints."
    8.  "Add a module-level docstring to `xplot_app/processing.py`. Review all functions for necessary imports (e.g., `numpy`, `re`, `pandas`)."
    9.  (Optional) "For the function `find_default_columns` in `xplot_app/processing.py`, generate a basic unit test case for `tests/test_processing.py` using a mock LAS object structure."
*   **Verification Steps:**
    *   `processing.py` contains the refactored functions.
    *   Validation functions return structured data, not GUI strings.
    *   The `execute_custom_calculations` function correctly modifies LAS objects (can be tested with a mock or simple LAS).
    *   (Optional) Basic unit tests for `processing.py` pass.
*   **Considerations for Next Phase:** The GUI dialogs will now need to call these processing functions and then format their structured return values for display.

---

### Phase 5: GUI Dialogs Refactoring (Iterative)

*   **Objective:** Encapsulate each Tkinter dialog into its own class, making them reusable and separating UI from application logic.
*   **Key Files Involved:** `xplot_app/dialogs/` (new directory with files like `depth_dialog.py`, `column_select_dialog.py`, etc.), `xplot_app/gui_utils.py` (optional).
*   **Tasks for Developer (You):**
    1.  Create the `xplot_app/dialogs/` directory and `__init__.py` inside it.
    2.  Plan the class structure for each dialog. For example, `DepthRangeDialog`, `BatchBoundariesDialog`, `ColumnSelectionDialog`, `PlotSettingsDialog`, `CalculatorDialog`, `StatisticsDialog`, `PostPlotDialog`.
    3.  Decide how dialogs will return their results (e.g., a dictionary, a specific object, or by modifying passed-in mutable objects if simpler for now, though returning results is cleaner).
    4.  For complex dialogs, sketch out the internal methods needed for UI creation and callbacks.
*   **Prompts/Tasks for Jules (Assistant) - Example for one dialog (repeat pattern for others):**
    *   **For `DepthRangeDialog` (from `get_depth_ranges`):**
        1.  "Create a class `DepthRangeDialog` in `xplot_app/dialogs/depth_dialog.py`. It should inherit from `tk.Toplevel` (or manage its own `Tk()` if it's the first/main dialog for now, but `Toplevel` is better if called from a main app window)."
        2.  "The `__init__` method of `DepthRangeDialog` should accept `parent`, `las_files`, `log_keywords`, and `preloaded_excel_df`. It should store these and set up instance variables for results (e.g., `self.result_depth_ranges = {}`)."
        3.  "Move the UI creation logic for method selection (manual/Excel) from `get_depth_ranges` into `DepthRangeDialog`, likely in a `_build_ui` method. `method_var` becomes `self.method_var`."
        4.  "Create `_create_manual_ui` and `_create_excel_ui` methods within `DepthRangeDialog` based on the logic in `get_depth_ranges`. `manual_entries` and `excel_data` become instance variables."
        5.  "Callbacks like `on_radio_click`, `on_load_excel`, `on_preview_boundaries` become methods of `DepthRangeDialog` (e.g., `self._on_radio_click`). The `submit_btn_ref` and `status_update` variables should be handled using instance variables or direct UI element references."
        6.  "The `on_submit` method of `DepthRangeDialog` should collect data from UI elements. If the 'Excel' method is chosen, it should call helper dialogs (like `BatchBoundariesDialog`, which you'll create next based on `select_boundaries_for_all_wells`). It should then store the final depth ranges in `self.result_depth_ranges` and close the dialog."
        7.  "Add a method like `get_selected_depth_ranges()` to `DepthRangeDialog` that the calling code can use to retrieve the results after the dialog is closed."
        8.  "Add docstrings for the class and its public methods. Ensure all necessary Tkinter and custom module imports (like `data_io` for `load_boundaries_from_excel`) are present."
    *   **For `BatchBoundariesDialog` (from `select_boundaries_for_all_wells`):**
        1.  "Create a class `BatchBoundariesDialog` in `xplot_app/dialogs/batch_boundaries_dialog.py` inheriting from `tk.Toplevel`."
        2.  "Its `__init__` should take `parent`, `boundary_dataframe`, `las_well_names`. It will build the table UI."
        3.  "Callbacks and `on_ok`/`on_cancel` become methods. `on_ok` populates a result dictionary and closes."
        4.  "Add a method to retrieve the selected boundaries."
    *   **Repeat this pattern for:**
        *   `SingleWellBoundariesDialog` (from `select_boundaries_from_excel`)
        *   `ColumnSelectionDialog` (from `get_column_names_and_depths` - UI part for X,Y,Class,Z selection. It will *not* call the depth dialogs itself; that's orchestrated by `main.py`).
        *   `PlotSettingsDialog` (from `get_plot_settings`).
        *   `CalculatorDialog` (from `get_calculations` - UI part. The `on_submit` will return the calculation string. The `on_check_availability` method will call `processing.validate_calculation_inputs` and display results using `messagebox`).
        *   `StatisticsDialog` (from `show_data_statistics`).
        *   `PostPlotDialog` (from `show_post_plot_options`).
    *   **For `gui_utils.py` (Optional):**
        1.  "Review the UI creation code in the dialogs. If there are common patterns like creating a scrollable frame with a canvas and scrollbar, extract this into a utility function in `xplot_app/gui_utils.py`."
*   **Verification Steps:**
    *   Each dialog is encapsulated in its own class in the `dialogs` module.
    *   Dialogs can be instantiated and shown (even if not fully integrated into the main flow yet, can test them individually).
    *   Dialogs correctly return the user's input/choices.
    *   `CalculatorDialog` correctly uses `processing.validate_calculation_inputs` for its "Check Availability" feature.
*   **Considerations for Next Phase:** How these dialogs will be orchestrated by the main application loop in `main.py`. The flow of data between them.

---

### Phase 6: Main Plotting Logic Module

*   **Objective:** Consolidate all Matplotlib plotting logic into `plotting.py`, making `create_plot` a central function that uses other refactored modules.
*   **Key Files Involved:** `xplot_app/plotting.py`.
*   **Tasks for Developer (You):**
    1.  Create an empty `xplot_app/plotting.py`.
    2.  Review the `create_plot` function in the original script. Identify its main sections (setup, data aggregation, scatter plotting, marginal plots, formatting).
    3.  Plan how to break down `create_plot` into smaller, private helper functions within the module.
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "Move the `create_plot` function from the original script to `xplot_app/plotting.py`."
    2.  "Modify `create_plot` to take `las_files`, `x_col`, `y_col`, `class_col`, `depth_ranges`, `settings_dict`, and `z_col` as parameters."
    3.  "Update `create_plot` to use `processing.validate_data_for_plotting` for initial data validation. If validation fails, it should display an error message on the plot (as it currently does) or perhaps raise an exception to be handled by the caller."
    4.  "Update `create_plot` to use `statistical_analyzers.StatisticalVisualizer` (or its components like `HistogramAnalyzer`, `KDEAnalyzer`) for generating marginal plots, passing the appropriate data and settings."
    5.  "Ensure `create_plot` correctly uses `settings_dict` for point sizes, colors, fonts, axis limits, colormap settings, etc. It should also use `config.apply_colormap_reversal` and `config.validate_colormap` (or similar helpers if they were moved elsewhere, e.g., `plotting_utils.py`)."
    6.  "Break down the `create_plot` function into smaller, private helper functions within `plotting.py` (e.g., `_setup_axes_layout`, `_aggregate_plot_data`, `_draw_scatter_points`, `_draw_marginal_plots`, `_apply_plot_formatting_and_labels`). `create_plot` will call these helpers."
    7.  "Add a module-level docstring to `plotting.py` and ensure `create_plot` and its helper functions have docstrings and type hints. Add all necessary imports (`matplotlib.pyplot`, `numpy`, `scipy.stats`, and imports from `xplot_app.processing`, `xplot_app.statistical_analyzers`, `xplot_app.config`)."
*   **Verification Steps:**
    *   `plotting.py` contains the `create_plot` function and its helpers.
    *   `create_plot` can be called with appropriate mock data and settings, and it generates a plot.
    *   It correctly utilizes functions/classes from `processing.py`, `statistical_analyzers.py`, and `config.py`.
*   **Considerations for Next Phase:** The `main.py` will now be responsible for gathering all inputs through dialogs and then calling `plotting.create_plot`.

---

### Phase 7: Main Application Flow Orchestration

*   **Objective:** Rebuild the application's main control flow in `xplot_app/main.py`, using all the refactored modules and dialogs.
*   **Key Files Involved:** `xplot_app/main.py`.
*   **Tasks for Developer (You):**
    1.  Outline the sequence of operations in the `main()` function of `xplot_app/main.py`. This will involve:
        *   Loading LAS files.
        *   Showing the calculator dialog and processing results.
        *   Showing dialogs for depth ranges, column selection.
        *   Validating data.
        *   Showing plot settings dialog.
        *   Creating the plot.
        *   Showing post-plot options.
    2.  Decide how to manage the main Tkinter window/root. Ideally, `main.py` creates one `tk.Tk()` root instance, and all dialogs are `Toplevel` windows parented to it or to each other.
    3.  Plan the "restart" loop.
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "In `xplot_app/main.py`, expand the `main()` function. It should implement a loop for 'restart' functionality."
    2.  "Inside the loop, add a call to `plt.close('all')`."
    3.  "Call `data_io.load_multiple_las_files()`. If no files, handle exit."
    4.  "Instantiate and run `dialogs.calculator_dialog.CalculatorDialog`. If calculations are provided, loop through `las_files` and call `processing.execute_custom_calculations`. Handle potential errors from execution using `messagebox` and the dialog's retry mechanism (or implement retry logic in `main.py`)."
    5.  "Call `data_io.load_excel_depth_ranges()` to get `preloaded_excel_df` (this dialog also handles user choice to load or not)."
    6.  "Instantiate and run `dialogs.column_select_dialog.ColumnSelectionDialog`, passing `las_files` and `preloaded_excel_df`. This dialog internally calls `dialogs.depth_dialog.DepthRangeDialog` to get depth ranges as part of its current logic. The result should be the selected columns and depth ranges. Handle cancellation."
        *   *(Developer Note: The above prompt reflects the current `get_column_names_and_depths` structure. A cleaner approach would be for `main.py` to call `DepthRangeDialog` first, then pass its results to `ColumnSelectionDialog`. If you've refactored the dialogs to support this, adjust the prompt.)*
    7.  "Call `processing.validate_data_for_plotting` with the selected data. If validation fails or has warnings, use `messagebox` to inform the user and ask if they want to proceed, potentially showing `dialogs.statistics_dialog.StatisticsDialog`."
    8.  "Instantiate and run `dialogs.plot_settings_dialog.PlotSettingsDialog`, passing necessary context (like `x_col`, `y_col`, `class_col`, `z_col`, `depth_ranges`, `las_files` for initial min/max). Handle cancellation."
    9.  "Call `plotting.create_plot` with all the collected data and settings."
    10. "Instantiate and run `dialogs.post_plot_dialog.PostPlotDialog`. Based on its result ('restart' or 'exit'), continue or break the main loop."
    11. "Add top-level `try-except Exception as e:` in the `main()` function's loop to catch any unhandled errors from modules, display them using `messagebox.showerror`, and then potentially offer to restart or exit via `PostPlotDialog`."
    12. "Ensure `main.py` imports all necessary modules from `xplot_app` submodules and standard libraries like `tkinter`, `matplotlib.pyplot`."
*   **Verification Steps:**
    *   The application runs end-to-end from `python -m xplot_app.main` (or similar, depending on how you structure the executable call).
    *   All dialogs appear in the correct sequence.
    *   Data flows correctly between dialogs, processing functions, and plotting.
    *   The "Start New Analysis" (restart) option works.
    *   Error handling is in place.
*   **Considerations for Next Phase:** With the main flow working, focus shifts to comprehensive testing and documentation.

---

### Phase 8: Testing, Documentation, and Quality Assurance

*   **Objective:** Ensure the refactored application is robust, well-documented, and adheres to quality standards.
*   **Key Files Involved:** `tests/` (new test files like `test_data_io.py`, `test_statistical_analyzers.py`), `README.md`.
*   **Tasks for Developer (You):**
    1.  Identify key functions in `processing.py`, `statistical_analyzers.py`, and `data_io.py` that are good candidates for unit tests.
    2.  Manually test various user workflows and edge cases (e.g., no files selected, invalid calculations, all NaN data, single-value data).
    3.  Review all docstrings for clarity and completeness.
    4.  Write the `README.md`.
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "For `xplot_app/processing.py`, generate unit tests for the following functions: `find_default_columns`, `analyze_log_availability` (with mock LAS data), `get_robust_limits`. Place them in `tests/test_processing.py` using the `unittest` framework."
    2.  "For `xplot_app/statistical_analyzers.py`, generate basic unit tests for `HistogramAnalyzer.plot` and `KDEAnalyzer.plot` focusing on data handling (e.g., empty data, NaN data, single value data). Mock `matplotlib.axes.Axes` if necessary. Place in `tests/test_statistical_analyzers.py`."
    3.  "Review all docstrings in the `xplot_app` package. Ensure they follow a consistent style (e.g., Google style) and accurately describe parameters, return values, and exceptions."
    4.  "Generate a `README.md` template for the application. It should include sections for: Project Title, Description, Features, Installation (mentioning virtual environment and `requirements.txt`), Usage (how to run `main.py`), and Modules Overview."
    5.  "Run `flake8 .` and `black .` on the `xplot_app` directory and report any outstanding issues." (You'll need to provide the code or run this command yourself and feed results back if Jules can't execute shell commands).
*   **Verification Steps:**
    *   Unit tests pass.
    *   `README.md` is comprehensive.
    *   Code passes linting and formatting checks.
    *   Manual testing confirms application stability.
*   **Considerations for Next Phase:** Final review and potential future improvements.

---

### Phase 9: Final Review and Future Considerations

*   **Objective:** Conduct a final assessment of the refactored application and brainstorm potential future work.
*   **Key Files Involved:** Entire codebase.
*   **Tasks for Developer (You):**
    1.  Perform a final walkthrough of the application.
    2.  Reflect on the refactoring process: what worked well, what was difficult.
    3.  Brainstorm future enhancements (as listed in the detailed markdown).
*   **Prompts/Tasks for Jules (Assistant):**
    1.  "Based on the refactored modular structure of `xplot_app` (provide an overview or key module interactions if needed), suggest 3-5 potential areas for future improvement or feature additions that would benefit from this new structure."
    2.  "Review the `main()` function in `xplot_app/main.py`. Are there any opportunities to simplify its control flow or improve state management further (e.g., by introducing a simple application context class)?"
*   **Verification Steps:**
    *   The application is considered complete for this refactoring effort.
    *   A list of potential future work is documented.
*   **Considerations for Next Phase:** None, this is the final phase of this refactoring cycle.

---

This plan breaks the large task into smaller, more digestible pieces suitable for working with an AI assistant. Remember to always review the code generated by Jules carefully and adapt it to your specific needs and understanding. Good luck!