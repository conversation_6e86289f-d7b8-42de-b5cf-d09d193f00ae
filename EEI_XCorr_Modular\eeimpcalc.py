# -*- coding: utf-8 -*-
"""
Created on Thu Aug 29 10:31:02 2024

@author: devri.agustianto
"""

import numpy as np

def eeimpcalc(pvel, svel, dens, angle, kvalue, calcmethod=1):
    """
    Calculate Extended Elastic Impedance (EEI) with robust input validation.
    
    Args:
        pvel: P-wave velocity array
        svel: S-wave velocity array
        dens: Density array
        angle: Angle in degrees
        kvalue: K parameter value
        calcmethod: Calculation method (1, 2, or 3)
        
    Returns:
        tuple: (EEI, EGI, scaler)
    """
    import numpy as np
    
    # Input validation
    try:
        # Convert inputs to numpy arrays
        pvel = np.asarray(pvel, dtype=float)
        svel = np.asarray(svel, dtype=float)
        dens = np.asarray(dens, dtype=float)
        
        # Validate scalar parameters
        if angle is None or not np.isfinite(angle):
            raise ValueError(f"Invalid angle: {angle}")
        if kvalue is None or not np.isfinite(kvalue) or kvalue <= 0:
            raise ValueError(f"Invalid k value: {kvalue}")
            
        # Check for empty arrays
        if pvel.size == 0 or svel.size == 0 or dens.size == 0:
            raise ValueError("Input arrays cannot be empty")
            
        # Check for arrays with no finite values
        if not np.isfinite(pvel).any() or not np.isfinite(svel).any() or not np.isfinite(dens).any():
            raise ValueError("Input arrays must contain at least some finite values")
            
    except (ValueError, TypeError) as e:
        print(f"eeimpcalc input validation error: {e}")
        # Return arrays of NaN with same shape as input
        nan_result = np.full_like(pvel, np.nan)
        return nan_result, nan_result, np.nan
    
    sdt = angle  # angle is in degrees
    k = kvalue

    # Original EEI
    if calcmethod == 1:
        # Original calculation method
        px = np.cos(np.deg2rad(sdt)) + np.sin(np.deg2rad(sdt))
        qx = -8 * k * np.sin(np.deg2rad(sdt))
        rx = np.cos(np.deg2rad(sdt)) - 4 * k * np.sin(np.deg2rad(sdt))

        ao = np.nanmean(pvel)
        bo = np.nanmean(svel)
        co = np.nanmean(dens)

        scf = (ao * co) / ((ao**px) * (bo**qx) * (co**rx))
        EEI = scf * ((pvel**px) * (svel**qx) * (dens**rx))

    # AI, SI, densitas EEI formulation
    elif calcmethod == 2:
        # New calculation method
        # Correct definitions for px, qx, and rx
        px = np.cos(np.deg2rad(sdt)) + np.sin(np.deg2rad(sdt))
        qx = -8 * k * np.sin(np.deg2rad(sdt))
        rx = np.sin(np.deg2rad(sdt)) * (4 * k - 1)

        AI = pvel * dens
        SI = svel * dens

        ao = np.nanmean(pvel)
        bo = np.nanmean(svel)
        co = np.nanmean(dens)
        AIo = np.nanmean(AI)
        SIo = np.nanmean(SI)

        scf = (ao * co) / ((AIo**px) * (SIo**qx) * (co**rx))

        # Using the correct equation: EEI(x) = AI^(cos(x)+sin(x)) ⋅ SI^(−8ksin(x)) ⋅ ρ^(sin(x)(4k−1))
        EEI = scf * ((AI**px) * (SI**qx) * (dens**rx))

    # AI, SI, densitas EEI formulation (omitted density)
    elif calcmethod == 3:
        # New calculation method
        # Correct definitions for px, qx, and rx
        px = np.cos(np.deg2rad(sdt)) + np.sin(np.deg2rad(sdt))
        qx = -8 * k * np.sin(np.deg2rad(sdt))
        rx = np.sin(np.deg2rad(sdt)) * (4 * k - 1)

        AI = pvel * dens
        SI = svel * dens

        ao = np.nanmean(pvel)
        bo = np.nanmean(svel)
        co = np.nanmean(dens)
        AIo = np.nanmean(AI)
        SIo = np.nanmean(SI)

        scf = (ao * co) / ((AIo**px) * (SIo**qx))

        # Using the correct equation: EEI(x) = AI^(cos(x)+sin(x)) ⋅ SI^(−8ksin(x)) ⋅ ρ^(sin(x)(4k−1))
        EEI = scf * ((AI**px) * (SI**qx))

    else:
        raise ValueError("Invalid calcmethod. Use 1, 2, or 3.")

    # EGI calculation (unchanged)
    sdt2 = 90
    px2 = np.cos(np.deg2rad(sdt2)) + np.sin(np.deg2rad(sdt2))
    qx2 = -8 * k * np.sin(np.deg2rad(sdt2))
    rx2 = np.cos(np.deg2rad(sdt2)) - 4 * k * np.sin(np.deg2rad(sdt2))

    scf2 = (ao * co) / ((ao**px2) * (bo**qx2) * (co**rx2))

    EGI = scf2 * ((pvel**px2) * (svel**qx2) * (dens**rx2))

    scaler = scf

    return EEI, EGI, scaler

def calculate_cpei(pvel, svel, rhob, n, phi):
    """
    Calculate CPEI (Compressional Poisson Elastic Impedance) using given n and phi parameters.

    Formula: CPEI = AI^n * (Vp/Vs) * cos(θ) + AI^n * (Vs/Vp) * sin(θ)

    Args:
        pvel: P-wave velocity array
        svel: S-wave velocity array
        rhob: Density array
        n: Exponent parameter (typically 0.1 to 2.0)
        phi: Angle parameter in degrees (-90 to +90)

    Returns:
        CPEI array
    """
    # Calculate acoustic impedance
    AI = pvel * rhob

    # Calculate velocity ratios
    Vp_Vs_ratio = pvel / svel
    Vs_Vp_ratio = svel / pvel

    # Convert angle to radians
    phi_rad = np.deg2rad(phi)

    # Calculate CPEI using the formula
    cpei = (AI**n) * Vp_Vs_ratio * np.cos(phi_rad) + (AI**n) * Vs_Vp_ratio * np.sin(phi_rad)

    return cpei

def calculate_peil(pvel, svel, rhob, n, phi):
    """
    Calculate PEIL (Poisson Elastic Impedance Log) using given n and phi parameters.

    Formula: PEIL = -AI^n * (Vp/Vs) * sin(θ) + AI^n * (Vs/Vp) * cos(θ)

    Args:
        pvel: P-wave velocity array
        svel: S-wave velocity array
        rhob: Density array
        n: Exponent parameter (typically 0.1 to 2.0)
        phi: Angle parameter in degrees (-90 to +90)

    Returns:
        PEIL array
    """
    # Calculate acoustic impedance
    AI = pvel * rhob

    # Calculate velocity ratios
    Vp_Vs_ratio = pvel / svel
    Vs_Vp_ratio = svel / pvel

    # Convert angle to radians
    phi_rad = np.deg2rad(phi)

    # Calculate PEIL using the formula
    peil = -(AI**n) * Vp_Vs_ratio * np.sin(phi_rad) + (AI**n) * Vs_Vp_ratio * np.cos(phi_rad)

    return peil