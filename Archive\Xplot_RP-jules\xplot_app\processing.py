"""Core data processing, validation, and calculation logic for XPlot App."""

import re
from typing import Dict, List, Any, Optional, Set, Tuple, Union # Added Union
import lasio 
import numpy as np
from scipy import interpolate 
import pandas as pd # Added for pd.isna

def find_default_columns(las: lasio.LASFile, keywords: Dict[str, List[str]]) -> Dict[str, Optional[str]]:
    """
    Finds default column mnemonics in a LASFile object based on a keywords dictionary.

    Iterates through a dictionary of common log types (keywords) and their possible
    aliases. For each keyword, it searches the LAS file's curves for a matching
    mnemonic (case-insensitive). The first alias that matches a curve mnemonic
    for a given keyword is considered the default column for that log type.

    Args:
        las: The lasio.LASFile object to search for curves.
        keywords: A dictionary where keys are generic log type names (e.g., 'DT', 'RHOB')
                  and values are lists of possible mnemonic aliases for that log type
                  (e.g., ['DT', 'DTCO', 'AC'] for 'DT').

    Returns:
        A dictionary where keys are the same as the input `keywords` keys, and
        values are the found mnemonic strings from the LAS file. If no matching
        mnemonic is found for a keyword, the value is None.
        Includes detailed print statements for logging the matching process.
    """
    default_columns: Dict[str, Optional[str]] = {}
    print(f"--- Finding default columns for well: {las.well.WELL.value if las.well.WELL else 'Unknown Well'} ---")
    for keyword, aliases in keywords.items():
        found_mnemonic: Optional[str] = None
        for alias in aliases:
            # las.curves is a list of CurveItem objects
            for curve_item in las.curves: # Iterate through CurveItem objects
                # Mnemonic comparison should be case-insensitive
                if alias.upper() == curve_item.mnemonic.upper():
                    found_mnemonic = curve_item.mnemonic
                    print(f"  Found curve '{found_mnemonic}' for keyword '{keyword}' (alias: '{alias}')")
                    break  # Found the first matching alias for this keyword
            if found_mnemonic:
                break  # Move to the next keyword
        
        default_columns[keyword] = found_mnemonic
        if not found_mnemonic:
            print(f"  Curve for keyword '{keyword}' not found (searched aliases: {aliases})")
    print("--- Finished finding default columns ---")
    return default_columns

def analyze_log_availability(las_files: List[lasio.LASFile]) -> Dict[str, Any]:
    """
    Analyzes log availability across a list of LAS files.

    Counts occurrences of each log mnemonic across all provided LAS files
    and categorizes them into common logs (present in all files) and
    partial logs (present in some but not all files).

    Args:
        las_files: A list of lasio.LASFile objects.

    Returns:
        A dictionary containing:
            'common_logs': A sorted list of log mnemonics available in all wells.
            'partial_logs': A dictionary where keys are log mnemonics and values
                            are dictionaries with 'wells' (list of well names
                            containing the log) and 'count' (number of wells
                            containing the log).
            'total_wells': The total number of LAS files processed.
    """
    all_logs: Dict[str, Dict[str, Any]] = {} # Stores {'log_mnemonic': {'wells': [], 'count': 0}}
    well_count: int = len(las_files)

    # Count log occurrences across wells
    for las_file in las_files: # Renamed las to las_file for clarity
        well_name: str = las_file.well.WELL.value if las_file.well.WELL else "UnknownWell"
        for curve_item in las_file.curves: # Iterate through CurveItem objects
            curve_name: str = curve_item.mnemonic
            if curve_name not in all_logs:
                all_logs[curve_name] = {'wells': [], 'count': 0}
            all_logs[curve_name]['wells'].append(well_name)
            all_logs[curve_name]['count'] += 1

    # Separate common logs from partial logs
    common_logs_list: List[str] = [] # Renamed for clarity
    partial_logs_dict: Dict[str, Dict[str, Any]] = {} # Renamed for clarity

    for log_name, log_info in all_logs.items():
        if log_info['count'] == well_count:
            common_logs_list.append(log_name)
        else:
            partial_logs_dict[log_name] = log_info
    
    # Sort common_logs for consistent output
    common_logs_list.sort()

    return {
        'common_logs': common_logs_list,
        'partial_logs': partial_logs_dict,
        'total_wells': well_count
    }

def validate_calculation_inputs(
    las_files: List[lasio.LASFile], 
    calculation_text: str
) -> Dict[str, Any]:
    """
    Validates that all input logs referenced in calculations exist in all LAS files.

    Distinguishes between input variables (must exist in LAS files) and 
    output variables (which are being created by the calculations).

    Args:
        las_files: A list of lasio.LASFile objects.
        calculation_text: A string containing the calculation code (e.g., "NEW_LOG = GR * 2").

    Returns:
        A dictionary containing validation results:
        {
            'valid': bool,  # True if all input logs are found in all wells, False otherwise.
            'missing_logs_detail': Dict[str, List[str]],  # Details of missing logs per well 
                                                        # e.g., {'Well-A': ['GR', 'NPHI']}. Empty if valid.
            'identified_input_vars': List[str], # List of identified input variables.
            'identified_output_vars': List[str], # List of identified output variables.
            'error_summary': Optional[str],  # A summary string of errors if not valid, else None.
            'recommendation_info': Optional[str] # A string with recommendations if not valid, else None.
        }
    """
    if not calculation_text.strip():
        return {
            'valid': True,
            'missing_logs_detail': {},
            'identified_input_vars': [],
            'identified_output_vars': [],
            'error_summary': None,
            'recommendation_info': None,
        }

    lines = [line.strip() for line in calculation_text.split('\n') if line.strip()]
    output_variables: Set[str] = set()
    input_variables: Set[str] = set()
    
    # Simple parsing for input/output variables (assumes 'VAR = ...' structure)
    # More robust parsing might be needed for complex expressions or function calls.
    for line in lines:
        if line.startswith('#'): # Skip comments
            continue
        
        # Attempt to identify output variable (left of '=')
        # This regex looks for a valid variable name on the left of an assignment.
        # It allows for spaces around the '='.
        match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=', line)
        if match:
            output_var = match.group(1).upper() # Convert to upper for case-insensitivity
            output_variables.add(output_var)
            
            # Consider everything on the right of '=' as potentially containing input variables
            right_side_expression = line.split('=', 1)[1] if len(line.split('=', 1)) > 1 else ""
            # Find all words that look like variables in the right-side expression.
            # Exclude numbers and common numpy functions.
            possible_inputs = set(re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', right_side_expression))
            input_variables.update(possible_inputs)
        else:
            # If not an assignment, treat all found variables as inputs
            possible_inputs = set(re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', line))
            input_variables.update(possible_inputs)

    # Refine input variables:
    # 1. Convert to uppercase for case-insensitivity with log mnemonics.
    # 2. Remove any identified output variables from the set of input variables.
    # 3. Remove common known non-log terms (like 'NP' for numpy, math functions if used directly).
    input_variables = {var.upper() for var in input_variables}
    input_variables -= output_variables 
    
    # Define a set of known non-log keywords (e.g., numpy functions)
    # This list might need to be expanded based on common usage in calculations.
    known_non_logs = {'NP', 'NUMPY', 'LOG', 'SQRT', 'EXP', 'SIN', 'COS', 'TAN', 'CLIP', 'WHERE', 'NAN'} 
    input_variables -= known_non_logs

    missing_logs_detail: Dict[str, List[str]] = {}
    all_las_curves_upper: List[Set[str]] = []
    for las_file in las_files:
        all_las_curves_upper.append({curve.mnemonic.upper() for curve in las_file.curves})

    for i, las_file in enumerate(las_files):
        well_name = las_file.well.WELL.value if las_file.well.WELL else f"Well_{i+1}"
        current_well_curves_upper = all_las_curves_upper[i]
        
        missing_in_well = list(input_variables - current_well_curves_upper)
        if missing_in_well:
            missing_logs_detail[well_name] = sorted(missing_in_well)

    is_valid = not bool(missing_logs_detail)
    error_summary_str: Optional[str] = None
    recommendation_info_str: Optional[str] = None

    if not is_valid:
        error_summary_str = "Missing input logs required for calculation in one or more wells."
        recommendation_info_str = "Ensure all referenced input logs exist in all selected LAS files, or adjust calculations."
        # Detailed per-well missing info is in 'missing_logs_detail'

    return {
        'valid': is_valid,
        'missing_logs_detail': missing_logs_detail,
        'identified_input_vars': sorted(list(input_variables)),
        'identified_output_vars': sorted(list(output_variables)),
        'error_summary': error_summary_str,
        'recommendation_info': recommendation_info_str,
    }

def interpolate_nan(depth: np.ndarray, data: np.ndarray) -> np.ndarray:
    """
    Interpolates NaN values in a 1D numpy array using linear interpolation.

    Args:
        depth: 1D numpy array of depth values (monotonic).
        data: 1D numpy array of data values, may contain NaNs.

    Returns:
        A 1D numpy array with NaN values interpolated. If interpolation is not
        possible (e.g., fewer than 2 valid data points), returns the original
        data array.
    """
    mask = ~np.isnan(data)
    if np.sum(mask) < 2:  # Need at least two points to interpolate
        return data
    
    try:
        # Create an interpolation function for the valid data points
        f_interp = interpolate.interp1d(
            depth[mask], 
            data[mask], 
            kind='linear', 
            fill_value="extrapolate" # Extrapolates if needed, or can raise error if bounds_error=True
        )
        return f_interp(depth)
    except Exception as e:
        print(f"Warning: Linear interpolation failed for a segment: {e}. Returning original data for that part.")
        # This part is tricky if only a segment fails. For simplicity, returning original on any error.
        # A more robust solution might try to interpolate segments or handle errors more granularly.
        return data

def interpolate_class(depth: np.ndarray, class_data: np.ndarray) -> np.ndarray:
    """
    Interpolates NaN values in a 1D class data array using nearest-neighbor interpolation.

    Args:
        depth: 1D numpy array of depth values (monotonic).
        class_data: 1D numpy array of class labels, may contain NaNs.

    Returns:
        A 1D numpy array with NaN class labels interpolated using nearest neighbor.
        If interpolation is not possible (e.g., no valid class data points),
        returns the original class_data array.
    """
    mask = ~np.isnan(class_data)
    if np.sum(mask) < 1:  # Need at least one point for nearest neighbor
        return class_data
    
    try:
        # Create a nearest-neighbor interpolation function
        f_interp = interpolate.interp1d(
            depth[mask], 
            class_data[mask], 
            kind='nearest', 
            fill_value="extrapolate" # Extrapolates using the nearest valid point
        )
        return f_interp(depth)
    except Exception as e:
        print(f"Warning: Nearest neighbor interpolation for class data failed: {e}. Returning original data.")
        return class_data

def validate_data_for_plotting(
    las_files: List[lasio.LASFile],
    x_col: str,
    y_col: str,
    class_col: Optional[str],
    depth_ranges: Dict[str, Tuple[float, float]],
    z_col: Optional[str] = None
) -> Dict[str, Any]:
    """
    Validates data from LAS files for plotting based on selected columns and depth ranges.

    Checks for data availability, sufficient valid points, and unique values.
    Aggregates statistics about the data that will be plotted.

    Args:
        las_files: List of lasio.LASFile objects.
        x_col: Mnemonic for the X-axis data.
        y_col: Mnemonic for the Y-axis data.
        class_col: Optional mnemonic for the class/categorical data.
        depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples.
        z_col: Optional mnemonic for the Z-axis data (continuous colormap).

    Returns:
        A dictionary containing:
            'valid': bool,  # Overall validity for proceeding with plotting.
            'issues': List[str],  # Critical issues preventing plotting.
            'warnings': List[str],  # Non-critical issues or recommendations.
            'stats': Dict[str, Any],  # Statistics about the data:
                {
                    'total_points_considered': int, # All points within depth ranges before NaN filter
                    'valid_points_for_plot': int,   # Points with valid X, Y (and Z if applicable)
                    'x_unique_values': int,
                    'y_unique_values': int,
                    'class_unique_values': Optional[int],
                    'z_unique_values': Optional[int],
                    'wells_with_data': List[str],    # Wells contributing valid data
                    'wells_without_data': List[str], # Wells specified but not contributing valid data
                    'wells_missing_cols': Dict[str, List[str]] # Wells missing some of the required columns
                }
    """
    MIN_VALID_POINTS_FOR_PLOT = 1 
    MIN_UNIQUE_VALUES_FOR_MEANINGFUL_PLOT = 1

    issues: List[str] = []
    warnings: List[str] = []
    stats: Dict[str, Any] = {
        'total_points_considered': 0,
        'valid_points_for_plot': 0,
        'x_unique_values': 0,
        'y_unique_values': 0,
        'class_unique_values': None,
        'z_unique_values': None,
        'wells_with_data': [],
        'wells_without_data': [],
        'wells_missing_cols': {}
    }

    overall_x_data_collected = []
    overall_y_data_collected = []
    overall_class_data_collected = []
    overall_z_data_collected = []

    for las_file in las_files:
        well_name = las_file.well.WELL.value if las_file.well.WELL else "UnknownWell"

        if well_name not in depth_ranges:
            warnings.append(f"Depth range not specified for well {well_name}; it will be skipped.")
            stats['wells_without_data'].append(well_name)
            continue

        top_depth, bottom_depth = depth_ranges[well_name]
        
        # Check for essential column existence
        current_missing_cols = []
        if 'DEPTH' not in las_file: current_missing_cols.append('DEPTH')
        if x_col not in las_file: current_missing_cols.append(x_col)
        if y_col not in las_file: current_missing_cols.append(y_col)
        if class_col and class_col not in las_file: current_missing_cols.append(class_col)
        if z_col and z_col not in las_file: current_missing_cols.append(z_col)

        if current_missing_cols:
            stats['wells_missing_cols'][well_name] = current_missing_cols
            warnings.append(f"Well {well_name} missing columns: {', '.join(current_missing_cols)}; skipped.")
            stats['wells_without_data'].append(well_name)
            continue
            
        depth_data = las_file['DEPTH'].data
        mask = (depth_data >= top_depth) & (depth_data <= bottom_depth)
        
        if not np.any(mask):
            warnings.append(f"No data within specified depth range for well {well_name}.")
            stats['wells_without_data'].append(well_name)
            continue

        x_data_well = las_file[x_col].data[mask]
        y_data_well = las_file[y_col].data[mask]
        stats['total_points_considered'] += len(x_data_well)

        valid_mask_xy = ~np.isnan(x_data_well) & ~np.isnan(y_data_well)

        if z_col:
            z_data_well = las_file[z_col].data[mask]
            valid_mask_xy &= ~np.isnan(z_data_well)
            z_data_clean = z_data_well[valid_mask_xy]
            if len(z_data_clean) > 0: overall_z_data_collected.extend(list(z_data_clean))
        
        x_data_clean = x_data_well[valid_mask_xy]
        y_data_clean = y_data_well[valid_mask_xy]

        if len(x_data_clean) > 0:
            stats['wells_with_data'].append(well_name)
            overall_x_data_collected.extend(list(x_data_clean))
            overall_y_data_collected.extend(list(y_data_clean))
            stats['valid_points_for_plot'] += len(x_data_clean)
            
            if class_col:
                class_data_well = las_file[class_col].data[mask]
                class_data_clean = class_data_well[valid_mask_xy] # Ensure class data aligns with valid XY
                overall_class_data_collected.extend(list(class_data_clean))
        else:
            warnings.append(f"Well {well_name} has no valid (non-NaN) X/Y data points in range for selected logs.")
            stats['wells_without_data'].append(well_name)

    if not stats['wells_with_data']:
        issues.append("No wells contain any valid data for the selected columns and depth ranges.")
    
    if stats['valid_points_for_plot'] < MIN_VALID_POINTS_FOR_PLOT:
        issues.append(f"Insufficient valid data points ({stats['valid_points_for_plot']}) across all wells. At least {MIN_VALID_POINTS_FOR_PLOT} required.")

    if overall_x_data_collected:
        stats['x_unique_values'] = len(np.unique(overall_x_data_collected))
        if stats['x_unique_values'] < MIN_UNIQUE_VALUES_FOR_MEANINGFUL_PLOT:
            warnings.append(f"X-axis data has only {stats['x_unique_values']} unique value(s). Plot may not be meaningful.")
    
    if overall_y_data_collected:
        stats['y_unique_values'] = len(np.unique(overall_y_data_collected))
        if stats['y_unique_values'] < MIN_UNIQUE_VALUES_FOR_MEANINGFUL_PLOT:
            warnings.append(f"Y-axis data has only {stats['y_unique_values']} unique value(s). Plot may not be meaningful.")

    if class_col and overall_class_data_collected:
        # Filter out NaNs before counting unique classes for stats
        valid_class_data = [c for c in overall_class_data_collected if not pd.isna(c)] # pd.isna handles np.nan and None
        if valid_class_data:
             stats['class_unique_values'] = len(np.unique(valid_class_data))
             if stats['class_unique_values'] < MIN_UNIQUE_VALUES_FOR_MEANINGFUL_PLOT and stats['class_unique_values'] > 0 : # if 0, it means all were NaN
                 warnings.append(f"Class column '{class_col}' has only {stats['class_unique_values']} unique value(s). Classification may not be meaningful.")
             elif stats['class_unique_values'] == 0:
                 warnings.append(f"Class column '{class_col}' contains no valid (non-NaN) data.")
        else:
            stats['class_unique_values'] = 0
            warnings.append(f"Class column '{class_col}' contains no valid (non-NaN) data after filtering.")


    if z_col and overall_z_data_collected:
        stats['z_unique_values'] = len(np.unique(overall_z_data_collected))
        if stats['z_unique_values'] < MIN_UNIQUE_VALUES_FOR_MEANINGFUL_PLOT:
            warnings.append(f"Z-axis (colormap) data '{z_col}' has only {stats['z_unique_values']} unique value(s). Colormap may not be meaningful.")

    # Determine overall validity
    is_valid = not bool(issues) and stats['valid_points_for_plot'] >= MIN_VALID_POINTS_FOR_PLOT

    return {
        'valid': is_valid,
        'issues': issues,
        'warnings': warnings,
        'stats': stats
    }

def execute_custom_calculations(las_files: List[Any], calculation_text: str) -> bool:
    """
    Execute custom calculations on LAS files and add new curves.
    
    Args:
        las_files: List of lasio.LASFile objects.
        calculation_text: String containing the calculation code.
        
    Returns:
        bool: True if calculations were successful, False otherwise.
    """
    import numpy as np
    from tkinter import messagebox
    
    print("🔄 Calculator: Executing calculations...")
    error_occurred = False
    all_new_curves = set()  # Track all new curves created across wells
    well_curves_added = {}  # Track which curves were added to each well

    for las in las_files:
        well_name = las.well.WELL.value if las.well.WELL else "UnknownWell"
        well_curves_added[well_name] = []

        # Prepare the execution environment
        local_ns = {}
        for curve_item in las.curves:  # Iterate through CurveItem objects
            curve_name = curve_item.mnemonic
            local_ns[curve_name] = np.array(las[curve_name].data)
        # Make numpy available
        local_ns['np'] = np

        try:
            # Execute the calculations
            exec(calculation_text, {}, local_ns)
        except Exception as e:
            # Enhanced error message
            error_message = f"CALCULATION EXECUTION ERROR\n\n"
            error_message += f"❌ Error in well: {well_name}\n"
            error_message += f"Error details: {str(e)}\n\n"
            error_message += "🔍 COMMON CAUSES:\n"
            error_message += "• Using logs that don't exist in this well\n"
            error_message += "• Syntax errors in calculation expressions\n"
            error_message += "• Division by zero or invalid mathematical operations\n\n"
            error_message += "🔧 HOW TO FIX:\n"
            error_message += "1. Use only logs available in all wells\n"
            error_message += "2. Check syntax before submitting\n"
            error_message += "3. Use 'Check Log Availability' button to validate\n"

            messagebox.showerror("Calculator Execution Error", error_message)
            error_occurred = True
            break  # Break out of the for loop

        # Add new variables as curves to the LAS file
        new_curves_added = []
        for var_name, data in local_ns.items():
            if var_name not in [curve.mnemonic for curve in las.curves] and var_name != 'np':
                # Check if data is array-like and has the correct length
                if isinstance(data, np.ndarray) and data.shape[0] == len(las['DEPTH'].data):
                    # Add the new curve
                    las.append_curve(var_name, data)
                    new_curves_added.append(var_name)
                    all_new_curves.add(var_name)
                    well_curves_added[well_name].append(var_name)
                    print(f"✅ Added calculated curve '{var_name}' to well {well_name}")
                else:
                    # Skip variables that are scalars or arrays of incorrect length
                    print(f"⚠️ Skipped variable '{var_name}' in well {well_name}: incorrect shape or type")
                    continue

        if new_curves_added:
            print(f"📊 Well {well_name}: Added {len(new_curves_added)} new calculated curves: {new_curves_added}")
        else:
            print(f"ℹ️ Well {well_name}: No new curves were added from calculations")

        if error_occurred:
            break

    # Check for consistency across wells
    if not error_occurred and all_new_curves:
        print(f"\n🔍 Calculator: Checking curve consistency across {len(las_files)} wells...")
        print(f"📊 Total new curves created: {sorted(all_new_curves)}")

        # Check which curves are missing from which wells
        missing_curves_report = {}
        for curve_name in all_new_curves:
            wells_missing = []
            for las in las_files:
                well_name = las.well.WELL.value if las.well.WELL else "UnknownWell"
                if curve_name not in well_curves_added[well_name]:
                    wells_missing.append(well_name)

            if wells_missing:
                missing_curves_report[curve_name] = wells_missing
                print(f"⚠️ Curve '{curve_name}' missing from wells: {wells_missing}")
            else:
                print(f"✅ Curve '{curve_name}' successfully added to all wells")

        # If some curves are missing from some wells, show a warning
        if missing_curves_report:
            warning_message = "⚠️ CURVE CONSISTENCY WARNING\n\n"
            warning_message += "Some calculated curves were not added to all wells:\n\n"
            for curve_name, missing_wells in missing_curves_report.items():
                warning_message += f"• '{curve_name}' missing from: {', '.join(missing_wells)}\n"
            warning_message += "\n💡 These curves will not appear in the column selection dropdown\n"
            warning_message += "because only curves present in ALL wells are shown.\n\n"
            warning_message += "This usually happens when:\n"
            warning_message += "• Calculations produce different variable names in different wells\n"
            warning_message += "• Some wells have data issues that prevent calculation\n"
            warning_message += "• Variable assignments are conditional\n\n"
            warning_message += "✅ Curves available in ALL wells will still be usable."

            messagebox.showwarning("Curve Consistency Warning", warning_message)
        else:
            print("✅ All calculated curves are available in all wells!")

    if not error_occurred:
        # Calculations successful for all LAS files
        print("✅ Calculator: All calculations executed successfully!")

        # Show success message with details about new curves
        success_message = f"✅ Calculations completed successfully!\n\n"
        success_message += f"Processed {len(las_files)} wells\n"

        if all_new_curves:
            success_message += f"Added {len(all_new_curves)} new calculated curves:\n"
            for curve_name in sorted(all_new_curves):
                success_message += f"• {curve_name}\n"
            success_message += "\n✅ All new curves are available in all wells and will appear in column selection."
        else:
            success_message += "No new curves were created (calculations may have been empty or only modified existing data)."

        messagebox.showinfo("Calculator Success", success_message)
        return True
    else:
        return False

def get_robust_limits(
    data_array: np.ndarray, 
    padding_percent: Union[int, float] = 5
) -> Tuple[Optional[float], Optional[float]]:
    """
    Calculates robust axis limits with padding, handling edge cases like empty or single-value arrays.

    Args:
        data_array: 1D numpy array of numerical data.
        padding_percent: Percentage of the data range to add as padding on both ends. 
                         Defaults to 5.

    Returns:
        A tuple (min_limit, max_limit). Returns (None, None) if data_array is empty.
        If data_array has a single unique value, returns a small range around that value.
    """
    if not isinstance(data_array, np.ndarray):
        # Attempt to convert if it's a list or other sequence
        try:
            data_array = np.array(data_array, dtype=float)
        except ValueError:
            print("Warning: get_robust_limits received non-convertible data. Returning (None,None).")
            return None, None
            
    if data_array.size == 0: # Changed from len(data_array) to data_array.size for numpy
        return None, None

    # Filter out NaNs and Infs for robust min/max calculation
    finite_data = data_array[np.isfinite(data_array)]
    if finite_data.size == 0:
        return None, None # All data was NaN or Inf

    if len(np.unique(finite_data)) == 1:
        single_value = finite_data[0]
        delta = max(abs(single_value) * 0.01, 1.0)  # 1% padding or at least 1.0
        return float(single_value - delta), float(single_value + delta)

    data_min = np.min(finite_data)
    data_max = np.max(finite_data)
    data_range = data_max - data_min

    if data_range == 0: # Should be caught by unique check, but as a safeguard
        delta = max(abs(data_min) * 0.01, 1.0)
        return float(data_min - delta), float(data_max + delta)

    padding = data_range * (float(padding_percent) / 100.0)
    
    # Ensure minimum padding for very small ranges to avoid visual collapse
    # This min_padding can be relative to the magnitude of the numbers or an absolute small value
    min_abs_padding = max(abs(data_min) * 0.01, abs(data_max) * 0.01, 0.1) # e.g. 1% of value or 0.1
    effective_padding = max(padding, min_abs_padding)

    return float(data_min - effective_padding), float(data_max + effective_padding)
