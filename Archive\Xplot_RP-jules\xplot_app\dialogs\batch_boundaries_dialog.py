import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from typing import List, Dict, Optional, Tuple, Any # Any for DataFrame
from gui_utils import create_scrollable_frame # Added import

class BatchBoundariesDialog(tk.Toplevel):
    """
    Dialog for selecting top and bottom boundaries for multiple wells from a DataFrame.
    """

    def __init__(self, parent: tk.Widget, boundary_dataframe: pd.DataFrame, 
                 las_well_names: List[str]) -> None:
        super().__init__(parent)
        self.title("Select Boundaries for All Wells")
        self.geometry("850x600") # Slightly wider for scrollbar visibility

        self.boundary_df = boundary_dataframe
        self.las_well_names = las_well_names
        self.result_boundaries: Optional[Dict[str, Tuple[float, float]]] = None

        self._available_wells = [well for well in self.las_well_names if well in self.boundary_df['Well'].unique()]
        
        if not self._available_wells:
            messagebox.showerror("No Matching Wells", 
                                 "No wells in the Excel data match the loaded LAS files.", 
                                 parent=self)
            self.destroy() # Close if no wells to show
            return

        self._well_data_dict: Dict[str, pd.DataFrame] = {}
        for well in self._available_wells:
            well_data = self.boundary_df[self.boundary_df['Well'] == well].sort_values('MD')
            if not well_data.empty:
                self._well_data_dict[well] = well_data
        
        self._selections: Dict[str, Dict[str, Any]] = {}

        self._build_ui()
        
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
        # self.wait_window(self) # Removed to make it non-blocking until get_selected_boundaries is called


    def _build_ui(self) -> None:
        """Builds the main UI components of the dialog."""
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        instructions = ttk.Label(
            main_frame,
            text="Select top and bottom boundaries for wells. Boundaries define the depth range for analysis.",
            wraplength=780
        )
        instructions.pack(pady=(0, 10))

        # Use the new utility function to create a scrollable frame
        # The table_container will be the parent passed to the utility.
        # The returned content_frame is where the grid of labels/comboboxes will be placed.
        self.scrollable_frame, _ = create_scrollable_frame(main_frame)
        # table_container.pack(fill=tk.BOTH, expand=True, pady=10) # Not needed if main_frame is parent

        self._populate_table()

        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="OK", command=self._on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.LEFT, padx=5)

    def _populate_table(self) -> None:
        """Populates the scrollable frame with well boundary selection rows."""
        headers = ["Well Name", "Top Boundary", "Top Depth", "Bottom Boundary", "Bottom Depth"]
        for col, header_text in enumerate(headers):
            ttk.Label(self.scrollable_frame, text=header_text, font=("", 10, "bold")).grid(
                row=0, column=col, padx=10, pady=5, sticky=tk.W
            )

        current_row = 1
        for well_name in self._available_wells:
            if well_name not in self._well_data_dict:
                continue 
            
            well_df = self._well_data_dict[well_name]
            surfaces = well_df['Surface'].unique().tolist()

            if len(surfaces) < 2: # Need at least two surfaces for top and bottom
                continue

            self._selections[well_name] = {
                "top_surface_var": tk.StringVar(value=surfaces[0]),
                "bottom_surface_var": tk.StringVar(value=surfaces[-1]),
                "top_depth_var": tk.StringVar(),
                "bottom_depth_var": tk.StringVar(),
                "top_depth_val": None,
                "bottom_depth_val": None
            }

            ttk.Label(self.scrollable_frame, text=well_name).grid(row=current_row, column=0, padx=10, pady=5, sticky=tk.W)
            
            top_combo = ttk.Combobox(self.scrollable_frame, textvariable=self._selections[well_name]["top_surface_var"],
                                     values=surfaces, state="readonly", width=20)
            top_combo.grid(row=current_row, column=1, padx=10, pady=5, sticky=tk.W)
            top_combo.bind("<<ComboboxSelected>>", lambda e, w=well_name: self._update_depth_label(w, "top"))

            ttk.Label(self.scrollable_frame, textvariable=self._selections[well_name]["top_depth_var"]).grid(
                row=current_row, column=2, padx=10, pady=5, sticky=tk.W)

            bottom_combo = ttk.Combobox(self.scrollable_frame, textvariable=self._selections[well_name]["bottom_surface_var"],
                                        values=surfaces, state="readonly", width=20)
            bottom_combo.grid(row=current_row, column=3, padx=10, pady=5, sticky=tk.W)
            bottom_combo.bind("<<ComboboxSelected>>", lambda e, w=well_name: self._update_depth_label(w, "bottom"))

            ttk.Label(self.scrollable_frame, textvariable=self._selections[well_name]["bottom_depth_var"]).grid(
                row=current_row, column=4, padx=10, pady=5, sticky=tk.W)

            self._update_depth_label(well_name, "top")
            self._update_depth_label(well_name, "bottom")
            current_row += 1
            
    def _update_depth_label(self, well_name: str, surface_type: str) -> None:
        """Updates the depth display when a surface selection changes."""
        sel_vars = self._selections[well_name]
        surface_name = sel_vars[f"{surface_type}_surface_var"].get()
        well_df = self._well_data_dict[well_name]
        
        md_values = well_df[well_df['Surface'] == surface_name]['MD'].values
        if len(md_values) > 0:
            depth = float(md_values[0])
            sel_vars[f"{surface_type}_depth_var"].set(f"{depth:.2f}")
            sel_vars[f"{surface_type}_depth_val"] = depth
        else:
            sel_vars[f"{surface_type}_depth_var"].set("N/A")
            sel_vars[f"{surface_type}_depth_val"] = None

    def _on_ok(self) -> None:
        """Validates selections and closes dialog, storing results."""
        self.result_boundaries = {}
        invalid_wells = []
        for well_name, selection_vars in self._selections.items():
            top_depth = selection_vars["top_depth_val"]
            bottom_depth = selection_vars["bottom_depth_val"]

            if top_depth is None or bottom_depth is None:
                invalid_wells.append(well_name)
                continue
            if bottom_depth <= top_depth:
                messagebox.showerror("Input Error", 
                                     f"For well {well_name}, bottom depth ({bottom_depth:.2f}) must be greater than top depth ({top_depth:.2f}).",
                                     parent=self)
                self.result_boundaries = None # Indicate error by setting to None
                return 

            self.result_boundaries[well_name] = (top_depth, bottom_depth)
        
        if invalid_wells: # Should ideally not be reached if previous check is comprehensive
            messagebox.showerror("Invalid Selections", 
                                 f"Could not retrieve valid depth values for all surfaces in wells: {', '.join(invalid_wells)}",
                                 parent=self)
            self.result_boundaries = None 
            return

        if not self.result_boundaries and self._available_wells: 
            # This case implies all wells had issues or no valid selections were made
            # messagebox.showwarning("No Selections", "No boundaries were effectively selected for any well.", parent=self)
            # It's better to let it close and return None/empty if no valid data was processed
            pass # Let it destroy and return the current state of self.result_boundaries

        self.destroy()

    def _on_cancel(self) -> None:
        """Handles dialog cancellation."""
        self.result_boundaries = None 
        self.destroy()

    def get_selected_boundaries(self) -> Optional[Dict[str, Tuple[float, float]]]:
        """Returns the dictionary of selected boundaries if OK was pressed, else None."""
        self.wait_window(self) # Ensure dialog closes before returning
        return self.result_boundaries

# Example Usage (for testing the dialog standalone)
if __name__ == '__main__':
    root_app = tk.Tk()
    root_app.title("Main App (Test)")
    root_app.withdraw() # Keep root window hidden for dialog test

    sample_excel_data = {
        'Well': ['Well-A', 'Well-A', 'Well-A', 'Well-B', 'Well-B', 'Well-C'],
        'Surface': ['Top1', 'Mid1', 'Base1', 'Top2', 'Base2', 'TopC'],
        'MD': [1000.0, 1500.0, 2000.0, 1200.0, 1800.0, 900.0]
    }
    mock_df = pd.DataFrame(sample_excel_data)
    mock_las_wells = ['Well-A', 'Well-B', 'Well-D'] 

    print("Launching BatchBoundariesDialog...")
    dialog = BatchBoundariesDialog(root_app, mock_df, mock_las_wells)
    # The get_selected_boundaries method now includes wait_window
    selected_data = dialog.get_selected_boundaries() 
    
    if selected_data is not None:
        print("Selected Boundaries:", selected_data)
    else:
        print("Dialog was cancelled or no valid selections made.")
    
    root_app.destroy()
