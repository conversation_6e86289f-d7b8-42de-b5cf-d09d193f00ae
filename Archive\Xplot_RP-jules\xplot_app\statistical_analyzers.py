"""Statistical analysis classes for XPlot App, including Histogram, KDE, and general visualizers."""

import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Union, Any # Added Any

class StatisticalAnalyzer(ABC):
    """Abstract base class for statistical analysis and visualization."""

    def __init__(self, data: np.ndarray, class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict[Any, str]] = None, 
                 class_names: Optional[Dict[Any, str]] = None) -> None:
        """
        Initialize the statistical analyzer.

        Args:
            data: 1D numpy array of data values.
            class_data: Optional 1D numpy array of class labels.
            colors: Optional dictionary mapping class values to color strings.
            class_names: Optional dictionary mapping class values to display names.
        """
        self.data = np.array(data)
        self.class_data = np.array(class_data) if class_data is not None else None
        self.colors: Dict[Any, str] = colors if colors is not None else {}
        self.class_names: Dict[Any, str] = class_names if class_names is not None else {}
        self._validate_data()

    def _validate_data(self) -> None:
        """Validate input data. Raises ValueError on invalid data."""
        if len(self.data) == 0:
            raise ValueError("Data array cannot be empty")

        if self.class_data is not None and len(self.class_data) != len(self.data):
            raise ValueError("Class data must have the same length as data array")

    def get_clean_data(self) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Remove NaN values from data and corresponding class data."""
        valid_mask = ~np.isnan(self.data)
        clean_data = self.data[valid_mask]
        clean_class_data = self.class_data[valid_mask] if self.class_data is not None else None
        return clean_data, clean_class_data

    def get_unique_classes(self) -> List[Any]: # Class values can be of any type
        """Get unique class values from class_data, excluding NaN."""
        if self.class_data is None:
            return []
        # Get clean data first to ensure class_data is also cleaned if data has NaNs
        _, clean_class_data_for_unique = self.get_clean_data()
        if clean_class_data_for_unique is None or len(clean_class_data_for_unique) == 0:
            return []
        # Ensure NaNs are handled before calling np.unique, as np.unique might treat NaNs differently
        # This line specifically filters out NaN values from the class data before finding unique values.
        return list(np.unique(clean_class_data_for_unique[~np.isnan(clean_class_data_for_unique)]))


    @abstractmethod
    def plot(self, ax: Any, **kwargs: Any) -> None: # Using Any for ax: plt.Axes for now
        """Abstract method for plotting. Must be implemented by subclasses."""
        pass


class HistogramAnalyzer(StatisticalAnalyzer):
    """Class for histogram analysis and visualization."""

    def __init__(self, data: np.ndarray, class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict[Any, str]] = None, class_names: Optional[Dict[Any, str]] = None,
                 bins: int = 30, density: bool = True, alpha: float = 0.7) -> None:
        """
        Initialize the histogram analyzer.

        Args:
            data: 1D numpy array of data values.
            class_data: Optional 1D numpy array of class labels.
            colors: Optional dictionary mapping class values to color strings.
            class_names: Optional dictionary mapping class values to display names.
            bins: Number of histogram bins.
            density: Whether to normalize histogram to show density.
            alpha: Transparency level for histogram bars.
        """
        super().__init__(data, class_data, colors, class_names)
        self.bins = bins
        self.density = density
        self.alpha = alpha

    def _calculate_optimal_bins(self, data: np.ndarray) -> int:
        """Calculate optimal number of bins for the given data."""
        if len(data) == 0:
            return 1

        unique_values = len(np.unique(data))
        if unique_values <= 1:
            return 1

        # Use Sturges' rule as a starting point, but cap it
        optimal_bins = min(self.bins, max(5, unique_values, int(np.ceil(np.log2(len(data)) + 1))))
        return optimal_bins

    def plot(self, ax: Any, orientation: str = 'vertical', **kwargs: Any) -> None: # Using Any for ax: plt.Axes
        """
        Plot histogram on the given axes.

        Args:
            ax: Matplotlib axes object.
            orientation: 'vertical' or 'horizontal'.
            **kwargs: Additional plotting arguments.
        """
        clean_data, clean_class_data = self.get_clean_data()

        if len(clean_data) == 0:
            print("Warning: No valid data for histogram")
            return

        if clean_class_data is None or len(self.get_unique_classes()) == 0:
            self._plot_single_histogram(ax, clean_data, orientation, **kwargs)
        else:
            self._plot_class_histograms(ax, clean_data, clean_class_data, orientation, **kwargs)

    def _plot_single_histogram(self, ax: Any, data: np.ndarray, orientation: str, **kwargs: Any) -> None: # Using Any for ax: plt.Axes
        """Plot a single histogram for all data."""
        bins = self._calculate_optimal_bins(data)

        try:
            if orientation == 'horizontal':
                ax.hist(data, bins=bins, orientation='horizontal',
                       color=kwargs.get('color', 'gray'), alpha=self.alpha, density=self.density)
            else:
                ax.hist(data, bins=bins, color=kwargs.get('color', 'gray'),
                       alpha=self.alpha, density=self.density)
        except Exception as e:
            print(f"Warning: Could not create histogram: {e}")
            if len(np.unique(data)) == 1:
                if orientation == 'horizontal':
                    ax.axhline(y=data[0], color=kwargs.get('color', 'gray'), alpha=self.alpha)
                else:
                    ax.axvline(x=data[0], color=kwargs.get('color', 'gray'), alpha=self.alpha)

    def _plot_class_histograms(self, ax: Any, data: np.ndarray, class_data: np.ndarray, # Using Any for ax: plt.Axes
                              orientation: str, **kwargs: Any) -> None:
        """Plot separate histograms for each class."""
        unique_classes = self.get_unique_classes()
        bins = self._calculate_optimal_bins(data)
        
        if len(np.unique(data)) > 1:
            bin_edges = np.linspace(np.min(data), np.max(data), bins + 1)
        else:
            single_val = data[0] if len(data) > 0 else 0
            bin_edges = np.array([single_val - 0.5, single_val + 0.5])


        for class_val in unique_classes:
            class_mask = (class_data == class_val)
            class_data_subset = data[class_mask]

            if len(class_data_subset) == 0:
                continue

            color_key = class_val if not np.isnan(class_val) else 'NaN' # Handle NaN key
            color = self.colors.get(color_key, f'C{len(unique_classes) % 10}')
            label = self.class_names.get(color_key, f'Class {class_val}')


            try:
                if orientation == 'horizontal':
                    ax.hist(class_data_subset, bins=bin_edges, orientation='horizontal',
                           color=color, alpha=self.alpha, density=self.density,
                           label=label, histtype='step', linewidth=2)
                else:
                    ax.hist(class_data_subset, bins=bin_edges, color=color,
                           alpha=self.alpha, density=self.density, label=label,
                           histtype='step', linewidth=2)
            except Exception as e:
                print(f"Warning: Could not create histogram for class {class_val}: {e}")


class KDEAnalyzer(StatisticalAnalyzer):
    """Class for Kernel Density Estimation analysis and visualization."""

    def __init__(self, data: np.ndarray, class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict[Any, str]] = None, class_names: Optional[Dict[Any, str]] = None,
                 bandwidth: Optional[float] = None, n_points: int = 100) -> None:
        """
        Initialize the KDE analyzer.

        Args:
            data: 1D numpy array of data values.
            class_data: Optional 1D numpy array of class labels.
            colors: Optional dictionary mapping class values to color strings.
            class_names: Optional dictionary mapping class values to display names.
            bandwidth: KDE bandwidth (None for automatic selection).
            n_points: Number of points for KDE curve.
        """
        super().__init__(data, class_data, colors, class_names)
        self.bandwidth = bandwidth
        self.n_points = n_points

    def plot(self, ax: Any, orientation: str = 'vertical',  # Using Any for ax: plt.Axes
             data_range: Optional[Tuple[float, float]] = None, **kwargs: Any) -> None:
        """
        Plot KDE on the given axes.

        Args:
            ax: Matplotlib axes object.
            orientation: 'vertical' or 'horizontal'.
            data_range: Tuple of (min, max) for KDE evaluation range.
            **kwargs: Additional plotting arguments.
        """
        clean_data, clean_class_data = self.get_clean_data()

        if len(clean_data) == 0:
            print("Warning: No valid data for KDE")
            return

        if clean_class_data is None or len(self.get_unique_classes()) == 0:
            self._plot_single_kde(ax, clean_data, orientation, data_range, **kwargs)
        else:
            self._plot_class_kdes(ax, clean_data, clean_class_data, orientation, data_range, **kwargs)

    def _plot_single_kde(self, ax: Any, data: np.ndarray, orientation: str, # Using Any for ax: plt.Axes
                        data_range: Optional[Tuple[float, float]], **kwargs: Any) -> None:
        """Plot a single KDE for all data."""
        if len(data) < 2:
            print("Warning: KDE requires at least 2 data points")
            if len(data) == 1:
                if orientation == 'horizontal':
                    ax.axhline(y=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
                else:
                    ax.axvline(x=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
            return

        if np.var(data) <= 1e-10: # Check for zero variance
            print("Warning: Data has zero variance, using line instead of KDE")
            if orientation == 'horizontal':
                ax.axhline(y=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
            else:
                ax.axvline(x=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
            return
            
        try:
            kde = stats.gaussian_kde(data)
            if self.bandwidth is not None:
                kde.set_bandwidth(self.bandwidth)

            if data_range is not None:
                eval_range = np.linspace(data_range[0], data_range[1], self.n_points)
            else:
                data_min, data_max = np.min(data), np.max(data)
                range_padding = (data_max - data_min) * 0.1 if (data_max - data_min) > 0 else 1.0
                eval_range = np.linspace(data_min - range_padding, data_max + range_padding, self.n_points)
            
            kde_values = kde(eval_range)

            if orientation == 'horizontal':
                ax.plot(kde_values, eval_range, color=kwargs.get('color', 'blue'),
                       linestyle='--', linewidth=2)
            else:
                ax.plot(eval_range, kde_values, color=kwargs.get('color', 'blue'),
                       linestyle='--', linewidth=2)
        except Exception as e:
            print(f"Warning: Could not create KDE: {e}")

    def _plot_class_kdes(self, ax: Any, data: np.ndarray, class_data: np.ndarray, # Using Any for ax: plt.Axes
                        orientation: str, data_range: Optional[Tuple[float, float]], 
                        **kwargs: Any) -> None:
        """Plot separate KDEs for each class."""
        unique_classes = self.get_unique_classes()

        for class_val in unique_classes:
            class_mask = (class_data == class_val)
            class_data_subset = data[class_mask]

            if len(class_data_subset) < 2:
                print(f"Warning: Class {class_val} has insufficient data for KDE")
                continue
            
            if np.var(class_data_subset) <= 1e-10: # Check for zero variance
                print(f"Warning: Class {class_val} has zero variance, skipping KDE")
                continue


            color_key = class_val if not np.isnan(class_val) else 'NaN' # Handle NaN key
            color = self.colors.get(color_key, f'C{len(unique_classes) % 10}')
            label = self.class_names.get(color_key, f'Class {class_val}')

            try:
                kde = stats.gaussian_kde(class_data_subset)
                if self.bandwidth is not None:
                    kde.set_bandwidth(self.bandwidth)

                if data_range is not None:
                    eval_range = np.linspace(data_range[0], data_range[1], self.n_points)
                else:
                    data_min_overall, data_max_overall = np.min(data), np.max(data) # Use overall data for range
                    range_padding = (data_max_overall - data_min_overall) * 0.1 if (data_max_overall - data_min_overall) > 0 else 1.0
                    eval_range = np.linspace(data_min_overall - range_padding, data_max_overall + range_padding, self.n_points)

                kde_values = kde(eval_range)

                if orientation == 'horizontal':
                    ax.plot(kde_values, eval_range, color=color, linestyle='--',
                           linewidth=2, label=label)
                else:
                    ax.plot(eval_range, kde_values, color=color, linestyle='--',
                           linewidth=2, label=label)
            except Exception as e:
                print(f"Warning: Could not create KDE for class {class_val}: {e}")


class StatisticalVisualizer:
    """Combined class for managing histogram and KDE visualizations."""

    def __init__(self, x_data: np.ndarray, y_data: np.ndarray,
                 class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict[Any, str]] = None, 
                 class_names: Optional[Dict[Any, str]] = None) -> None:
        """
        Initialize the statistical visualizer.

        Args:
            x_data: X-axis data.
            y_data: Y-axis data.
            class_data: Optional class labels.
            colors: Optional dictionary mapping class values to color strings.
            class_names: Optional dictionary mapping class values to display names.
        """
        self.x_data = np.array(x_data)
        self.y_data = np.array(y_data)
        self.class_data = np.array(class_data) if class_data is not None else None
        self.colors: Dict[Any, str] = colors if colors is not None else {} # Ensure type
        self.class_names: Dict[Any, str] = class_names if class_names is not None else {} # Ensure type

        # Create analyzers for X and Y data
        self.x_histogram = HistogramAnalyzer(self.x_data, self.class_data, self.colors, self.class_names)
        self.y_histogram = HistogramAnalyzer(self.y_data, self.class_data, self.colors, self.class_names)
        self.x_kde = KDEAnalyzer(self.x_data, self.class_data, self.colors, self.class_names)
        self.y_kde = KDEAnalyzer(self.y_data, self.class_data, self.colors, self.class_names)

    def plot_marginal_distributions(self, ax_histx: Any, ax_histy: Any, # Using Any for ax: plt.Axes
                                   plot_type: str = 'Both',
                                   x_range: Optional[Tuple[float, float]] = None,
                                   y_range: Optional[Tuple[float, float]] = None,
                                   **kwargs: Any) -> None:
        """
        Plot marginal distributions on the given axes.

        Args:
            ax_histx: Matplotlib Axes for X marginal plot (top).
            ax_histy: Matplotlib Axes for Y marginal plot (right).
            plot_type: 'Histogram', 'KDE', or 'Both'.
            x_range: Range for X-axis KDE evaluation.
            y_range: Range for Y-axis KDE evaluation.
            **kwargs: Additional plotting arguments.
        """
        if plot_type in ['Histogram', 'Both']:
            self.x_histogram.plot(ax_histx, orientation='vertical', **kwargs)
            self.y_histogram.plot(ax_histy, orientation='horizontal', **kwargs)

        if plot_type in ['KDE', 'Both']:
            self.x_kde.plot(ax_histx, orientation='vertical', data_range=x_range, **kwargs)
            self.y_kde.plot(ax_histy, orientation='horizontal', data_range=y_range, **kwargs)
