#!/usr/bin/env python3
"""Test script to check if all imports work correctly."""

import sys
import traceback

def test_import(module_name, import_statement):
    """Test a specific import and report results."""
    try:
        exec(import_statement)
        print(f"✓ {module_name} imported successfully")
        return True
    except ImportError as e:
        print(f"❌ {module_name} import error: {e}")
        return False
    except Exception as e:
        print(f"❌ {module_name} unexpected error: {e}")
        return False

def main():
    print("Testing imports...")
    print("=" * 50)
    
    all_passed = True
    
    # Test core module imports
    tests = [
        ("config", "from config import log_keywords, COLORMAP_CATEGORIES"),
        ("data_io", "from data_io import load_multiple_las_files, load_excel_depth_ranges"),
        ("processing", "from processing import analyze_log_availability, find_default_columns, validate_calculation_inputs, validate_data_for_plotting, execute_custom_calculations"),
        ("statistical_analyzers", "from statistical_analyzers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StatisticalVisualizer"),
        ("plotting", "from plotting import create_plot"),
        ("gui_utils", "from gui_utils import create_scrollable_frame"),
    ]
    
    for module_name, import_statement in tests:
        if not test_import(module_name, import_statement):
            all_passed = False
    
    # Test dialog imports
    dialog_tests = [
        ("column_select_dialog", "from dialogs.column_select_dialog import ColumnSelectionDialog"),
        ("depth_dialog", "from dialogs.depth_dialog import DepthRangeDialog"),
        ("plot_settings_dialog", "from dialogs.plot_settings_dialog import PlotSettingsDialog"),
        ("post_plot_dialog", "from dialogs.post_plot_dialog import PostPlotDialog"),
        ("calculator_dialog", "from dialogs.calculator_dialog import CalculatorDialog"),
        ("statistics_dialog", "from dialogs.statistics_dialog import StatisticsDialog"),
        ("batch_boundaries_dialog", "from dialogs.batch_boundaries_dialog import BatchBoundariesDialog"),
    ]
    
    for module_name, import_statement in dialog_tests:
        if not test_import(module_name, import_statement):
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All imports successful! The refactored modules are properly structured.")
        return 0
    else:
        print("❌ Some imports failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())