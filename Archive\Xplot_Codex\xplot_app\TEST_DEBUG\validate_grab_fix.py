#!/usr/bin/env python3
"""
Simple validation script to check if the grab fix is properly implemented.
"""

import os
import sys

def validate_grab_fix():
    """Validate that the grab fix is properly implemented."""
    
    print("=== Validating Grab Fix Implementation ===")
    print()
    
    # Check if the enhanced depth dialog file exists
    enhanced_dialog_path = "dialogs/enhanced_depth_dialog.py"
    if not os.path.exists(enhanced_dialog_path):
        print(f"✗ File not found: {enhanced_dialog_path}")
        return False
    
    print(f"✓ Found: {enhanced_dialog_path}")
    
    # Read the file and check for key fixes
    try:
        with open(enhanced_dialog_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for grab error handling
        if "grab failed" in content.lower():
            print("✓ Grab error handling implemented")
        else:
            print("✗ Grab error handling not found")
            return False
        
        # Check for grab_release calls
        if "grab_release()" in content:
            print("✓ Grab release functionality implemented")
        else:
            print("✗ Grab release functionality not found")
            return False
        
        # Check for fallback mechanisms
        if "attributes('-topmost'" in content:
            print("✓ Fallback topmost mechanism implemented")
        else:
            print("✗ Fallback topmost mechanism not found")
            return False
        
        # Check for enhanced error handling in validation
        if "validation_errors" in content.lower():
            print("✓ Enhanced validation error handling implemented")
        else:
            print("✗ Enhanced validation error handling not found")
            return False
        
        # Check for proper dialog setup
        if "wait_visibility()" in content:
            print("✓ Proper dialog visibility handling implemented")
        else:
            print("✗ Proper dialog visibility handling not found")
            return False
        
        print()
        print("=== Validation Results ===")
        print("✓ All grab fix components are properly implemented")
        print("✓ Error handling for grab conflicts is in place")
        print("✓ Fallback mechanisms are available")
        print("✓ Enhanced validation error handling is implemented")
        print()
        print("The grab fix should resolve the following issues:")
        print("- 'grab failed: another application has grab' errors")
        print("- Modal dialog conflicts during Excel depth processing")
        print("- Validation errors when depth ranges are missing from some wells")
        print("- Proper error messages for incomplete depth data")
        
        return True
        
    except Exception as e:
        print(f"✗ Error reading file: {e}")
        return False

def check_column_dialog_fix():
    """Check if the column selection dialog has the proper grab handling."""
    
    print("\n=== Checking Column Selection Dialog Fix ===")
    
    column_dialog_path = "dialogs/column_select_dialog.py"
    if not os.path.exists(column_dialog_path):
        print(f"✗ File not found: {column_dialog_path}")
        return False
    
    print(f"✓ Found: {column_dialog_path}")
    
    try:
        with open(column_dialog_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for grab release before depth selection
        if "grab_release()" in content:
            print("✓ Grab release before depth selection implemented")
        else:
            print("✗ Grab release before depth selection not found")
            return False
        
        # Check for grab restoration
        if "grab_set()" in content:
            print("✓ Grab restoration after depth selection implemented")
        else:
            print("✗ Grab restoration after depth selection not found")
            return False
        
        # Check for error handling
        if "except tk.TclError" in content:
            print("✓ TclError handling implemented")
        else:
            print("✗ TclError handling not found")
            return False
        
        print("✓ Column selection dialog grab handling is properly implemented")
        return True
        
    except Exception as e:
        print(f"✗ Error reading file: {e}")
        return False

if __name__ == "__main__":
    print("Xplot Application - Grab Fix Validation")
    print("=" * 50)
    
    success1 = validate_grab_fix()
    success2 = check_column_dialog_fix()
    
    if success1 and success2:
        print("\n🎉 GRAB FIX VALIDATION PASSED!")
        print("All components are properly implemented.")
        print("\nThe fix should resolve:")
        print("- Modal dialog grab conflicts")
        print("- Excel depth processing errors")
        print("- Missing depth range validation issues")
        print("- Proper user feedback for incomplete data")
    else:
        print("\n❌ GRAB FIX VALIDATION FAILED!")
        print("Some components may be missing or incorrectly implemented.")
    
    print("\nNext steps:")
    print("1. Test manually by running the application")
    print("2. Load LAS files and Excel depth data")
    print("3. Go through the depth range selection workflow")
    print("4. Verify no 'grab failed' errors occur")
