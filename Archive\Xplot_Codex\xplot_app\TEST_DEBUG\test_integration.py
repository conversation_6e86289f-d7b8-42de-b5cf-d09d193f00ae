#!/usr/bin/env python3
"""
Test script to verify the Excel depth processing integration.
"""

import sys
import traceback

def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        import data_io
        print("✓ data_io imported successfully")
    except Exception as e:
        print(f"✗ Failed to import data_io: {e}")
        return False
    
    try:
        from dialogs.enhanced_depth_dialog import EnhancedDepthDialog, get_enhanced_depth_ranges
        print("✓ enhanced_depth_dialog imported successfully")
    except Exception as e:
        print(f"✗ Failed to import enhanced_depth_dialog: {e}")
        traceback.print_exc()
        return False
    
    try:
        from dialogs.depth_dialog import get_depth_ranges
        print("✓ depth_dialog imported successfully")
    except Exception as e:
        print(f"✗ Failed to import depth_dialog: {e}")
        return False
    
    try:
        import main
        print("✓ main imported successfully")
    except Exception as e:
        print(f"✗ Failed to import main: {e}")
        return False
    
    return True

def test_data_io_functions():
    """Test data_io functions."""
    print("\nTesting data_io functions...")
    
    try:
        import data_io
        
        # Test find_default_columns function
        class MockLAS:
            def __init__(self):
                self.curves = [MockCurve("DEPTH"), MockCurve("DT"), MockCurve("RHOB")]
        
        class MockCurve:
            def __init__(self, mnemonic):
                self.mnemonic = mnemonic
        
        mock_las = MockLAS()
        keywords = {
            'DEPTH': ['DEPTH', 'DEPT', 'MD'],
            'DT': ['DT', 'DTCO', 'P-SONIC'],
            'RHOB': ['RHOB', 'DEN', 'DENSITY']
        }
        
        result = data_io.find_default_columns(mock_las, keywords)
        expected_keys = ['DEPTH', 'DT', 'RHOB']
        
        if all(key in result for key in expected_keys):
            print("✓ find_default_columns works correctly")
        else:
            print(f"✗ find_default_columns failed. Got: {result}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing data_io functions: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_enhanced_dialog_class():
    """Test that the EnhancedDepthDialog class can be instantiated."""
    print("\nTesting EnhancedDepthDialog class...")
    
    try:
        from dialogs.enhanced_depth_dialog import EnhancedDepthDialog
        
        dialog = EnhancedDepthDialog()
        print("✓ EnhancedDepthDialog instantiated successfully")
        
        # Test that methods exist
        if hasattr(dialog, 'get_depth_ranges'):
            print("✓ get_depth_ranges method exists")
        else:
            print("✗ get_depth_ranges method missing")
            return False
            
        if hasattr(dialog, 'select_boundaries_from_excel'):
            print("✓ select_boundaries_from_excel method exists")
        else:
            print("✗ select_boundaries_from_excel method missing")
            return False
            
    except Exception as e:
        print(f"✗ Error testing EnhancedDepthDialog: {e}")
        traceback.print_exc()
        return False
    
    return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("EXCEL DEPTH PROCESSING INTEGRATION TEST")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_data_io_functions,
        test_enhanced_dialog_class,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed!")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! Integration appears successful.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
