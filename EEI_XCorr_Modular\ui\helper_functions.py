# ui/helper_functions.py
import numpy as np
import logging

logger = logging.getLogger(__name__)

def safe_format_float(value, precision=4, default="N/A"):
    """
    Safely format a float value with proper None handling.

    Args:
        value: The value to format (can be None, float, int, or string)
        precision: Number of decimal places
        default: Default string to return if value is None or invalid

    Returns:
        Formatted string
    """
    try:
        if value is None:
            logger.warning(f"safe_format_float: Received None value, returning default: {default}")
            return default

        if isinstance(value, (int, float)) and not (np.isnan(value) if hasattr(np, 'isnan') else False):
            return f"{float(value):.{precision}f}"
        else:
            logger.warning(f"safe_format_float: Invalid value type or NaN: {value} (type: {type(value)}), returning default: {default}")
            return default
    except (ValueError, TypeError) as e:
        logger.error(f"safe_format_float: Error formatting value {value}: {str(e)}, returning default: {default}")
        return default

def safe_format_parameter_string(n_value, phi_value, default="N/A"):
    """
    Safely format parameter strings for CPEI/PEIL analysis.

    Args:
        n_value: The n parameter value
        phi_value: The phi parameter value
        default: Default string to return if values are invalid

    Returns:
        Formatted parameter string
    """
    try:
        if n_value is None or phi_value is None:
            logger.warning(f"safe_format_parameter_string: Received None values - n: {n_value}, phi: {phi_value}")
            return f"n={n_value}, phi={phi_value}"

        n_str = safe_format_float(n_value, precision=1, default=str(n_value))
        phi_str = f"{phi_value}°" if phi_value is not None else str(phi_value)

        return f"n={n_str}, phi={phi_str}"
    except Exception as e:
        logger.error(f"safe_format_parameter_string: Error formatting parameters n={n_value}, phi={phi_value}: {str(e)}")
        return f"n={n_value}, phi={phi_value}"

def validate_cpei_peil_inputs(pvel, svel, rhob, n, phi, analysis_type="CPEI"):
    """
    Validate inputs for CPEI/PEIL calculations and log any issues.

    Args:
        pvel: P-wave velocity array
        svel: S-wave velocity array
        rhob: Density array
        n: Exponent parameter
        phi: Angle parameter
        analysis_type: Type of analysis ("CPEI" or "PEIL")

    Returns:
        dict: Validation results with 'valid' boolean and 'errors' list
    """
    errors = []

    try:
        # Check for None values
        if pvel is None:
            errors.append("P-wave velocity (pvel) is None")
        if svel is None:
            errors.append("S-wave velocity (svel) is None")
        if rhob is None:
            errors.append("Density (rhob) is None")
        if n is None:
            errors.append(f"{analysis_type} parameter n is None")
        if phi is None:
            errors.append(f"{analysis_type} parameter phi is None")

        # Check array shapes and data validity
        if pvel is not None and hasattr(pvel, 'shape'):
            if pvel.size == 0:
                errors.append("P-wave velocity array is empty")
            elif np.all(np.isnan(pvel)):
                errors.append("P-wave velocity array contains only NaN values")

        if svel is not None and hasattr(svel, 'shape'):
            if svel.size == 0:
                errors.append("S-wave velocity array is empty")
            elif np.all(np.isnan(svel)):
                errors.append("S-wave velocity array contains only NaN values")

        if rhob is not None and hasattr(rhob, 'shape'):
            if rhob.size == 0:
                errors.append("Density array is empty")
            elif np.all(np.isnan(rhob)):
                errors.append("Density array contains only NaN values")

        # Check parameter ranges
        if n is not None:
            try:
                n_float = float(n)
                if n_float <= 0 or n_float > 10: # Basic range check
                    errors.append(f"{analysis_type} parameter n ({n_float}) is outside reasonable range (0, 10]")
            except (ValueError, TypeError):
                errors.append(f"{analysis_type} parameter n ({n}) is not a valid number")

        if phi is not None:
            try:
                phi_float = float(phi)
                if phi_float < -90 or phi_float > 90: # Standard angle range
                    errors.append(f"{analysis_type} parameter phi ({phi_float}) is outside range [-90, 90]")
            except (ValueError, TypeError):
                errors.append(f"{analysis_type} parameter phi ({phi}) is not a valid number")

        if errors:
            logger.warning(f"{analysis_type} input validation failed: {'; '.join(errors)}")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    except Exception as e:
        error_msg = f"Unexpected error during {analysis_type} input validation: {str(e)}"
        logger.error(error_msg)
        return {
            'valid': False,
            'errors': [error_msg]
        }