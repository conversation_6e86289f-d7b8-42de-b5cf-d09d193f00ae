#!/usr/bin/env python3
"""Test script for the enhanced plot settings dialog."""

import tkinter as tk
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from dialogs.plot_settings_dialog import PlotSettingsDialog
    print("✓ Successfully imported PlotSettingsDialog")
except ImportError as e:
    print(f"✗ Failed to import PlotSettingsDialog: {e}")
    sys.exit(1)

def test_dialog():
    """Test the dialog with mock data."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    # Mock LAS file data
    class MockLAS:
        def __init__(self, well_name, curves):
            self.well = type('obj', (object,), {'WELL': type('obj', (object,), {'value': well_name})})()
            self.curves = curves
    
    # Create mock LAS files with class data
    mock_las_files = [
        MockLAS("Well1", {"DEPTH": None, "DT": None, "RHOB": None, "FLUID_CODE": [1, 2, 3, 1, 2]}),
        MockLAS("Well2", {"DEPTH": None, "DT": None, "RHOB": None, "FLUID_CODE": [2, 3, 1, 2, 3]}),
    ]
    
    try:
        # Test dialog creation
        dialog = PlotSettingsDialog(
            parent=root,
            z_col=None,
            class_col="FLUID_CODE",
            las_files=mock_las_files
        )
        print("✓ Successfully created PlotSettingsDialog with class column")
        
        # Don't actually show the dialog in test mode
        dialog.destroy()
        
    except Exception as e:
        print(f"✗ Failed to create dialog: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    root.destroy()
    return True

if __name__ == "__main__":
    print("Testing enhanced PlotSettingsDialog...")
    success = test_dialog()
    if success:
        print("✓ All tests passed!")
    else:
        print("✗ Tests failed!")
        sys.exit(1)
