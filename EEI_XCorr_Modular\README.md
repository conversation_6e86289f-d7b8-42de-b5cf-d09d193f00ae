# EEI XCorr Modular - Extended Elastic Impedance Analysis Tool

## Overview

**EEI XCorr Modular** is a comprehensive Python application for Extended Elastic Impedance (EEI) analysis of well log data in the oil and gas industry. The tool enables geoscientists and petrophysicists to perform advanced seismic reservoir characterization through correlation analysis between EEI attributes and target logs.

### Key Capabilities

- **Multi-well LAS File Processing**: Load and analyze multiple LAS (Log ASCII Standard) files simultaneously
- **Extended Elastic Impedance (EEI) Calculations**: Compute EEI with variable chi angles for optimal target log correlation
- **CPEI and PEIL Optimization**: Calculate Correlation-based P-wave Elastic Impedance and Porosity-Elastic Impedance Logs
- **Statistical Analysis**: Comprehensive correlation analysis with statistical validation
- **Interactive Visualization**: Generate cross-plots, histograms, and correlation heatmaps
- **Batch Processing**: Automated analysis across multiple wells with boundary detection
- **Modular Architecture**: Clean, maintainable code structure with separate UI and calculation modules

## Features

### Core Analysis Features

- **Multi-well EEI Analysis**: Process 8+ wells simultaneously with automatic well matching
- **Optimal Chi Angle Detection**: Automatically find the best EEI angle for maximum correlation with target logs
- **Boundary Detection**: Automatic depth range detection from Excel files or manual specification
- **Log Inventory Management**: Comprehensive log curve detection and categorization
- **Derived Log Calculations**: Support for calculated logs and custom log creation

### Data Processing

- **LAS File Validation**: Robust validation of essential logs (DT, DTS, RHOB, target logs)
- **Log Curve Mapping**: Intelligent mapping of various log mnemonics to standard types
- **Data Quality Checks**: Comprehensive data validation and error handling
- **Statistical Integration**: Advanced statistical analysis with percentile-based scaling

### Visualization & Output

- **Cross-plot Generation**: EEI vs target log correlation plots with well-specific data
- **Statistical Analysis Plots**: Distribution histograms and parameter analysis
- **Correlation Heatmaps**: Multi-angle correlation analysis visualization
- **Summary Reports**: Comprehensive analysis reports with optimization results

## Requirements

### System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Python**: 3.8 or higher
- **Memory**: 8GB RAM minimum, 16GB recommended for large datasets
- **Storage**: 500MB free space for application and data

### Dependencies

```txt
numpy>=1.21.0
pandas>=1.3.0
lasio>=0.29
matplotlib>=3.5.0
scipy>=1.7.0
tkinter (included with Python)
openpyxl>=3.0.7 (for Excel file processing)
```

### Required Files

- **LAS Files**: Well log data in LAS format (minimum: DT, DTS, RHOB, target logs)
- **Excel Boundary File**: Well boundary definitions (optional, for automated depth ranges)
- **Python Environment**: Activated virtual environment for dependency management

## Installation

### 1. Environment Setup

```bash
# Create and activate virtual environment
python -m venv eei_env
# On Windows:
eei_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Project Structure Setup

The project expects the following structure:
```
EEI_XCorr_Modular/
├── load_multilas_EEI_XCOR_PLOT_Final.py    # Main entry point
├── eei_calculation_engine.py              # Core calculation logic
├── eei_data_processing.py                 # Data processing utilities
├── eei_config.py                         # Configuration settings
├── eeimpcalc.py                          # EEI calculation functions
├── ui/                                   # User interface modules
│   ├── calculator_interface.py
│   ├── dialog_systems.py
│   ├── file_management.py
│   ├── helper_functions.py
│   ├── plotting_components.py
│   ├── statistical_analysis.py
│   └── workflow_orchestration.py
├── Archives/                            # Documentation and archives
└── __pycache__/                        # Python bytecode cache
```

### 3. Data Preparation

#### LAS Files
- Ensure LAS files contain required logs: DT, DTS, RHOB
- Verify consistent units across files
- Check for null/invalid values in critical logs

#### Excel Boundary File (Optional)
- Well name column matching LAS file names
- Top and Base depth columns
- Format: Excel file with well boundaries for automated depth range detection

## Usage

### Running the Application

1. **Activate Environment**
   ```bash
   # Windows PowerShell
   your_env\Scripts\Activate.ps1
   ```

2. **Start Application**
   ```bash
   cd /path/to/EEI_XCorr_Modular
   python load_multilas_EEI_XCOR_PLOT_Final.py
   ```

### Workflow Overview

1. **File Selection**: Select multiple LAS files for analysis
2. **Log Inventory**: Review available log curves and their distribution
3. **Target Log Selection**: Choose target log for correlation (e.g., GSAT, porosity, etc.)
4. **Boundary Definition**: Set analysis depth ranges (manual or from Excel)
5. **Analysis Type Selection**: Choose EEI, CPEI, or PEIL analysis
6. **Optimization**: Automatic optimization for best correlation angle
7. **Results Visualization**: Generate plots and statistical analysis
8. **Output Generation**: Save results and plots

### Configuration

Key configuration parameters in `eei_config.py`:

```python
# Log keyword mappings for automatic detection
LOG_KEYWORDS = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC'],
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY'],
    # ... additional mappings
}

# Analysis parameter ranges
ANALYSIS_PARAMS = {
    'CPEI': {
        'n_range': {'min': 0.1, 'max': 2.0, 'step': 0.1},
        'phi_range': {'min': -90, 'max': 90, 'step': 1}
    }
}
```

## File Formats

### Input Formats

- **LAS Files**: Standard LAS 2.0 or 3.0 format well logs
- **Excel Files**: .xlsx format for well boundaries and metadata
- **Configuration**: Python modules for parameter settings

### Output Formats

- **Plots**: PNG format cross-plots and statistical visualizations
- **Reports**: Text-based analysis summaries in console output
- **Data**: NumPy arrays and Pandas DataFrames for further analysis

## Project Structure

```
EEI_XCorr_Modular/
├── 📁 Core Modules
│   ├── load_multilas_EEI_XCOR_PLOT_Final.py    # Main application entry point
│   ├── eei_calculation_engine.py              # EEI/CPEI/PEIL calculations
│   ├── eei_data_processing.py                 # Data validation & processing
│   ├── eei_config.py                         # Configuration & constants
│   └── eeimpcalc.py                          # Legacy EEI calculations
│
├── 📁 User Interface
│   ├── calculator_interface.py               # Custom log calculator
│   ├── dialog_systems.py                     # UI dialog management
│   ├── file_management.py                    # LAS/Excel file handling
│   ├── helper_functions.py                   # Utility functions
│   ├── plotting_components.py                # Visualization components
│   ├── statistical_analysis.py               # Statistical computations
│   └── workflow_orchestration.py             # Analysis workflow control
│
├── 📁 Archives
│   └── Finished_Summary/                     # Documentation & references
│       ├── EEI_Formula.md                    # EEI calculation reference
│       ├── EI_Formula.md                     # EI calculation reference
│       └── *.md                              # Progress reports & docs
│
└── 📁 __pycache__/                          # Python bytecode cache
```

## Development

### Code Architecture

- **Modular Design**: Separate concerns with dedicated modules
- **Stateless Calculations**: Pure functions for reliable computations
- **Error Handling**: Comprehensive validation and error recovery
- **Logging**: Detailed logging for debugging and monitoring

### Adding New Features

1. **New Log Types**: Add mappings in `eei_config.py`
2. **New Calculations**: Extend `EEICalculationEngine` class
3. **New Visualizations**: Add methods to `plotting_components.py`
4. **New UI Components**: Extend dialog systems in `ui/` modules

### Testing

```bash
# Run with sample data
python load_multilas_EEI_XCOR_PLOT_Final.py

# Check logs for errors
# Review generated plots in working directory
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed in activated environment
2. **LAS File Issues**: Verify LAS files have required logs (DT, DTS, RHOB)
3. **Memory Issues**: For large datasets, ensure sufficient RAM (16GB+ recommended)
4. **Plot Display Issues**: Check matplotlib backend configuration

### Debug Mode

Enable detailed logging by modifying the logging configuration in the main file:

```python
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
```

## Contributing

### Development Guidelines

- Follow PEP 8 style guidelines
- Add docstrings to all functions and classes
- Include error handling for user inputs
- Test with multiple LAS file formats
- Update documentation for new features

### Version Control

- Use Git for version control
- Follow conventional commit message format
- Create feature branches for new development
- Submit pull requests for code review

## License

This project is developed for internal use by PT Pertamina (Persero). All rights reserved.

## Support

For technical support or questions regarding this application, contact the development team or refer to the archived documentation in the `Archives/Finished_Summary/` directory.

---

**Last Updated**: October 2025
**Version**: 1.0.0
**Authors**: Devri Agustianto, Refactoring Team
