"""Dialog for comprehensive plot configuration settings."""

from __future__ import annotations

from typing import Any, Dict, List, Optional

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import numpy as np
import matplotlib.pyplot as plt

import config


class PlotSettingsDialog(tk.Toplevel):
    """Comprehensive plot configuration dialog matching reference implementation."""

    def __init__(self, parent: tk.Misc, z_col: Optional[str] = None, class_col: Optional[str] = None, las_files: Optional[List] = None) -> None:
        super().__init__(parent)
        self.z_col = z_col
        self.class_col = class_col
        self.las_files = las_files or []
        self.result: Optional[Dict[str, Any]] = None
        self.title("Plot Configuration Settings")
        self.geometry("700x800")

        # Class customization variables
        self.symbol_vars: Dict[Any, tk.StringVar] = {}
        self.color_vars: Dict[Any, tk.StringVar] = {}
        self.color_buttons: Dict[Any, tk.Button] = {}
        self.class_names_vars: Dict[Any, tk.Entry] = {}

        self._build_ui()

    def _build_ui(self) -> None:
        """Build comprehensive plot configuration interface."""
        # Create main frame with scrollbar
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create canvas and scrollbar
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Initialize variables
        self.point_size_var = tk.StringVar(value="50")
        self.bin_size_var = tk.StringVar(value="30")
        self.x_title_var = tk.StringVar(value="")
        self.y_title_var = tk.StringVar(value="")

        # Plot Type Selection
        plot_type_frame = ttk.LabelFrame(scrollable_frame, text="Plot Type Configuration", padding="10")
        plot_type_frame.pack(fill=tk.X, pady=(0, 10))

        # Marginal plot options
        ttk.Label(plot_type_frame, text="Marginal Plot Type:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.marginal_plot_type = tk.StringVar(value="Both")
        marginal_combo = ttk.Combobox(plot_type_frame, textvariable=self.marginal_plot_type,
                                     values=["Histogram", "KDE", "Both", "None"], state="readonly")
        marginal_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=2)

        # Basic Plot Settings
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Plot Settings", padding="10")
        basic_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(basic_frame, text="Point Size:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(basic_frame, textvariable=self.point_size_var, width=15).grid(row=0, column=1, sticky="ew", padx=5, pady=2)

        ttk.Label(basic_frame, text="Histogram Bins:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(basic_frame, textvariable=self.bin_size_var, width=15).grid(row=1, column=1, sticky="ew", padx=5, pady=2)

        # Axis Labels
        labels_frame = ttk.LabelFrame(scrollable_frame, text="Axis Labels", padding="10")
        labels_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(labels_frame, text="X-Axis Title:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(labels_frame, textvariable=self.x_title_var, width=30).grid(row=0, column=1, sticky="ew", padx=5, pady=2)

        ttk.Label(labels_frame, text="Y-Axis Title:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(labels_frame, textvariable=self.y_title_var, width=30).grid(row=1, column=1, sticky="ew", padx=5, pady=2)

        # Z-Column (Colormap) Settings
        if self.z_col:
            colormap_frame = ttk.LabelFrame(scrollable_frame, text="Colormap Settings (Z-Axis)", padding="10")
            colormap_frame.pack(fill=tk.X, pady=(0, 10))

            self.colormap_var = tk.StringVar(value="viridis")
            ttk.Label(colormap_frame, text="Colormap:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
            colormap_combo = ttk.Combobox(
                colormap_frame,
                textvariable=self.colormap_var,
                values=config.get_colormap_options("Sequential"),
                state="readonly"
            )
            colormap_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=2)

            # Colormap category selection
            ttk.Label(colormap_frame, text="Colormap Category:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
            self.colormap_category = tk.StringVar(value="Sequential")
            category_combo = ttk.Combobox(colormap_frame, textvariable=self.colormap_category,
                                        values=["Sequential", "Diverging", "Qualitative"], state="readonly")
            category_combo.grid(row=1, column=1, sticky="ew", padx=5, pady=2)

            # Update colormap options when category changes
            def update_colormap_options(*args):
                category = self.colormap_category.get()
                colormap_combo['values'] = config.get_colormap_options(category)
                if self.colormap_var.get() not in colormap_combo['values']:
                    self.colormap_var.set(colormap_combo['values'][0] if colormap_combo['values'] else "viridis")

            self.colormap_category.trace_add("write", update_colormap_options)

            # Colorbar options
            self.show_cb_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(colormap_frame, text="Show Colorbar", variable=self.show_cb_var).grid(row=2, column=0, columnspan=2, sticky="w", padx=5, pady=2)

            self.reverse_colormap_var = tk.BooleanVar(value=False)
            ttk.Checkbutton(colormap_frame, text="Reverse Colormap", variable=self.reverse_colormap_var).grid(row=3, column=0, columnspan=2, sticky="w", padx=5, pady=2)

            colormap_frame.columnconfigure(1, weight=1)

        # Class Column Settings
        if self.class_col:
            class_frame = ttk.LabelFrame(scrollable_frame, text="Class Visualization Settings", padding="10")
            class_frame.pack(fill=tk.X, pady=(0, 10))

            # Legend style
            ttk.Label(class_frame, text="Legend Style:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
            self.legend_style = tk.StringVar(value="Well-Class")
            legend_combo = ttk.Combobox(class_frame, textvariable=self.legend_style,
                                      values=["Well-Class", "Class-Only", "Simple"], state="readonly")
            legend_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=2)

            class_frame.columnconfigure(1, weight=1)

            # Add class customization section
            self._build_class_customization(scrollable_frame)

        # Legend Settings
        legend_frame = ttk.LabelFrame(scrollable_frame, text="Legend Settings", padding="10")
        legend_frame.pack(fill=tk.X, pady=(0, 10))

        self.legend_font_size = tk.StringVar(value="10")
        ttk.Label(legend_frame, text="Legend Font Size:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        ttk.Entry(legend_frame, textvariable=self.legend_font_size, width=15).grid(row=0, column=1, sticky="ew", padx=5, pady=2)

        self.legend_font_weight = tk.StringVar(value="normal")
        ttk.Label(legend_frame, text="Legend Font Weight:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        weight_combo = ttk.Combobox(legend_frame, textvariable=self.legend_font_weight,
                                   values=["normal", "bold"], state="readonly")
        weight_combo.grid(row=1, column=1, sticky="ew", padx=5, pady=2)

        legend_frame.columnconfigure(1, weight=1)

        # Configure grid weights
        basic_frame.columnconfigure(1, weight=1)
        labels_frame.columnconfigure(1, weight=1)
        plot_type_frame.columnconfigure(1, weight=1)

        # Button frame
        button_frame = ttk.Frame(self)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Apply Settings", command=self._on_submit).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.RIGHT, padx=5)

        # Add help button
        ttk.Button(button_frame, text="Help", command=self._show_help).pack(side=tk.LEFT, padx=(20, 5))

    def _build_class_customization(self, parent_frame: ttk.Frame) -> None:
        """Build the class customization section with colors, symbols, and names."""
        if not self.class_col or not self.las_files:
            return

        # Create class customization frame
        class_custom_frame = ttk.LabelFrame(parent_frame, text="Class Customization", padding="10")
        class_custom_frame.pack(fill=tk.X, pady=(0, 10))

        # Get unique classes across all LAS files
        unique_classes = set()
        has_nan_class = False

        for las in self.las_files:
            if self.class_col in las.curves:
                class_data = np.array(las[self.class_col].data)
                # Add non-NaN values
                unique_classes.update(set(class_data[~np.isnan(class_data)]))
                # Check for NaN values
                if np.isnan(class_data).any():
                    has_nan_class = True

        unique_classes = sorted(unique_classes)

        if not unique_classes and not has_nan_class:
            ttk.Label(class_custom_frame, text="No class data found in the selected column.").pack(pady=10)
            return

        # Available symbols
        symbols = ['o', 's', 'D', '^', 'v', '>', '<', 'p', '*', 'h', 'H', '+', 'x', 'd', '|', '_']

        # Instructions
        ttk.Label(class_custom_frame,
                 text="Customize class names, symbols, and colors for each class:").pack(pady=(0, 10))

        # Create headers
        header_frame = ttk.Frame(class_custom_frame)
        header_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(header_frame, text="Class Value", font=("", 9, "bold")).grid(row=0, column=0, padx=5, sticky="w")
        ttk.Label(header_frame, text="Display Name", font=("", 9, "bold")).grid(row=0, column=1, padx=5, sticky="w")
        ttk.Label(header_frame, text="Symbol", font=("", 9, "bold")).grid(row=0, column=2, padx=5, sticky="w")
        ttk.Label(header_frame, text="Color", font=("", 9, "bold")).grid(row=0, column=3, padx=5, sticky="w")

        # Create scrollable frame for class entries
        canvas = tk.Canvas(class_custom_frame, height=200)
        scrollbar_class = ttk.Scrollbar(class_custom_frame, orient="vertical", command=canvas.yview)
        scrollable_class_frame = ttk.Frame(canvas)

        scrollable_class_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_class_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar_class.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar_class.pack(side="right", fill="y")

        # Create entries for each unique class
        for i, class_value in enumerate(unique_classes):
            self._create_class_entry(scrollable_class_frame, class_value, i, symbols)

        # Add NaN class if it exists
        if has_nan_class:
            self._create_class_entry(scrollable_class_frame, 'NaN', len(unique_classes), symbols, is_nan=True)

    def _create_class_entry(self, parent: ttk.Frame, class_value: Any, row: int, symbols: List[str], is_nan: bool = False) -> None:
        """Create a single class customization entry."""
        # Class value label
        display_value = "NaN (No Data)" if is_nan else f"Class {class_value}"
        ttk.Label(parent, text=display_value).grid(row=row, column=0, padx=5, pady=2, sticky="w")

        # Class name entry
        name_entry = tk.Entry(parent, width=15)
        default_name = "No Data" if is_nan else f"Class {class_value}"
        name_entry.insert(0, default_name)
        name_entry.grid(row=row, column=1, padx=5, pady=2)
        self.class_names_vars[class_value] = name_entry

        # Symbol selection
        symbol_var = tk.StringVar()
        default_symbol = "x" if is_nan else symbols[row % len(symbols)]
        symbol_var.set(default_symbol)
        symbol_combo = ttk.Combobox(parent, textvariable=symbol_var, values=symbols, width=8, state="readonly")
        symbol_combo.grid(row=row, column=2, padx=5, pady=2)
        self.symbol_vars[class_value] = symbol_var

        # Color selection
        color_var = tk.StringVar()
        if is_nan:
            default_color = "#808080"  # Gray for NaN
        else:
            # Generate color from viridis colormap
            try:
                cmap = plt.colormaps['viridis']
                # Get unique classes count for proper color distribution
                unique_classes_count = len([las for las in self.las_files if self.class_col in las.curves])
                if unique_classes_count > 1:
                    rgba_color = cmap(row / max(1, unique_classes_count - 1))
                else:
                    rgba_color = cmap(0.5)  # Use middle color if only one class
                default_color = "#{:02x}{:02x}{:02x}".format(
                    int(rgba_color[0] * 255),
                    int(rgba_color[1] * 255),
                    int(rgba_color[2] * 255)
                )
            except Exception:
                # Fallback to a simple color scheme
                colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"]
                default_color = colors[row % len(colors)]
        color_var.set(default_color)
        self.color_vars[class_value] = color_var

        # Color button
        def make_color_chooser(class_val_local: Any) -> callable:
            def choose_color():
                color_result = colorchooser.askcolor(
                    parent=self,
                    title=f"Choose color for {class_val_local}",
                    initialcolor=self.color_vars[class_val_local].get()
                )
                if color_result[1]:
                    self.color_vars[class_val_local].set(color_result[1])
                    self.color_buttons[class_val_local].config(bg=color_result[1])
            return choose_color

        color_button = tk.Button(parent, text="Choose", command=make_color_chooser(class_value), width=8)
        color_button.grid(row=row, column=3, padx=5, pady=2)
        color_button.config(bg=default_color)
        self.color_buttons[class_value] = color_button

    def _on_submit(self) -> None:
        """Validate and collect all plot settings."""
        try:
            point_size = float(self.point_size_var.get())
            bins = int(self.bin_size_var.get())
            legend_font_size = float(self.legend_font_size.get())
        except ValueError:
            messagebox.showerror("Error", "Invalid numeric value in Point Size, Histogram Bins, or Legend Font Size")
            return

        # Basic settings
        self.result = {
            "point_size": point_size,
            "bin_size": bins,
            "x_title_text": self.x_title_var.get(),
            "y_title_text": self.y_title_var.get(),
            "marginal_plot_type": self.marginal_plot_type.get(),
            "legend_font_size": legend_font_size,
            "legend_font_weight": self.legend_font_weight.get(),
            "downsampling_factor": 1,
        }

        # Z-column (colormap) settings
        if self.z_col:
            cmap = self.colormap_var.get()
            if not config.validate_colormap(cmap):
                cmap = "viridis"

            # Apply colormap reversal if requested
            if hasattr(self, 'reverse_colormap_var') and self.reverse_colormap_var.get():
                cmap = config.apply_colormap_reversal(cmap, reverse=True)

            self.result.update({
                "colormap": cmap,
                "show_colorbar": self.show_cb_var.get(),
                "reverse_colormap": getattr(self, 'reverse_colormap_var', tk.BooleanVar()).get(),
                "colormap_category": self.colormap_category.get(),
            })

        # Class column settings
        if self.class_col:
            self.result.update({
                "legend_style": self.legend_style.get(),
            })

            # Collect class customization data
            if self.symbol_vars and self.color_vars and self.class_names_vars:
                symbols = {class_val: var.get() for class_val, var in self.symbol_vars.items()}
                colors = {class_val: var.get() for class_val, var in self.color_vars.items()}
                class_names = {class_val: entry.get() for class_val, entry in self.class_names_vars.items()}

                self.result.update({
                    "symbols": symbols,
                    "colors": colors,
                    "class_names": class_names,
                })
            else:
                # Provide empty defaults if no customization was set up
                self.result.update({
                    "symbols": {},
                    "colors": {},
                    "class_names": {},
                })

        self.destroy()

    def _on_cancel(self) -> None:
        self.result = None
        self.destroy()

    def _show_help(self) -> None:
        """Show help information about plot configuration options."""
        help_text = """
Plot Configuration Help:

PLOT TYPE:
• Marginal Plot Type: Choose histogram, KDE (Kernel Density Estimation), both, or none for the side plots

BASIC SETTINGS:
• Point Size: Size of scatter plot points (default: 50)
• Histogram Bins: Number of bins for histograms (default: 30)

AXIS LABELS:
• X/Y-Axis Title: Custom labels for the axes (leave blank for automatic)

COLORMAP SETTINGS (when Z-column is selected):
• Colormap: Color scheme for data visualization
• Category: Sequential (gradual), Diverging (center-focused), Qualitative (distinct colors)
• Show Colorbar: Display color scale legend
• Reverse Colormap: Flip the color order

CLASS SETTINGS (when Class column is selected):
• Legend Style: How to display class information in the legend

LEGEND SETTINGS:
• Font Size: Size of legend text
• Font Weight: Normal or bold legend text

Click 'Apply Settings' to proceed with plot generation.
        """

        help_window = tk.Toplevel(self)
        help_window.title("Plot Configuration Help")
        help_window.geometry("500x400")

        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, help_text.strip())
        text_widget.config(state=tk.DISABLED)

        ttk.Button(help_window, text="Close", command=help_window.destroy).pack(pady=10)

    def show(self) -> Optional[Dict[str, Any]]:
        """Show the dialog and return the result."""
        self.transient()
        self.grab_set()
        self.wait_window()
        return self.result
