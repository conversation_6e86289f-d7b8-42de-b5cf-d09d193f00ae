#!/usr/bin/env python3
"""
Demonstration of Enhanced Color Customization Features

This script demonstrates the enhanced color customization functionality
for class selection as a third axis in the plotting application.
"""

import numpy as np
import matplotlib.pyplot as plt
from statistical_analyzers import StatisticalVisualizer

def demo_class_based_visualization():
    """Demonstrate class-based color customization."""
    print("Enhanced Color Customization Demo")
    print("=" * 50)
    
    # Create sample data with multiple classes
    np.random.seed(42)
    n_points = 200
    
    # Generate data for 3 different classes
    class_1_x = np.random.normal(2, 0.8, n_points//3)
    class_1_y = np.random.normal(3, 0.6, n_points//3)
    class_1_labels = np.ones(n_points//3)
    
    class_2_x = np.random.normal(5, 1.0, n_points//3)
    class_2_y = np.random.normal(2, 0.8, n_points//3)
    class_2_labels = np.ones(n_points//3) * 2
    
    class_3_x = np.random.normal(3.5, 0.7, n_points//3)
    class_3_y = np.random.normal(5, 0.9, n_points//3)
    class_3_labels = np.ones(n_points//3) * 3
    
    # Add some NaN values to demonstrate NaN handling
    nan_indices = np.random.choice(n_points//3, 5, replace=False)
    class_3_labels[nan_indices] = np.nan
    
    # Combine all data
    x_data = np.concatenate([class_1_x, class_2_x, class_3_x])
    y_data = np.concatenate([class_1_y, class_2_y, class_3_y])
    class_data = np.concatenate([class_1_labels, class_2_labels, class_3_labels])
    
    # Define custom colors and class names (as would be set in the enhanced dialog)
    colors = {
        1.0: "#FF6B6B",  # Red for Class 1
        2.0: "#4ECDC4",  # Teal for Class 2  
        3.0: "#45B7D1",  # Blue for Class 3
        'NaN': "#808080"  # Gray for NaN values
    }
    
    class_names = {
        1.0: "Sandstone",
        2.0: "Limestone", 
        3.0: "Shale",
        'NaN': "No Data"
    }
    
    symbols = {
        1.0: "o",  # Circle
        2.0: "s",  # Square
        3.0: "^",  # Triangle
        'NaN': "x"  # X for NaN
    }
    
    print(f"Generated {len(x_data)} data points with {len(np.unique(class_data[~np.isnan(class_data)]))} classes")
    print(f"Classes: {list(class_names.values())}")
    print(f"Colors: {list(colors.values())}")
    print(f"Symbols: {list(symbols.values())}")
    
    # Create the enhanced statistical visualizer
    visualizer = StatisticalVisualizer(
        x_data=x_data,
        y_data=y_data, 
        class_data=class_data,
        colors=colors,
        class_names=class_names
    )
    
    # Create a demonstration plot
    fig, ((ax_main, ax_histy), (ax_histx, ax_empty)) = plt.subplots(
        2, 2, 
        figsize=(12, 10),
        gridspec_kw={
            'width_ratios': [3, 1], 
            'height_ratios': [1, 3],
            'hspace': 0.05,
            'wspace': 0.05
        }
    )
    
    # Hide the empty subplot
    ax_empty.set_visible(False)
    
    # Create main scatter plot with class-based coloring
    unique_classes = np.unique(class_data[~np.isnan(class_data)])
    
    for class_val in unique_classes:
        mask = (class_data == class_val)
        ax_main.scatter(
            x_data[mask], 
            y_data[mask],
            c=colors.get(class_val, '#000000'),
            marker=symbols.get(class_val, 'o'),
            label=class_names.get(class_val, f'Class {class_val}'),
            s=50,
            alpha=0.7
        )
    
    # Handle NaN values
    nan_mask = np.isnan(class_data)
    if np.any(nan_mask):
        ax_main.scatter(
            x_data[nan_mask],
            y_data[nan_mask], 
            c=colors['NaN'],
            marker=symbols['NaN'],
            label=class_names['NaN'],
            s=50,
            alpha=0.7
        )
    
    # Create marginal plots with class-based coloring
    visualizer.plot_marginal_distributions(
        ax_histx=ax_histx,
        ax_histy=ax_histy,
        plot_type="Both"  # Both histogram and KDE
    )
    
    # Customize the plot
    ax_main.set_xlabel("Porosity (%)", fontsize=12)
    ax_main.set_ylabel("Permeability (mD)", fontsize=12)
    ax_main.legend(fontsize=10, loc='upper right')
    ax_main.grid(True, alpha=0.3)
    
    # Hide tick labels for marginal plots
    ax_histx.set_xticklabels([])
    ax_histy.set_yticklabels([])
    
    # Set title
    fig.suptitle("Enhanced Class-Based Color Customization Demo\nScatter Plot with Class-Colored Marginal Distributions", 
                 fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    
    print("\nFeatures Demonstrated:")
    print("✓ Individual colors for each class")
    print("✓ Custom symbols for each class") 
    print("✓ Meaningful class names")
    print("✓ NaN value handling with special styling")
    print("✓ Class-based histogram coloring")
    print("✓ Class-based KDE curve coloring")
    print("✓ Consistent legend across all plot elements")
    
    print(f"\nPlot shows {len(unique_classes)} distinct classes plus NaN values")
    print("Each class has its own color scheme applied to:")
    print("- Scatter plot points")
    print("- Histogram bars") 
    print("- KDE curves")
    print("- Legend entries")
    
    plt.show()
    
    return True

if __name__ == "__main__":
    print("Starting Enhanced Color Customization Demonstration...")
    try:
        demo_class_based_visualization()
        print("\n✓ Demo completed successfully!")
        print("\nThis demonstrates the enhanced functionality that is now available")
        print("in the PlotSettingsDialog when a class column is selected.")
    except Exception as e:
        print(f"\n✗ Demo failed: {e}")
        import traceback
        traceback.print_exc()
