# Extended Elastic Impedance (EEI) calculation (<PERSON><PERSON><PERSON> et al. 2002, Eq. 19)

import numpy as np

# Set your projection angle chi in degrees (e.g., chi = 12.4 for bulk modulus, chi = -51.3 for shear modulus)
chi = 21
chi_rad = np.radians(chi)

# Input logs: DT, DTS, RHOB (make sure units are consistent)
P_WAVE = 1e6 / DT   # Vp, m/s
S_WAVE = 1e6 / DTS  # Vs, m/s
RHOB = RHOB         # Density log (g/cc or kg/m^3)

# Average Vs/Vp squared ratio (K) for your interval or use as a constant (typical: 0.21–0.25)
#k = 0.243  

# Calculate K at each sample
K_LOG = (S_WAVE ** 2) / (P_WAVE ** 2)
k_ratio = K_LOG
#k_ratio = np.nanmean(K_LOG)

# EEI exponents (<PERSON><PERSON><PERSON> et al. 2002, Eq. 20)
p = np.cos(chi_rad) + np.sin(chi_rad)
q = -8 * k_ratio * np.sin(chi_rad)
r = np.cos(chi_rad) - 4 * k_ratio * np.sin(chi_rad)

# Reference values for normalization (typically mean or baseline values from the logs)
alpha_o = np.nanmean(P_WAVE)
beta_o  = np.nanmean(S_WAVE)
rho_o   = np.nanmean(RHOB)

# Initialize EEI array with NaN values
EEI = np.full_like(P_WAVE, np.nan)

# EEI Calculation (Equation 19)
EEI = (
    alpha_o * rho_o *
    ((P_WAVE / alpha_o) ** p) *
    ((S_WAVE / beta_o) ** q) *
    ((RHOB / rho_o) ** r)
)


<!-- # Set parameters
Xt = np.radians(30)
k = 0.243
ao = 2865
bo = 1410
co = 2.35
AIo = 6744
SIo = 3319 -->

<!-- # Convert RHOB from g/cc to kg/m^3
RHOB_kgm3 = RHOB * 1000.0

# Calculate P-wave and S-wave velocities
P_WAVE = 1e6 / DT  # m/s
S_WAVE = 1e6 / DTS # m/s

# Shear modulus (G), Bulk modulus (K), Lame's first parameter (lambda), and P-wave modulus (M) in GPa
GSAT = RHOB_kgm3 * (S_WAVE ** 2) / 1e9           # GPa
KSAT = (RHOB_kgm3 * (P_WAVE ** 2) - (4.0/3.0) * GSAT * 1e9) / 1e9  # GPa
LSAT = KSAT - (2.0 / 3.0) * GSAT                 # GPa
MSAT = KSAT + (4.0 / 3.0) * GSAT                 # GPa -->