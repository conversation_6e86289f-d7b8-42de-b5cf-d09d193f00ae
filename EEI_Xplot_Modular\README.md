# Xplot - Well Log Data Visualization and Analysis Tool

## Overview

**Xplot** is a comprehensive Python application designed for well log data visualization and analysis in the oil and gas industry. Built specifically for Pertamina's subsurface data analysis needs, this modular application provides an intuitive graphical interface for loading, processing, and visualizing well log data from LAS (Log ASCII Standard) files.

## Features

### Core Functionality
- **Multi-file LAS Loading**: Load and process multiple LAS files simultaneously
- **Interactive Calculator**: Perform calculations on well log data with real-time validation
- **Column Selection**: Choose specific data columns for plotting and analysis
- **Excel Integration**: Import depth ranges from Excel files for enhanced depth processing
- **Advanced Plotting**: Create customizable cross-plots with statistical analysis
- **Data Validation**: Comprehensive data quality checks and validation

### Supported Well Log Types
The application recognizes and processes various well log types including:
- **Sonic Logs**: DT (Compressional), DTS (Shear)
- **Porosity Logs**: PHIT (Total), PHIE (Effective), NPHI (Neutron)
- **Density Logs**: RHOB (Bulk Density), DRHO (Density Correction)
- **Saturation Logs**: SWT (Water Saturation), SWE (Effective Water Saturation)
- **Resistivity Logs**: RT (True Resistivity)
- **Other Logs**: GR (Gamma Ray), CALI (Caliper), PEF (Photoelectric), SP (Spontaneous Potential)

### Visualization Options
- **Multiple Colormap Categories**: Sequential, Diverging, and Qualitative colormaps
- **Statistical Analysis**: Data distribution analysis and validation statistics
- **Customizable Plot Settings**: Flexible plotting parameters and styling options
- **Depth-based Filtering**: Apply depth ranges for focused analysis

## Installation

### Prerequisites
```bash
Python 3.8 or higher
pip (Python package manager)
```

### Required Dependencies
```bash
pip install lasio pandas numpy matplotlib scipy tkinter
```

### Optional Dependencies
For enhanced functionality, consider installing:
```bash
pip install openpyxl  # For Excel file support
```

## Usage

### Running the Application
```bash
python main.py
```

### Workflow
1. **Load LAS Files**: Select and load multiple LAS files through the file dialog
2. **Excel Integration** (Optional): Import depth ranges from Excel files
3. **Calculator Dialog**: Perform calculations on the loaded data
4. **Column Selection**: Choose X, Y, Z, and classification columns for plotting
5. **Plot Settings**: Customize visualization parameters and styling
6. **Generate Plots**: Create and display the final cross-plots

### Configuration
The application includes configurable settings in `config.py`:
- Log type detection keywords
- Available colormap categories
- Default plotting parameters

## Project Structure

```
EEI_Xplot_Modular/
├── main.py                    # Main application entry point
├── config.py                  # Configuration constants and utilities
├── data_io.py                 # LAS and Excel file I/O operations
├── processing.py              # Data processing and calculation engine
├── plotting.py               # Plotting utilities and statistical analysis
├── statistical_analyzers.py   # Advanced statistical analysis tools
├── dialogs/                  # GUI dialog components
│   ├── calculator_dialog.py
│   ├── column_select_dialog.py
│   ├── plot_settings_dialog.py
│   └── ...
├── Archives/                 # Archived project files
└── __init__.py              # Package initialization
```

## Architecture

### Modular Design
The application follows a modular architecture with clear separation of concerns:

- **Data Layer** (`data_io.py`): Handles file I/O and data loading
- **Processing Layer** (`processing.py`): Data manipulation and calculations
- **Visualization Layer** (`plotting.py`): Plot generation and statistical analysis
- **Configuration Layer** (`config.py`): Centralized configuration management
- **UI Layer** (`dialogs/`): User interface components

### Error Handling
- Comprehensive logging throughout the application
- User-friendly error messages and warnings
- Graceful handling of file loading failures
- Validation checks at each workflow step

## Contributing

### Development Setup
1. Clone the repository
2. Install development dependencies
3. Run tests and validation

### Code Style
- Follow PEP 8 guidelines
- Use type hints for function parameters and return values
- Include docstrings for all public functions and classes

## Logging

The application uses Python's built-in logging module:
- **INFO**: General application flow and successful operations
- **WARNING**: Non-critical issues that don't stop execution
- **ERROR**: Critical issues that may affect functionality

## Troubleshooting

### Common Issues
- **LAS File Not Loading**: Ensure the file format is correct and contains valid well log data
- **Memory Issues**: For large datasets, consider processing files in smaller batches
- **Plot Display Issues**: Ensure matplotlib backend is properly configured

### Getting Help
For technical support or feature requests, please refer to the project documentation or contact the development team.

## License

This project is developed for PT Pertamina (Persero) internal use.

## Version History

- **Current Version**: Modular architecture implementation
- **Previous Versions**: Legacy Xplot application (archived)

---

*For questions or support, please contact the subsurface data analysis team at PT Pertamina (Persero).*
