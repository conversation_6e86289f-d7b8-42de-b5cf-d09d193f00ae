# -*- coding: utf-8 -*-
"""
Created on Thu Aug 29 11:15:20 2024

@author: devri.agustianto
"""

# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]

import numpy as np
import lasio
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, colorchooser
import matplotlib.pyplot as plt
from scipy import stats
from scipy import interpolate
import pandas as pd
import re
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Union

# ============================================================================
# CLASS-BASED STATISTICAL VISUALIZATION IMPLEMENTATION
# ============================================================================

class StatisticalAnalyzer(ABC):
    """Abstract base class for statistical analysis and visualization."""

    def __init__(self, data: np.ndarray, class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict] = None, class_names: Optional[Dict] = None):
        """
        Initialize the statistical analyzer.

        Args:
            data: 1D numpy array of data values
            class_data: Optional 1D numpy array of class labels
            colors: Optional dictionary mapping class values to colors
            class_names: Optional dictionary mapping class values to display names
        """
        self.data = np.array(data)
        self.class_data = np.array(class_data) if class_data is not None else None
        self.colors = colors or {}
        self.class_names = class_names or {}
        self._validate_data()

    def _validate_data(self):
        """Validate input data."""
        if len(self.data) == 0:
            raise ValueError("Data array cannot be empty")

        if self.class_data is not None and len(self.class_data) != len(self.data):
            raise ValueError("Class data must have the same length as data array")

    def get_clean_data(self) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Remove NaN values from data and corresponding class data."""
        valid_mask = ~np.isnan(self.data)
        clean_data = self.data[valid_mask]
        clean_class_data = self.class_data[valid_mask] if self.class_data is not None else None
        return clean_data, clean_class_data

    def get_unique_classes(self) -> List:
        """Get unique class values, excluding NaN."""
        if self.class_data is None:
            return []
        clean_data, clean_class_data = self.get_clean_data()
        if clean_class_data is None or len(clean_class_data) == 0:
            return []
        return list(np.unique(clean_class_data[~np.isnan(clean_class_data)]))

    @abstractmethod
    def plot(self, ax, **kwargs):
        """Abstract method for plotting. Must be implemented by subclasses."""
        pass


class HistogramAnalyzer(StatisticalAnalyzer):
    """Class for histogram analysis and visualization."""

    def __init__(self, data: np.ndarray, class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict] = None, class_names: Optional[Dict] = None,
                 bins: int = 30, density: bool = True, alpha: float = 0.7):
        """
        Initialize the histogram analyzer.

        Args:
            data: 1D numpy array of data values
            class_data: Optional 1D numpy array of class labels
            colors: Optional dictionary mapping class values to colors
            class_names: Optional dictionary mapping class values to display names
            bins: Number of histogram bins
            density: Whether to normalize histogram to show density
            alpha: Transparency level for histogram bars
        """
        super().__init__(data, class_data, colors, class_names)
        self.bins = bins
        self.density = density
        self.alpha = alpha

    def _calculate_optimal_bins(self, data: np.ndarray) -> int:
        """Calculate optimal number of bins for the given data."""
        if len(data) == 0:
            return 1

        unique_values = len(np.unique(data))
        if unique_values <= 1:
            return 1

        # Use Sturges' rule as a starting point, but cap it
        optimal_bins = min(self.bins, max(5, unique_values, int(np.ceil(np.log2(len(data)) + 1))))
        return optimal_bins

    def plot(self, ax, orientation: str = 'vertical', **kwargs):
        """
        Plot histogram on the given axes.

        Args:
            ax: Matplotlib axes object
            orientation: 'vertical' or 'horizontal'
            **kwargs: Additional plotting arguments
        """
        clean_data, clean_class_data = self.get_clean_data()

        if len(clean_data) == 0:
            print("Warning: No valid data for histogram")
            return

        # If no class data, plot single histogram
        if clean_class_data is None or len(self.get_unique_classes()) == 0:
            self._plot_single_histogram(ax, clean_data, orientation, **kwargs)
        else:
            self._plot_class_histograms(ax, clean_data, clean_class_data, orientation, **kwargs)

    def _plot_single_histogram(self, ax, data: np.ndarray, orientation: str, **kwargs):
        """Plot a single histogram for all data."""
        bins = self._calculate_optimal_bins(data)

        try:
            if orientation == 'horizontal':
                ax.hist(data, bins=bins, orientation='horizontal',
                       color=kwargs.get('color', 'gray'), alpha=self.alpha, density=self.density)
            else:
                ax.hist(data, bins=bins, color=kwargs.get('color', 'gray'),
                       alpha=self.alpha, density=self.density)
        except Exception as e:
            print(f"Warning: Could not create histogram: {e}")
            # Fallback for single value
            if len(np.unique(data)) == 1:
                if orientation == 'horizontal':
                    ax.axhline(y=data[0], color=kwargs.get('color', 'gray'), alpha=self.alpha)
                else:
                    ax.axvline(x=data[0], color=kwargs.get('color', 'gray'), alpha=self.alpha)

    def _plot_class_histograms(self, ax, data: np.ndarray, class_data: np.ndarray,
                              orientation: str, **kwargs):
        """Plot separate histograms for each class."""
        unique_classes = self.get_unique_classes()

        # Calculate bins based on all data to ensure consistent binning
        bins = self._calculate_optimal_bins(data)

        # Create bin edges based on data range
        if len(np.unique(data)) > 1:
            bin_edges = np.linspace(np.min(data), np.max(data), bins + 1)
        else:
            # Single value case
            single_val = data[0]
            bin_edges = np.array([single_val - 0.5, single_val + 0.5])

        for class_val in unique_classes:
            class_mask = (class_data == class_val)
            class_data_subset = data[class_mask]

            if len(class_data_subset) == 0:
                continue

            color = self.colors.get(class_val, f'C{len(unique_classes) % 10}')
            label = self.class_names.get(class_val, f'Class {class_val}')

            try:
                if orientation == 'horizontal':
                    ax.hist(class_data_subset, bins=bin_edges, orientation='horizontal',
                           color=color, alpha=self.alpha, density=self.density,
                           label=label, histtype='step', linewidth=2)
                else:
                    ax.hist(class_data_subset, bins=bin_edges, color=color,
                           alpha=self.alpha, density=self.density, label=label,
                           histtype='step', linewidth=2)
            except Exception as e:
                print(f"Warning: Could not create histogram for class {class_val}: {e}")


class KDEAnalyzer(StatisticalAnalyzer):
    """Class for Kernel Density Estimation analysis and visualization."""

    def __init__(self, data: np.ndarray, class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict] = None, class_names: Optional[Dict] = None,
                 bandwidth: Optional[float] = None, n_points: int = 100):
        """
        Initialize the KDE analyzer.

        Args:
            data: 1D numpy array of data values
            class_data: Optional 1D numpy array of class labels
            colors: Optional dictionary mapping class values to colors
            class_names: Optional dictionary mapping class values to display names
            bandwidth: KDE bandwidth (None for automatic selection)
            n_points: Number of points for KDE curve
        """
        super().__init__(data, class_data, colors, class_names)
        self.bandwidth = bandwidth
        self.n_points = n_points

    def plot(self, ax, orientation: str = 'vertical', data_range: Optional[Tuple[float, float]] = None, **kwargs):
        """
        Plot KDE on the given axes.

        Args:
            ax: Matplotlib axes object
            orientation: 'vertical' or 'horizontal'
            data_range: Tuple of (min, max) for KDE evaluation range
            **kwargs: Additional plotting arguments
        """
        clean_data, clean_class_data = self.get_clean_data()

        if len(clean_data) == 0:
            print("Warning: No valid data for KDE")
            return

        # If no class data, plot single KDE
        if clean_class_data is None or len(self.get_unique_classes()) == 0:
            self._plot_single_kde(ax, clean_data, orientation, data_range, **kwargs)
        else:
            self._plot_class_kdes(ax, clean_data, clean_class_data, orientation, data_range, **kwargs)

    def _plot_single_kde(self, ax, data: np.ndarray, orientation: str,
                        data_range: Optional[Tuple[float, float]], **kwargs):
        """Plot a single KDE for all data."""
        if len(data) < 2:
            print("Warning: KDE requires at least 2 data points")
            # Fallback for insufficient data
            if len(data) == 1:
                if orientation == 'horizontal':
                    ax.axhline(y=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
                else:
                    ax.axvline(x=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
            return

        # Check for zero variance
        if np.var(data) <= 1e-10:
            print("Warning: Data has zero variance, using line instead of KDE")
            if orientation == 'horizontal':
                ax.axhline(y=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
            else:
                ax.axvline(x=data[0], color=kwargs.get('color', 'blue'), linestyle='--')
            return

        try:
            kde = stats.gaussian_kde(data)
            if self.bandwidth is not None:
                kde.set_bandwidth(self.bandwidth)

            # Determine evaluation range
            if data_range is not None:
                eval_range = np.linspace(data_range[0], data_range[1], self.n_points)
            else:
                data_min, data_max = np.min(data), np.max(data)
                range_padding = (data_max - data_min) * 0.1
                eval_range = np.linspace(data_min - range_padding, data_max + range_padding, self.n_points)

            kde_values = kde(eval_range)

            if orientation == 'horizontal':
                ax.plot(kde_values, eval_range, color=kwargs.get('color', 'blue'),
                       linestyle='--', linewidth=2)
            else:
                ax.plot(eval_range, kde_values, color=kwargs.get('color', 'blue'),
                       linestyle='--', linewidth=2)

        except Exception as e:
            print(f"Warning: Could not create KDE: {e}")

    def _plot_class_kdes(self, ax, data: np.ndarray, class_data: np.ndarray,
                        orientation: str, data_range: Optional[Tuple[float, float]], **kwargs):
        """Plot separate KDEs for each class."""
        unique_classes = self.get_unique_classes()

        for class_val in unique_classes:
            class_mask = (class_data == class_val)
            class_data_subset = data[class_mask]

            if len(class_data_subset) < 2:
                print(f"Warning: Class {class_val} has insufficient data for KDE")
                continue

            if np.var(class_data_subset) <= 1e-10:
                print(f"Warning: Class {class_val} has zero variance")
                continue

            color = self.colors.get(class_val, f'C{len(unique_classes) % 10}')
            label = self.class_names.get(class_val, f'Class {class_val}')

            try:
                kde = stats.gaussian_kde(class_data_subset)
                if self.bandwidth is not None:
                    kde.set_bandwidth(self.bandwidth)

                # Determine evaluation range
                if data_range is not None:
                    eval_range = np.linspace(data_range[0], data_range[1], self.n_points)
                else:
                    data_min, data_max = np.min(data), np.max(data)
                    range_padding = (data_max - data_min) * 0.1
                    eval_range = np.linspace(data_min - range_padding, data_max + range_padding, self.n_points)

                kde_values = kde(eval_range)

                if orientation == 'horizontal':
                    ax.plot(kde_values, eval_range, color=color, linestyle='--',
                           linewidth=2, label=label)
                else:
                    ax.plot(eval_range, kde_values, color=color, linestyle='--',
                           linewidth=2, label=label)

            except Exception as e:
                print(f"Warning: Could not create KDE for class {class_val}: {e}")


class StatisticalVisualizer:
    """Combined class for managing histogram and KDE visualizations."""

    def __init__(self, x_data: np.ndarray, y_data: np.ndarray,
                 class_data: Optional[np.ndarray] = None,
                 colors: Optional[Dict] = None, class_names: Optional[Dict] = None):
        """
        Initialize the statistical visualizer.

        Args:
            x_data: X-axis data
            y_data: Y-axis data
            class_data: Optional class labels
            colors: Optional dictionary mapping class values to colors
            class_names: Optional dictionary mapping class values to display names
        """
        self.x_data = np.array(x_data)
        self.y_data = np.array(y_data)
        self.class_data = np.array(class_data) if class_data is not None else None
        self.colors = colors or {}
        self.class_names = class_names or {}

        # Create analyzers for X and Y data
        self.x_histogram = HistogramAnalyzer(self.x_data, self.class_data, colors, class_names)
        self.y_histogram = HistogramAnalyzer(self.y_data, self.class_data, colors, class_names)
        self.x_kde = KDEAnalyzer(self.x_data, self.class_data, colors, class_names)
        self.y_kde = KDEAnalyzer(self.y_data, self.class_data, colors, class_names)

    def plot_marginal_distributions(self, ax_histx, ax_histy, plot_type: str = 'Both',
                                   x_range: Optional[Tuple[float, float]] = None,
                                   y_range: Optional[Tuple[float, float]] = None,
                                   **kwargs):
        """
        Plot marginal distributions on the given axes.

        Args:
            ax_histx: Axes for X marginal plot (top)
            ax_histy: Axes for Y marginal plot (right)
            plot_type: 'Histogram', 'KDE', or 'Both'
            x_range: Range for X-axis evaluation
            y_range: Range for Y-axis evaluation
            **kwargs: Additional plotting arguments
        """
        # Plot histograms
        if plot_type in ['Histogram', 'Both']:
            self.x_histogram.plot(ax_histx, orientation='vertical', **kwargs)
            self.y_histogram.plot(ax_histy, orientation='horizontal', **kwargs)

        # Plot KDEs
        if plot_type in ['KDE', 'Both']:
            self.x_kde.plot(ax_histx, orientation='vertical', data_range=x_range, **kwargs)
            self.y_kde.plot(ax_histy, orientation='horizontal', data_range=y_range, **kwargs)

# ============================================================================
# END OF CLASS-BASED IMPLEMENTATION
# ============================================================================

# Define keywords for broader log detection (similar to reference file)
# This helps in identifying common log types with various mnemonics
log_keywords = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC', 'AC', 'DT4P'],  # P-wave slowness
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC', 'DT4S', 'SHEAR_DT'],  # S-wave slowness
    'PHIT': ['PHIT', 'PHID', 'PHI_D', 'POR', 'TOTAL_POR'],
    'PHIE': ['PHIE', 'PHIE_D', 'EFF_POR', 'EFFECTIVE_POR'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO', 'BULK_DEN'],
    'SWT': ['SWT', 'SW', 'WATER_SAT', 'SAT_WATER'],
    'SWE': ['SWE', 'SWE_D', 'SW_EFF'],
    'DEPTH': ['DEPTH', 'DEPT', 'MD', 'MEASURED_DEPTH'],
    'P-WAVE': ['P-WAVE', 'P_VELOCITY', 'VP', 'VELP'],
    'S-WAVE': ['S-WAVE', 'S_VELOCITY', 'VS', 'VELS'],
    'FLUID_CODE': ['FLUID_CODE', 'FLUID', 'FLUID_ID'],
    'FLUID_PETREL': ['FLUID_PETREL'], # Specific to Petrel workflows
    'LITHO_CODE': ['LITHO_CODE', 'LITHOLOGY', 'LITHO_PETREL', 'LITH'], # Added LITHO_PETREL
    'GR': ['GR', 'GAMMA_RAY', 'GR_LOG', 'CGR', 'SGR'],
    'NPHI': ['NPHI', 'NEUTRON', 'NEUTRON_POROSITY', 'NPOR'],
    'VCL': ['VCL', 'VOL_WETCLAY', 'V_CLAY', 'VSH', 'SHALE_VOL'], # Added VSH
    'RT': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD', 'AT90', 'DEEP_RES'],
    # Additional potentially useful logs for crossplotting or calculations
    'CALI': ['CALI', 'CAL', 'CALIPER'],
    'PEF': ['PEF', 'PE', 'PHOTOELECTRIC'],
    'SP': ['SP', 'SPONTANEOUS_POTENTIAL'],
    'DRHO': ['DRHO', 'DENSITY_CORRECTION', 'DELTARHO'],
}
# Enhanced Colormap Categories for Z-axis visualization
COLORMAP_CATEGORIES = {
    'Sequential': {
        'Perceptually Uniform': ['viridis', 'plasma', 'inferno', 'magma', 'cividis'],
        'Single Hue': ['Blues', 'BuGn', 'BuPu', 'GnBu', 'Greens', 'Greys', 'Oranges',
                       'OrRd', 'PuBu', 'PuBuGn', 'PuRd', 'Purples', 'RdPu', 'Reds',
                       'YlGn', 'YlGnBu', 'YlOrBr', 'YlOrRd'],
        'Multi Hue': ['rainbow', 'turbo', 'jet', 'hsv', 'gist_rainbow', 'gist_ncar',
                      'nipy_spectral', 'CMRmap']
    },
    'Diverging': ['RdYlBu', 'RdBu', 'coolwarm', 'seismic', 'bwr', 'RdGy', 'PiYG',
                  'PRGn', 'BrBG', 'RdYlGn', 'Spectral'],
    'Qualitative': ['Set1', 'Set2', 'Set3', 'tab10', 'tab20', 'tab20b', 'tab20c',
                    'Pastel1', 'Pastel2', 'Paired', 'Accent', 'Dark2']
}

def get_colormap_options(category, subcategory=None):
    """
    Get colormap options based on category and subcategory.

    Args:
        category (str): Main category ('Sequential', 'Diverging', 'Qualitative')
        subcategory (str): Subcategory for Sequential maps (optional)

    Returns:
        list: List of available colormaps
    """
    if category not in COLORMAP_CATEGORIES:
        return ['viridis']  # Fallback

    if category == 'Sequential' and subcategory:
        return COLORMAP_CATEGORIES[category].get(subcategory, ['viridis'])
    elif category == 'Sequential':
        # Return all sequential colormaps if no subcategory specified
        all_sequential = []
        for subcat_maps in COLORMAP_CATEGORIES[category].values():
            all_sequential.extend(subcat_maps)
        return all_sequential
    else:
        return COLORMAP_CATEGORIES[category]

def get_sequential_subcategories():
    """Get list of Sequential colormap subcategories."""
    return list(COLORMAP_CATEGORIES['Sequential'].keys())

def validate_colormap(colormap_name):
    """
    Validate if a colormap is available in matplotlib.

    Args:
        colormap_name (str): Name of the colormap to validate

    Returns:
        bool: True if colormap is available, False otherwise
    """
    try:
        plt.colormaps[colormap_name]
        return True
    except KeyError:
        return False

def apply_colormap_reversal(colormap_name, reverse=False):
    """
    Apply reversal to colormap name if requested.

    Args:
        colormap_name (str): Base colormap name
        reverse (bool): Whether to reverse the colormap

    Returns:
        str: Final colormap name (with _r suffix if reversed)
    """
    if reverse and not colormap_name.endswith('_r'):
        return f"{colormap_name}_r"
    elif not reverse and colormap_name.endswith('_r'):
        return colormap_name[:-2]  # Remove _r suffix
    return colormap_name

def load_multiple_las_files():
    root = tk.Tk()
    root.withdraw()
    file_paths = filedialog.askopenfilenames(title="Select LAS files", filetypes=[("LAS files", "*.las")])

    las_files = []
    print("\n=== DEBUG: LAS FILE LOADING ===")
    print(f"Number of files selected: {len(file_paths)}")

    if not file_paths:
        print("No files selected by user.")
        return None

    for file_path in file_paths:
        try:
            las = lasio.read(file_path)
            # Debug information about the loaded file
            print(f"Loaded file: {file_path}")
            print(f"  Well name: {las.well.WELL.value}")
            print(f"  Curves available: {len(las.curves)}")
            print(f"  Depth range: {min(las['DEPTH'].data)} to {max(las['DEPTH'].data)}")
            print(f"  Sample curves with non-NaN values:")

            # Check a few curves for NaN values
            for curve_name in list(las.curves.keys())[:5]:  # First 5 curves
                data = las[curve_name].data
                non_nan_count = np.sum(~np.isnan(data))
                total_count = len(data)
                print(f"    {curve_name}: {non_nan_count}/{total_count} non-NaN values ({non_nan_count/total_count*100:.1f}%)")

            las_files.append(las)
        except Exception as e:
            print(f"ERROR loading file {file_path}: {str(e)}")

    if not las_files:
        print("No LAS files were successfully loaded.")
        return None

    print(f"Successfully loaded {len(las_files)} LAS files")
    print("================================\n")
    return las_files

def load_boundaries_from_excel(title="Select Excel file with boundary information"):
    """
    Load boundary information from an Excel file.

    Args:
        title: The title to display in the file dialog

    Returns:
        DataFrame containing well names, surface names, and measured depths
        or None if no file was selected or an error occurred.
    """
    root = tk.Tk()
    root.withdraw()

    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")]
    )

    if not file_path:
        print("No Excel file selected.")
        return None

    try:
        # Load the Excel file
        df = pd.read_excel(file_path)

        # Check if the required columns exist
        required_columns = ['Well', 'Surface', 'MD']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            messagebox.showerror(
                "Missing Columns",
                f"The Excel file is missing the following required columns: {', '.join(missing_columns)}.\n"
                f"Please ensure the file contains columns named: {', '.join(required_columns)}"
            )
            return None

        # Basic validation
        if df.empty:
            messagebox.showerror("Empty File", "The Excel file contains no data.")
            return None

        print(f"Successfully loaded boundary data from {file_path}")
        print(f"Found {len(df)} boundary entries for {df['Well'].nunique()} wells")
        return df

    except Exception as e:
        messagebox.showerror("Error Loading File", f"An error occurred while loading the Excel file:\n{str(e)}")
        print(f"Error loading Excel file: {str(e)}")
        return None
def filter_excel_data_for_las_wells(df, las_files):
    """
    Filter Excel data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        df: DataFrame containing well names, surface names, and measured depths
        las_files: List of LAS file objects

    Returns:
        Filtered DataFrame containing only boundaries for wells in the LAS files
    """
    if df is None:
        return None

    # Get the list of well names from the LAS files
    las_well_names = [las.well.WELL.value for las in las_files]

    # Filter the DataFrame to only include rows where the Well column value is in las_well_names
    filtered_df = df[df['Well'].isin(las_well_names)]

    # Check if we have any matching wells
    if filtered_df.empty:
        print("Warning: No matching wells found in Excel file. The Excel file contains wells:",
              ", ".join(df['Well'].unique()))
        print("But the loaded LAS files contain wells:", ", ".join(las_well_names))
        return None

    # Log the filtering results
    original_well_count = df['Well'].nunique()
    filtered_well_count = filtered_df['Well'].nunique()
    print(f"Filtered Excel data from {original_well_count} wells to {filtered_well_count} wells that match loaded LAS files")
    print(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")

    return filtered_df
def load_excel_depth_ranges(las_files):
    """
    Prompt the user to load an Excel file with depth ranges at the beginning of the program.
    Filter the data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        las_files: List of LAS file objects

    Returns:
        DataFrame containing well names, surface names, and measured depths (filtered for LAS wells)
        or None if no file was selected, the user canceled, or an error occurred.
    """
    # Ask user if they want to load an Excel file with depth ranges
    root = tk.Tk()
    root.withdraw()

    load_excel = messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\n"
        "The file should have columns named 'Well', 'Surface', and 'MD'."
    )

    if not load_excel:
        print("User chose not to load Excel file with depth ranges at this time.")
        return None

    # Load the Excel file
    df = load_boundaries_from_excel("Select Excel file with depth ranges")

    # Filter the data to only include boundaries for wells in the LAS files
    return filter_excel_data_for_las_wells(df, las_files)
def select_boundaries_for_all_wells(df, las_well_names):
    """
    Create a dialog to select top and bottom boundaries for all wells at once.

    Args:
        df: DataFrame containing boundary data
        las_well_names: List of well names from LAS files

    Returns:
        Dictionary mapping well names to (top_depth, bottom_depth) tuples,
        or None if cancelled
    """
    # Filter data for wells that exist in both the Excel file and LAS files
    available_wells = [well for well in las_well_names if well in df['Well'].unique()]

    if not available_wells:
        messagebox.showerror(
            "No Matching Wells",
            "No wells in the Excel file match the loaded LAS files."
        )
        return None

    # Create a dictionary to store well data
    well_data_dict = {}
    for well in available_wells:
        # Filter and sort data for this well
        well_data = df[df['Well'] == well].sort_values('MD')
        if not well_data.empty:
            well_data_dict[well] = well_data

    # Create dialog
    dialog = tk.Toplevel()
    dialog.title("Select Boundaries for All Wells")
    dialog.geometry("800x600")  # Larger dialog for the table

    # Create main frame
    main_frame = ttk.Frame(dialog, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # Add instructions
    instructions = ttk.Label(
        main_frame,
        text="Select top and bottom boundaries for all wells. The boundaries will be used to define the depth range for analysis.",
        wraplength=780
    )
    instructions.pack(pady=(0, 10))

    # Create a frame for the table
    table_frame = ttk.Frame(main_frame)
    table_frame.pack(fill=tk.BOTH, expand=True, pady=10)

    # Create a canvas with scrollbar for the table
    canvas = tk.Canvas(table_frame)
    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda _e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Create table headers
    ttk.Label(scrollable_frame, text="Well Name", font=("", 10, "bold")).grid(row=0, column=0, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Top Boundary", font=("", 10, "bold")).grid(row=0, column=1, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Top Depth", font=("", 10, "bold")).grid(row=0, column=2, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Bottom Boundary", font=("", 10, "bold")).grid(row=0, column=3, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Bottom Depth", font=("", 10, "bold")).grid(row=0, column=4, padx=10, pady=5, sticky=tk.W)

    # Dictionary to store selections for each well
    selections = {}

    # Function to update depth labels when surface selection changes
    def update_depth_label(well_name, surface_type):
        surface = selections[well_name][f"{surface_type}_surface"].get()
        well_df = well_data_dict[well_name]

        md_values = well_df[well_df['Surface'] == surface]['MD'].values
        if len(md_values) > 0:
            depth = float(md_values[0])
            selections[well_name][f"{surface_type}_depth_var"].set(f"{depth:.2f}")
            selections[well_name][f"{surface_type}_depth"] = depth
        else:
            selections[well_name][f"{surface_type}_depth_var"].set("N/A")
            selections[well_name][f"{surface_type}_depth"] = None

    # Create rows for each well
    row = 1
    for well_name in available_wells:
        if well_name in well_data_dict:
            well_df = well_data_dict[well_name]
            surfaces = well_df['Surface'].unique().tolist()

            if len(surfaces) < 2:
                # Skip wells with fewer than 2 surfaces (need at least top and bottom)
                continue

            # Create a dictionary to store selections for this well
            selections[well_name] = {
                "top_surface": tk.StringVar(),
                "bottom_surface": tk.StringVar(),
                "top_depth_var": tk.StringVar(),
                "bottom_depth_var": tk.StringVar(),
                "top_depth": None,
                "bottom_depth": None
            }

            # Set default values
            selections[well_name]["top_surface"].set(surfaces[0])
            selections[well_name]["bottom_surface"].set(surfaces[-1])

            # Create widgets for this well
            ttk.Label(scrollable_frame, text=well_name).grid(row=row, column=0, padx=10, pady=5, sticky=tk.W)

            # Top boundary combobox
            top_combo = ttk.Combobox(
                scrollable_frame,
                textvariable=selections[well_name]["top_surface"],
                values=surfaces,
                state="readonly",
                width=20
            )
            top_combo.grid(row=row, column=1, padx=10, pady=5, sticky=tk.W)

            # Top depth label
            top_depth_label = ttk.Label(
                scrollable_frame,
                textvariable=selections[well_name]["top_depth_var"]
            )
            top_depth_label.grid(row=row, column=2, padx=10, pady=5, sticky=tk.W)

            # Bottom boundary combobox
            bottom_combo = ttk.Combobox(
                scrollable_frame,
                textvariable=selections[well_name]["bottom_surface"],
                values=surfaces,
                state="readonly",
                width=20
            )
            bottom_combo.grid(row=row, column=3, padx=10, pady=5, sticky=tk.W)

            # Bottom depth label
            bottom_depth_label = ttk.Label(
                scrollable_frame,
                textvariable=selections[well_name]["bottom_depth_var"]
            )
            bottom_depth_label.grid(row=row, column=4, padx=10, pady=5, sticky=tk.W)

            # Set up callbacks for comboboxes
            selections[well_name]["top_surface"].trace_add(
                "write",
                lambda *_args, w=well_name: update_depth_label(w, "top")
            )
            selections[well_name]["bottom_surface"].trace_add(
                "write",
                lambda *_args, w=well_name: update_depth_label(w, "bottom")
            )

            # Initialize depth labels
            update_depth_label(well_name, "top")
            update_depth_label(well_name, "bottom")

            row += 1

    # Create buttons frame
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=10)

    # Result variable
    result = {"boundaries": {}, "cancelled": False}

    def on_ok():
        # Show a progress indicator
        status_label = ttk.Label(main_frame, text="Processing selections...", font=("", 10, "italic"))
        status_label.pack(before=button_frame, pady=5)
        dialog.update_idletasks()  # Force UI update to show the status label

        # Validate selections
        invalid_wells = []
        for well_name, selection in selections.items():
            top_depth = selection["top_depth"]
            bottom_depth = selection["bottom_depth"]

            if top_depth is None or bottom_depth is None:
                invalid_wells.append(well_name)
            else:
                # Store the boundaries
                result["boundaries"][well_name] = (top_depth, bottom_depth)

        if invalid_wells:
            status_label.destroy()  # Remove the status label
            messagebox.showerror(
                "Invalid Selections",
                f"Could not retrieve valid depth values for the following wells: {', '.join(invalid_wells)}"
            )
            return

        # Update status to indicate completion
        status_label.config(text="Selections complete! Closing dialog...")
        dialog.update_idletasks()  # Force UI update

        # Use after() to ensure the UI updates before destroying the dialog
        dialog.after(500, lambda: dialog.destroy())
        result["cancelled"] = False

    def on_cancel():
        result["cancelled"] = True
        dialog.destroy()

    # Create buttons
    ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)

    # Run dialog
    dialog.protocol("WM_DELETE_WINDOW", on_cancel)
    dialog.transient()
    dialog.grab_set()

    # Make the dialog modal and wait for it to be destroyed
    dialog.wait_visibility()
    dialog.focus_set()
    dialog.wait_window()

    if result["cancelled"]:
        print("Batch selection was cancelled by the user.")
        return None

    print(f"Batch selection completed successfully for {len(result['boundaries'])} wells.")
    return result["boundaries"]
def select_boundaries_from_excel(df, well_name):
    """
    Create a dialog to select top and bottom boundaries from Excel data for a specific well.
    The dialog will only show boundaries for the specified well.

    Args:
        df: DataFrame containing boundary data
        well_name: Name of the well to filter data for

    Returns:
        Tuple of (top_depth, bottom_depth) or None if cancelled
    """
    # Filter data for the current well
    well_data = df[df['Well'] == well_name]

    if well_data.empty:
        messagebox.showerror(
            "Missing Well Data",
            f"No boundary data found for well '{well_name}' in the Excel file."
        )
        return None

    # Sort the data by depth to make selection more intuitive
    well_data = well_data.sort_values('MD')

    # Create dialog
    dialog = tk.Toplevel()
    dialog.title(f"Select Boundaries for {well_name}")
    dialog.geometry("400x300")

    # Create frame
    frame = ttk.Frame(dialog, padding="10")
    frame.pack(fill=tk.BOTH, expand=True)

    # Get unique surface names for this well
    surfaces = well_data['Surface'].unique().tolist()

    # Create variables to store selections
    top_surface_var = tk.StringVar()
    bottom_surface_var = tk.StringVar()

    # Set default values if available
    if len(surfaces) > 0:
        top_surface_var.set(surfaces[0])
    if len(surfaces) > 1:
        bottom_surface_var.set(surfaces[-1])

    # Function to update depth labels when surface selection changes
    def update_depth_labels(*_args):
        top_surface = top_surface_var.get()
        bottom_surface = bottom_surface_var.get()

        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

        if len(top_md) > 0:
            top_depth_label.config(text=f"Depth: {top_md[0]:.2f}")
        else:
            top_depth_label.config(text="Depth: N/A")

        if len(bottom_md) > 0:
            bottom_depth_label.config(text=f"Depth: {bottom_md[0]:.2f}")
        else:
            bottom_depth_label.config(text="Depth: N/A")

    # Create widgets
    ttk.Label(frame, text="Select Top Boundary:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
    top_combo = ttk.Combobox(frame, textvariable=top_surface_var, values=surfaces, state="readonly")
    top_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
    top_depth_label = ttk.Label(frame, text="Depth: ")
    top_depth_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

    ttk.Label(frame, text="Select Bottom Boundary:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
    bottom_combo = ttk.Combobox(frame, textvariable=bottom_surface_var, values=surfaces, state="readonly")
    bottom_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
    bottom_depth_label = ttk.Label(frame, text="Depth: ")
    bottom_depth_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

    # Register callbacks
    top_surface_var.trace_add("write", update_depth_labels)
    bottom_surface_var.trace_add("write", update_depth_labels)

    # Initialize depth labels
    update_depth_labels()

    # Result variable
    result = {"top_depth": None, "bottom_depth": None, "cancelled": False}

    def on_ok():
        # Show processing indicator
        status_label = ttk.Label(frame, text="Processing...", font=("", 9, "italic"))
        status_label.grid(row=5, column=0, columnspan=2, pady=(5, 0))
        dialog.update_idletasks()  # Force UI update

        top_surface = top_surface_var.get()
        bottom_surface = bottom_surface_var.get()

        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

        if len(top_md) == 0 or len(bottom_md) == 0:
            status_label.destroy()
            messagebox.showerror("Missing Data", "Could not retrieve depth values for selected surfaces.")
            return

        result["top_depth"] = float(top_md[0])
        result["bottom_depth"] = float(bottom_md[0])

        # Update status and close dialog
        status_label.config(text="Selection complete! Closing...")
        dialog.update_idletasks()  # Force UI update

        # Use after() to ensure the UI updates before destroying the dialog
        dialog.after(300, lambda: dialog.destroy())

    def on_cancel():
        result["cancelled"] = True
        dialog.destroy()

def find_default_columns(las, keywords):
    """
    Find the default columns for specific logs based on the provided keywords.
    This version includes detailed logging for troubleshooting.
    """
    default_columns = {}
    for keyword, aliases in keywords.items():
        found = False
        for alias in aliases:
            for curve in las.curves:
                # Case-insensitive comparison
                if alias.upper() == curve.mnemonic.upper():
                    default_columns[keyword] = curve.mnemonic
                    print(f"Found curve {curve.mnemonic} for alias {alias}")
                    found = True
                    break
            if found:
                break
        if not found:
            default_columns[keyword] = None
            print(f"Curve for {keyword} not found among aliases {aliases}")
    return default_columns
def get_depth_ranges(las_files, _log_keywords_for_finding_cols, preloaded_excel_df=None):
    """
    Create a GUI to display well names and allow users to input top and bottom depths for analysis.
    Provides two methods:
    1. Manual input: Directly enter depth values
    2. Excel file import: Select depths from geological markers in an Excel file

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        preloaded_excel_df: Optional pre-loaded Excel DataFrame with depth ranges

    If no input is provided, use the min and max non-NaN values from the DT log, if available.
    """
    # Calculate default depth values for each well
    default_depth_values_for_fallback = {}

    for las in las_files:
        well_name = las.well.WELL.value

        current_well_cols_for_depth = find_default_columns(las, log_keywords)
        depth_mnemonic = current_well_cols_for_depth.get('DEPTH')
        dt_mnemonic = current_well_cols_for_depth.get('DT')

        default_top_depth_val = 0.0  # Fallback default
        default_bottom_depth_val = 0.0 # Fallback default

        if depth_mnemonic and depth_mnemonic in las.curves:
            depth_data_for_range = np.array(las[depth_mnemonic].data)

            if dt_mnemonic and dt_mnemonic in las.curves:
                dt_data_for_range = np.array(las[dt_mnemonic].data)
                # Consider only depths where DT is valid
                valid_dt_mask = np.isfinite(dt_data_for_range) & np.isfinite(depth_data_for_range)
                valid_depths_for_dt_range = depth_data_for_range[valid_dt_mask]

                if valid_depths_for_dt_range.size > 0:
                    default_top_depth_val = np.min(valid_depths_for_dt_range)
                    default_bottom_depth_val = np.max(valid_depths_for_dt_range)
                elif np.any(np.isfinite(depth_data_for_range)): # Fallback to full depth range if DT is all NaN
                    default_top_depth_val = np.nanmin(depth_data_for_range)
                    default_bottom_depth_val = np.nanmax(depth_data_for_range)
                    print(f"Info: DT log '{dt_mnemonic}' for well {well_name} has no valid data points within depth range. Using full depth range for defaults.")
                else: # Depth log itself is all NaN or empty
                    print(f"Warning: Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN. Using 0,0 for default depth range.")
            elif np.any(np.isfinite(depth_data_for_range)): # DT log not found, use full depth range
                default_top_depth_val = np.nanmin(depth_data_for_range)
                default_bottom_depth_val = np.nanmax(depth_data_for_range)
                print(f"Info: DT log not found for well {well_name} (searched for '{dt_mnemonic}'). Using full depth range for defaults.")
            else: # Depth log is all NaN or empty, and DT log not found
                 print(f"Warning: Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN, and DT log not found. Using 0,0 for default depth range.")
        else:
            print(f"Error: DEPTH log not found for well {well_name} (searched for '{depth_mnemonic}'). Cannot set default depth range. Using 0,0.")

        default_depth_values_for_fallback[well_name] = (default_top_depth_val, default_bottom_depth_val)

    # Create main dialog for method selection
    root = tk.Tk()
    root.title("Set Depth Ranges for Wells")
    root.geometry("600x500")

    # Create a frame
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # Method selection
    method_frame = ttk.LabelFrame(main_frame, text="Select Method for Defining Boundaries", padding="10")
    method_frame.pack(fill=tk.X, padx=5, pady=5)

    # Set default method - use "excel" if preloaded Excel data is available
    default_method = "excel" if preloaded_excel_df is not None else "manual"
    method_var = tk.StringVar(value=default_method)

    # Variable to store the submit button reference
    submit_btn_ref = {"btn": None}

    # Define a command to call when radio buttons are clicked
    def on_radio_click():
        method = method_var.get()
        if method == "manual":
            create_manual_ui()
            # Re-enable the Submit button for manual input
            if submit_btn_ref["btn"] is not None:
                submit_btn_ref["btn"].config(state=tk.NORMAL)
        else:  # excel
            create_excel_ui()
            # Disable the Submit button until a valid Excel file is loaded
            if submit_btn_ref["btn"] is not None:
                submit_btn_ref["btn"].config(state=tk.DISABLED)

    ttk.Radiobutton(
        method_frame,
        text="Manual Input",
        variable=method_var,
        value="manual",
        command=on_radio_click
    ).pack(anchor=tk.W, pady=2)

    ttk.Radiobutton(
        method_frame,
        text="Import from Excel File",
        variable=method_var,
        value="excel",
        command=on_radio_click
    ).pack(anchor=tk.W, pady=2)

    # Content frame for the selected method
    content_frame = ttk.Frame(main_frame, padding="10")
    content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Variables to store entries for manual method
    manual_entries = []

    # Variable to store Excel data
    excel_data = {"df": preloaded_excel_df}

    # Variable to store status update function
    status_update = {"func": None}

    # Function to create manual input UI
    def create_manual_ui():
        # Clear the content frame
        for widget in content_frame.winfo_children():
            widget.destroy()

        # Create scrollable frame for many wells
        canvas = tk.Canvas(content_frame)
        scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda _e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Create and set column headers
        ttk.Label(scrollable_frame, text="Well Name").grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(scrollable_frame, text="Top Depth").grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(scrollable_frame, text="Bottom Depth").grid(row=0, column=2, padx=5, pady=5)

        # Clear previous entries
        manual_entries.clear()

        # Create entries for each well
        for i, las in enumerate(las_files, start=1):
            well_name = las.well.WELL.value
            default_top, default_bottom = default_depth_values_for_fallback[well_name]

            ttk.Label(scrollable_frame, text=well_name).grid(row=i, column=0, padx=5, pady=2)
            top_entry = ttk.Entry(scrollable_frame)
            top_entry.insert(0, str(default_top))
            top_entry.grid(row=i, column=1, padx=5, pady=2)
            bottom_entry = ttk.Entry(scrollable_frame)
            bottom_entry.insert(0, str(default_bottom))
            bottom_entry.grid(row=i, column=2, padx=5, pady=2)

            manual_entries.append((well_name, top_entry, bottom_entry))

    # Function to create Excel import UI
    def create_excel_ui():
        # Clear the content frame
        for widget in content_frame.winfo_children():
            widget.destroy()

        # Declare submit_btn as global so we can access it
        # global submit_btn # This global might not be necessary depending on how submit_btn_ref is used

        # Create UI for Excel import
        excel_frame = ttk.Frame(content_frame)
        excel_frame.pack(fill=tk.BOTH, expand=True)

        # Status label - show different message if Excel data is already loaded
        initial_status = "No Excel file loaded. Click 'Load Excel File' to import boundary data."
        load_button_text = "Load Excel File"

        if excel_data["df"] is not None:
            df = excel_data["df"]
            initial_status = f"Excel file already loaded and ready to use.\nFound {len(df)} boundary entries for {df['Well'].nunique()} wells.\nClick 'Submit' to proceed with selecting boundaries for each well."
            # Enable the Submit button since we already have Excel data
            if submit_btn_ref["btn"] is not None:
                submit_btn_ref["btn"].config(state=tk.NORMAL)
            # Change the button text to indicate loading a different file
            load_button_text = "Load Different Excel File"

        status_var = tk.StringVar(value=initial_status)
        status_label = ttk.Label(excel_frame, textvariable=status_var, wraplength=500)
        status_label.pack(pady=10)

        # Set up status update function for use in on_submit
        status_update["func"] = lambda text: status_var.set(text)

        # Function to load Excel file
        def on_load_excel():
            # Update the button text to indicate loading is in progress
            load_button.config(text="Loading...", state=tk.DISABLED)

            # Update the UI to show loading is in progress
            root.update_idletasks()

            df = load_boundaries_from_excel()
            if df is not None:
                # Filter the Excel data to only include wells that match the loaded LAS files
                filtered_df = filter_excel_data_for_las_wells(df, las_files)

                if filtered_df is not None:
                    # Check if this is a different file than what was already loaded
                    is_different = True
                    if excel_data["df"] is not None:
                        # Simple check - compare number of rows and wells
                        old_rows = len(excel_data["df"])
                        old_wells = excel_data["df"]['Well'].nunique()
                        new_rows = len(filtered_df)
                        new_wells = filtered_df['Well'].nunique()

                        if old_rows == new_rows and old_wells == new_wells:
                            is_different = False

                    excel_data["df"] = filtered_df

                    if is_different:
                        status_var.set(f"New Excel file loaded and filtered successfully.\n"
                                      f"Found {len(filtered_df)} boundary entries for {filtered_df['Well'].nunique()} wells that match loaded LAS files.\n"
                                      f"Click 'Submit' to proceed with selecting boundaries for each well.")
                    else:
                        status_var.set(f"Excel file loaded (appears to be the same or similar to previous file).\n"
                                      f"Found {len(filtered_df)} boundary entries for {filtered_df['Well'].nunique()} wells that match loaded LAS files.\n"
                                      f"Click 'Submit' to proceed with selecting boundaries for each well.")

                    # Preview data
                    preview_text.delete(1.0, tk.END)
                    preview_text.insert(tk.END, filtered_df.head(10).to_string())
                    # Add a note about the data being filtered for LAS wells
                    preview_text.insert(tk.END, "\n\nNote: This data is filtered to show only wells that match your loaded LAS files.")

                    # Enable the Submit button now that a valid Excel file is loaded
                    if submit_btn_ref["btn"] is not None:
                        submit_btn_ref["btn"].config(state=tk.NORMAL)

                    # Update the button text to indicate loading a different file
                    load_button.config(text="Load Different Excel File", state=tk.NORMAL)
                else:
                    status_var.set("No matching wells found in Excel file. Please load a file with wells that match the loaded LAS files.")
                    # Keep the Submit button disabled since no matching wells were found
                    if submit_btn_ref["btn"] is not None:
                        submit_btn_ref["btn"].config(state=tk.DISABLED)
                    # Reset the button text
                    load_button.config(text="Load Excel File", state=tk.NORMAL)
            else:
                status_var.set("Failed to load Excel file. Please try again.")
                # Keep the Submit button disabled on failure
                if submit_btn_ref["btn"] is not None:
                    submit_btn_ref["btn"].config(state=tk.DISABLED)
                # Reset the button text
                load_button.config(text="Load Excel File", state=tk.NORMAL)

        # Create a frame for the buttons
        buttons_frame = ttk.Frame(excel_frame)
        buttons_frame.pack(pady=5)

        # Load button - use the text based on whether Excel data is already loaded
        load_button = ttk.Button(buttons_frame, text=load_button_text, command=on_load_excel)
        load_button.pack(side=tk.LEFT, padx=5)

        # Add a button to preview boundaries for all wells
        def on_preview_boundaries():
            if excel_data["df"] is None:
                messagebox.showerror("No Data", "Please load an Excel file first.")
                return

            # Get the list of well names from the LAS files
            las_well_names = [las.well.WELL.value for las in las_files]

            # Show a preview of the boundaries that would be selected
            preview_text.delete(1.0, tk.END)
            preview_text.insert(tk.END, "Preview of available boundaries for each well:\n\n")

            # Get unique wells in Excel that also exist in LAS files
            df_excel = excel_data["df"]
            matching_wells = [well for well in las_well_names if well in df_excel['Well'].unique()]

            if not matching_wells:
                preview_text.insert(tk.END, "No matching wells found in Excel file.")
                return

            # Show a preview for each well
            for well_name in matching_wells:
                well_data = df_excel[df_excel['Well'] == well_name].sort_values('MD')
                if not well_data.empty:
                    surfaces = well_data['Surface'].unique().tolist()
                    if len(surfaces) >= 2:
                        top_surface = surfaces[0]
                        bottom_surface = surfaces[-1]
                        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values[0]
                        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values[0]
                        preview_text.insert(tk.END, f"{well_name}:\n")
                        preview_text.insert(tk.END, f"  Top: {top_surface} ({top_md:.2f})\n")
                        preview_text.insert(tk.END, f"  Bottom: {bottom_surface} ({bottom_md:.2f})\n\n")
                    else:
                        preview_text.insert(tk.END, f"{well_name}: Insufficient boundaries (need at least 2)\n\n")
                else:
                    preview_text.insert(tk.END, f"{well_name}: No data found\n\n")

            preview_text.insert(tk.END, "\nNote: These are the default boundaries that will be used. You can change them in the batch selection dialog.")

        preview_button = ttk.Button(buttons_frame, text="Preview Boundaries", command=on_preview_boundaries)
        preview_button.pack(side=tk.LEFT, padx=5)

        # Preview frame
        preview_frame = ttk.LabelFrame(excel_frame, text="Data Preview (first 10 rows)")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        preview_text = tk.Text(preview_frame, height=10, wrap=tk.NONE)
        preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        preview_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=preview_text.yview)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        preview_text.configure(yscrollcommand=preview_scrollbar.set)

        # Horizontal scrollbar for preview
        h_scrollbar = ttk.Scrollbar(preview_frame, orient="horizontal", command=preview_text.xview)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        preview_text.configure(xscrollcommand=h_scrollbar.set)

        # Show preview of preloaded Excel data if available
        if excel_data["df"] is not None:
            preview_text.delete(1.0, tk.END)
            preview_text.insert(tk.END, excel_data["df"].head(10).to_string())
            # Add a note about the data being already filtered for LAS wells
            preview_text.insert(tk.END, "\n\nNote: This data is already filtered to show only wells that match your loaded LAS files.")

    # Note: Method switching is now handled directly by the radio button command (on_radio_click)

    # Initialize UI based on the selected method
    on_radio_click()

    # Result variable
    depth_ranges_output = {}

    def on_submit():
        method = method_var.get()
        processed_excel_wells = set() # Keep track of wells processed from Excel

        if method == "manual":
            # Process manual entries
            for well_name, top_entry, bottom_entry in manual_entries:
                try:
                    top_depth_val = float(top_entry.get())
                    bottom_depth_val = float(bottom_entry.get())
                    depth_ranges_output[well_name] = (top_depth_val, bottom_depth_val)
                except ValueError:
                    # If the user input is invalid, fallback to determined default non-NaN DT range
                    print(f"Invalid depth input for {well_name}, using determined default range: {default_depth_values_for_fallback[well_name]}.")
                    depth_ranges_output[well_name] = default_depth_values_for_fallback[well_name]
            root.quit()

        else:  # excel
            if excel_data["df"] is None:
                messagebox.showerror("No Data", "Please load an Excel file first.")
                return

            df_excel = excel_data["df"]

            # Get the list of well names from the LAS files
            las_well_names = [las.well.WELL.value for las in las_files]

            # Update status to indicate batch selection is starting
            if status_update["func"] is not None:
                status_update["func"]("Starting batch selection dialog...")
            else:
                # Fallback if status update function is not available
                temp_status_label = ttk.Label(main_frame, text="Starting batch selection dialog...", font=("", 10, "italic"))
                temp_status_label.pack(before=submit_btn_ref["btn"], pady=5)


            root.update_idletasks()  # Force UI update

            # Show a dialog to select boundaries for all wells at once
            print("Opening batch selection dialog for all wells...")
            all_boundaries = select_boundaries_for_all_wells(df_excel, las_well_names)

            # Update status based on the result
            if status_update["func"] is not None:
                if all_boundaries:
                    status_update["func"]("Processing selected boundaries...")
                else:
                    status_update["func"]("Batch selection cancelled. Applying default depth ranges...")
            else:
                # Fallback if status update function is not available
                if 'temp_status_label' in locals():
                    if all_boundaries:
                        temp_status_label.config(text="Processing selected boundaries...")
                    else:
                        temp_status_label.config(text="Batch selection cancelled. Applying default depth ranges...")


            root.update_idletasks()  # Force UI update

            if all_boundaries:
                # Apply the selected boundaries
                for well_name, boundaries in all_boundaries.items():
                    depth_ranges_output[well_name] = boundaries
                    processed_excel_wells.add(well_name)
                    print(f"Boundaries set for {well_name} from Excel batch selection: {boundaries}")

                # Apply default values for any LAS wells that weren't in the Excel file or weren't selected
                for las_well_name in las_well_names:
                    if las_well_name not in depth_ranges_output:
                        depth_ranges_output[las_well_name] = default_depth_values_for_fallback[las_well_name]
                        print(f"No boundaries selected for {las_well_name}. Applied LAS-derived default range: {default_depth_values_for_fallback[las_well_name]}")
                # Update status for successful completion
                if status_update["func"] is not None:
                    status_update["func"]("Boundary selection completed successfully!")
                elif 'temp_status_label' in locals():
                    temp_status_label.config(text="Boundary selection completed successfully!")


            else:
                # User cancelled the batch selection dialog
                print("Batch selection cancelled. Applying default depth ranges for all wells.")
                for las_well_name in las_well_names:
                    depth_ranges_output[las_well_name] = default_depth_values_for_fallback[las_well_name]
                    print(f"Applied default depth range for {las_well_name}: {default_depth_values_for_fallback[las_well_name]}")
                # Update status for default ranges applied
                if status_update["func"] is not None:
                    status_update["func"]("Default depth ranges applied to all wells.")
                elif 'temp_status_label' in locals():
                    temp_status_label.config(text="Default depth ranges applied to all wells.")


            # Final update before closing
            root.update_idletasks()  # Force UI update

            # Use after() to ensure the UI updates before closing
            root.after(1000, root.quit)

    # Submit button - instantiated as a variable so it can be enabled/disabled dynamically
    submit_btn = ttk.Button(main_frame, text="Submit", command=on_submit)
    submit_btn.pack(pady=10)
    # Store the reference to the submit button
    submit_btn_ref["btn"] = submit_btn

    # Set initial state based on the selected method
    if method_var.get() == "manual":
        submit_btn.config(state=tk.NORMAL)
    else:  # excel
        # For Excel method, enable only if we have preloaded data
        if excel_data["df"] is not None:
            submit_btn.config(state=tk.NORMAL)
        else:
            submit_btn.config(state=tk.DISABLED)


    # Run the dialog
    root.mainloop()
    root.destroy()

    return depth_ranges_output

def analyze_log_availability(las_files):
    """
    Analyze which logs are available across all wells.

    Args:
        las_files: List of lasio.LASFile objects

    Returns:
        dict: {
            'common_logs': list,      # Available in all wells
            'partial_logs': dict,     # Available in some wells
            'total_wells': int        # Total number of wells
        }
    """
    all_logs = {}
    well_count = len(las_files)

    # Count log occurrences across wells
    for las in las_files:
        well_name = las.well.WELL.value
        for curve_name in las.curves.keys():
            if curve_name not in all_logs:
                all_logs[curve_name] = {'wells': [], 'count': 0}
            all_logs[curve_name]['wells'].append(well_name)
            all_logs[curve_name]['count'] += 1

    # Separate common logs from partial logs
    common_logs = []
    partial_logs = {}

    for log_name, log_info in all_logs.items():
        if log_info['count'] == well_count:
            common_logs.append(log_name)
        else:
            partial_logs[log_name] = log_info

    return {
        'common_logs': sorted(common_logs),
        'partial_logs': partial_logs,
        'total_wells': well_count
    }

def validate_calculation_inputs(las_files, calculation_text):
    """
    Validate that all logs referenced in calculations exist in all wells.
    Distinguishes between input variables (must exist) and output variables (being created).

    Args:
        las_files: List of lasio.LASFile objects
        calculation_text: String containing the calculation code

    Returns:
        dict: {
            'valid': bool,
            'missing_logs': dict,  # {well_name: [missing_log_names]}
            'available_logs': list,
            'error_details': str
        }
    """
    print("🔄 Validation: Starting validate_calculation_inputs()...")
    print(f"🔄 Validation: Calculation text length: {len(calculation_text)}")
    print(f"🔄 Validation: Number of LAS files: {len(las_files)}")

    # Parse calculation text to separate output variables from input variables
    print("🔄 Validation: Parsing calculation text to identify input vs output variables...")

    # Split into lines and analyze each assignment
    lines = [line.strip() for line in calculation_text.split('\n') if line.strip()]
    output_variables = set()
    input_variables = set()

    for line in lines:
        print(f"🔄 Validation: Analyzing line: '{line}'")

        # Skip comments
        if line.startswith('#'):
            continue

        # Look for assignment operations (=, +=, -=, *=, /=)
        if '=' in line and not any(op in line for op in ['==', '!=', '<=', '>=']):
            # Split on assignment operator
            if '+=' in line or '-=' in line or '*=' in line or '/=' in line:
                # Compound assignment - left side is both input and output
                left_side = line.split('=')[0].strip()
                right_side = '='.join(line.split('=')[1:]).strip()
            else:
                # Regular assignment
                parts = line.split('=', 1)
                if len(parts) == 2:
                    left_side = parts[0].strip()
                    right_side = parts[1].strip()
                else:
                    continue

            # Extract output variable name (left side of assignment)
            # Handle array indexing like VAR[0] = ...
            output_var = re.match(r'^([A-Z_][A-Z0-9_]*)', left_side.upper())
            if output_var:
                output_variables.add(output_var.group(1))
                print(f"🔄 Validation: Found output variable: {output_var.group(1)}")

            # Extract input variables from right side
            right_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', right_side.upper()))
            input_variables.update(right_vars)
            print(f"🔄 Validation: Found input variables in right side: {right_vars}")
        else:
            # No assignment, treat all variables as input
            line_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', line.upper()))
            input_variables.update(line_vars)
            print(f"🔄 Validation: Found variables in non-assignment line: {line_vars}")

    # Remove numpy functions and common Python keywords from input variables
    numpy_functions = {
        'NP', 'NUMPY', 'LOG', 'SQRT', 'EXP', 'SIN', 'COS', 'TAN',
        'NANMIN', 'NANMAX', 'NANMEAN', 'NANSTD', 'WHERE', 'ISNAN', 'ISFINITE',
        # Mathematical functions
        'CLIP', 'ABS', 'ABSOLUTE', 'MIN', 'MAX', 'MEAN', 'STD', 'SUM', 'PROD',
        'ROUND', 'FLOOR', 'CEIL', 'POWER', 'POW', 'SIGN',
        # Array functions
        'ARRAY', 'ZEROS', 'ONES', 'CONCATENATE', 'STACK', 'RESHAPE', 'FLATTEN',
        # Trigonometric functions
        'ARCSIN', 'ARCCOS', 'ARCTAN', 'ARCTAN2', 'SINH', 'COSH', 'TANH',
        # Statistical functions
        'MEDIAN', 'PERCENTILE', 'QUANTILE', 'VAR', 'CORRCOEF', 'COV',
        # Logical functions
        'ALL', 'ANY', 'LOGICAL_AND', 'LOGICAL_OR', 'LOGICAL_NOT',
        # Comparison functions
        'GREATER', 'LESS', 'EQUAL', 'NOT_EQUAL', 'GREATER_EQUAL', 'LESS_EQUAL'
    }
    python_keywords = {'IF', 'ELSE', 'FOR', 'WHILE', 'DEF', 'CLASS', 'IMPORT', 'FROM', 'AS', 'RETURN', 'TRUE', 'FALSE', 'NONE'}

    # Remove output variables from input variables (can't be missing if we're creating them)
    input_variables = input_variables - output_variables - numpy_functions - python_keywords

    print(f"🔄 Validation: Final output variables: {output_variables}")
    print(f"🔄 Validation: Final input variables to check: {input_variables}")

    # Get common logs across all wells
    common_logs = set(las_files[0].curves.keys())
    for las in las_files[1:]:
        common_logs.intersection_update(las.curves.keys())

    # Check which INPUT variables are missing in each well
    missing_logs = {}
    all_missing = set()

    for las in las_files:
        well_name = las.well.WELL.value
        well_curves = set(las.curves.keys())
        missing_in_well = input_variables - well_curves
        if missing_in_well:
            missing_logs[well_name] = list(missing_in_well)
            all_missing.update(missing_in_well)

    is_valid = len(all_missing) == 0

    if not is_valid:
        error_details = "CALCULATION ERROR: Missing Input Logs\n\n"
        error_details += "⚠️ WARNING: Your calculations reference INPUT logs that are NOT available in all wells.\n"
        error_details += "This will cause calculation failures in wells where these logs are missing.\n\n"

        if output_variables:
            error_details += f"📊 OUTPUT variables being created: {', '.join(sorted(output_variables))}\n"
            error_details += "✅ These are fine - they will be created by your calculations.\n\n"

        error_details += "❌ MISSING INPUT LOGS:\n\n"

        for well_name, missing in missing_logs.items():
            available_in_well = input_variables.intersection(set(las_files[0].curves.keys()))
            error_details += f"Well: {well_name}\n"
            if missing:
                error_details += f"  ❌ Missing INPUT logs: {', '.join(missing)}\n"
            if available_in_well:
                error_details += f"  ✅ Available INPUT logs: {', '.join(available_in_well)}\n"
            error_details += "\n"

        # Enhanced summary with availability analysis
        error_details += "AVAILABILITY ANALYSIS:\n"
        for log in all_missing:
            wells_missing = [well for well, missing in missing_logs.items() if log in missing]
            wells_with_log = len(las_files) - len(wells_missing)
            error_details += f"- {log}: Available in {wells_with_log}/{len(las_files)} wells "
            error_details += f"(Missing from: {', '.join(wells_missing)})\n"

        error_details += "\n💡 SOLUTION OPTIONS:\n"
        error_details += "1. Remove references to ❌ missing INPUT logs from your calculations\n"
        error_details += "2. Use only ✅ logs that are available in all wells\n"
        error_details += "3. Check the calculator legend for safe log options\n"
        error_details += "4. Use 'Check Log Availability' button before submitting\n\n"

        error_details += "🔍 SAFE INPUT LOGS TO USE:\n"
        safe_logs = input_variables.intersection(common_logs)
        if safe_logs:
            error_details += f"✅ Available in all wells: {', '.join(sorted(safe_logs))}\n"
        else:
            error_details += "⚠️ None of your referenced INPUT logs are available in all wells!\n"

        # Show all available logs for reference
        error_details += f"\n📋 ALL AVAILABLE LOGS (in all wells): {', '.join(sorted(common_logs))}\n"

        error_details += "\n📋 NEXT STEPS:\n"
        error_details += "[Retry] - Modify calculations to use only ✅ logs\n"
        error_details += "[Skip] - Continue workflow without custom calculations\n"
        error_details += "[Cancel] - Return to main workflow"
    else:
        error_details = ""
        if output_variables:
            print(f"✅ Validation: Will create output variables: {sorted(output_variables)}")
        if input_variables:
            print(f"✅ Validation: All required input variables are available: {sorted(input_variables)}")
        else:
            print("✅ Validation: No input variables required (calculations use only constants/functions)")

    print("🔄 Validation: Preparing return result...")
    result = {
        'valid': is_valid,
        'missing_logs': missing_logs,
        'available_logs': list(common_logs),
        'error_details': error_details
    }
    print("🔄 Validation: validate_calculation_inputs() completed successfully")
    return result

def get_calculations(las_files):
    """
    Enhanced calculator interface showing log availability.

    Args:
        las_files: List of lasio.LASFile objects

    Returns:
        bool: True if calculations were successful, False if cancelled or failed
    """
    import tkinter as tk
    from tkinter import messagebox, ttk

    # Get columns that are present in all LAS files
    common_columns = set(las_files[0].curves.keys())
    for las in las_files[1:]:
        common_columns.intersection_update(las.curves.keys())
    columns = sorted(common_columns)

    if not columns:
        messagebox.showerror("Error", "No common columns found across all LAS files.")
        return False

    while True:
        root = tk.Tk()
        root.title("Custom Log Calculator - Enhanced with Availability Info")
        root.geometry("1000x800")

        # Create PanedWindow
        paned = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)

        # Left Frame: Available variables with availability info
        left_frame = ttk.Frame(paned, width=300)
        paned.add(left_frame, weight=0)

        # Right Frame: Instructions and Text widget
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=1)

        # Enhanced variable list with availability indicators
        variables_label = tk.Label(left_frame, text="Available Variables:", anchor='w', font=('Arial', 12, 'bold'))
        variables_label.pack(fill=tk.X, pady=(10, 5))

        # Add availability legend with enhanced clarity
        legend_frame = tk.Frame(left_frame)
        legend_frame.pack(fill=tk.X, pady=5)
        tk.Label(legend_frame, text="✅ Available in all wells (SAFE to use)",
                 fg='green', font=('Arial', 9, 'bold')).pack(anchor='w')
        tk.Label(legend_frame, text="⚠️ Available in some wells (WILL CAUSE ERRORS)",
                 fg='red', font=('Arial', 9, 'bold')).pack(anchor='w')

        # Add explanatory note
        note_frame = tk.Frame(left_frame)
        note_frame.pack(fill=tk.X, pady=(0, 5))
        note_text = "Note: Only use ✅ logs for calculations that must work across all wells.\nUsing ⚠️ logs will cause failures in wells where they're missing."
        tk.Label(note_frame, text=note_text, fg='darkblue', font=('Arial', 8),
                 justify=tk.LEFT, wraplength=280).pack(anchor='w')

        # Get log availability analysis
        log_analysis = analyze_log_availability(las_files)

        # Create listbox with scrollbar
        listbox_frame = tk.Frame(left_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        variables_listbox = tk.Listbox(listbox_frame, font=('Courier', 10))
        scrollbar_vars = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=variables_listbox.yview)
        variables_listbox.config(yscrollcommand=scrollbar_vars.set)

        # Populate listbox with availability indicators
        for idx, col in enumerate(columns, start=1):
            if col in log_analysis['common_logs']:
                display_text = f"✅ ({idx:2d}) {col}"
                variables_listbox.insert(tk.END, display_text)
            else:
                wells_with_log = log_analysis['partial_logs'].get(col, {}).get('wells', [])
                well_count = len(wells_with_log)
                total_wells = log_analysis['total_wells']
                display_text = f"⚠️ ({idx:2d}) {col} [{well_count}/{total_wells} wells]"
                variables_listbox.insert(tk.END, display_text)

        variables_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_vars.pack(side=tk.RIGHT, fill=tk.Y)

        # Enhanced instructions with practical examples and clear safety guidelines
        instructions = (
            "🧮 CUSTOM LOG CALCULATOR\n\n"
            "⚠️ IMPORTANT: Only use logs marked with ✅ (available in all wells)\n"
            "Using ⚠️ logs will cause calculation failures!\n\n"
            "📋 SAFE CALCULATION EXAMPLES:\n"
            "PHIE_HC = PHIE * (1 - SWE)  # Effective porosity with hydrocarbons\n"
            "VP_VS_RATIO = (304800/DT) / (304800/DTS)  # Vp/Vs ratio\n"
            "AI = RHOB * (304800/DT)  # Acoustic impedance\n"
            "POISSON = 0.5 * ((VP_VS_RATIO**2 - 2) / (VP_VS_RATIO**2 - 1))\n"
            "KdKs = KDRY / KSOLID  # Ratio calculation\n"
            "KdKs_CLIPPED = np.clip(KdKs, 0, None)  # Remove negative values\n"
            "NORMALIZED_GR = (GR - np.nanmin(GR)) / (np.nanmax(GR) - np.nanmin(GR))\n\n"
            "🔧 AVAILABLE FUNCTIONS:\n"
            "• Numpy: np.log(), np.sqrt(), np.exp(), np.sin(), np.cos(), np.clip(), etc.\n"
            "• Statistics: np.nanmin(), np.nanmax(), np.nanmean(), np.nanstd()\n"
            "• Math: Basic operators (+, -, *, /, **), parentheses for grouping\n\n"
            "💡 TIP: Use 'Check Log Availability' before submitting!"
        )

        instructions_label = tk.Label(right_frame, text=instructions, justify=tk.LEFT,
                                     font=('Arial', 10), fg='darkblue')
        instructions_label.pack(anchor='w', padx=10, pady=(10, 5))

        # Text widget for calculations with scrollbar
        text_frame = tk.Frame(right_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10)

        text = tk.Text(text_frame, width=80, height=20, font=('Courier', 11))
        scrollbar_text = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text.yview)
        text.config(yscrollcommand=scrollbar_text.set)

        text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_text.pack(side=tk.RIGHT, fill=tk.Y)

        # Enhanced button frame
        button_frame = tk.Frame(root)
        button_frame.pack(pady=15)

        result = {"calculations": None}

        def on_submit():
            result["calculations"] = text.get("1.0", tk.END).strip()
            root.quit()

        def on_cancel():
            result["calculations"] = None
            root.quit()

        def on_check_syntax():
            calc_text = text.get("1.0", tk.END).strip()
            if not calc_text:
                messagebox.showwarning("Syntax Check", "Please enter some calculations first.")
                return

            try:
                compile(calc_text, '<string>', 'exec')
                messagebox.showinfo("Syntax Check", "✅ No syntax errors found.")
            except SyntaxError as e:
                messagebox.showerror("Syntax Error", f"❌ Syntax error:\n{e}")

        def on_check_availability():
            calc_text = text.get("1.0", tk.END).strip()
            if not calc_text:
                messagebox.showwarning("Log Availability", "Please enter some calculations first.")
                return

            validation_result = validate_calculation_inputs(las_files, calc_text)

            if validation_result['valid']:
                messagebox.showinfo("Log Availability",
                                  "✅ All referenced logs are available in all wells.")
            else:
                messagebox.showwarning("Log Availability",
                                     validation_result['error_details'])

        # Enhanced buttons with better styling
        tk.Button(button_frame, text="Check Syntax",
                  command=on_check_syntax, bg='lightblue', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Check Log Availability",
                  command=on_check_availability, bg='lightyellow', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Submit Calculations",
                  command=on_submit, bg='lightgreen', width=18, height=2).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Cancel",
                  command=on_cancel, bg='lightcoral', width=18, height=2).pack(side=tk.LEFT, padx=5)

        root.mainloop()
        root.destroy()

        calculations = result["calculations"]

        if calculations is None:  # User cancelled
            return False

        # Check if user submitted empty calculations (no calculations to perform)
        if not calculations or not calculations.strip():
            print("ℹ️ Calculator: No calculations entered. Proceeding without custom calculations.")
            return True  # Successfully "completed" by doing nothing

        # Validate inputs before execution
        print("🔄 Calculator: Validating calculation inputs...")
        validation_result = validate_calculation_inputs(las_files, calculations)

        if not validation_result['valid']:
            print("🔄 Calculator: Validation failed, showing error dialog...")
            # Show detailed error information
            retry = messagebox.askretrycancel(
                "Log Availability Error",
                validation_result['error_details'] + "\n\nWould you like to:\n\n"
                "• Retry: Modify your calculations to use only ✅ logs\n"
                "• Cancel: Skip calculator and continue with existing logs"
            )
            if retry:
                print("🔄 Calculator: User chose retry, showing calculator again...")
                continue  # Go to the start of the while loop
            else:
                print("🔄 Calculator: User chose cancel, continuing workflow...")
                return True  # Skip calculator, continue workflow successfully

        # Execute calculations safely
        print("🔄 Calculator: Executing calculations...")
        error_occurred = False
        all_new_curves = set()  # Track all new curves created across wells
        well_curves_added = {}  # Track which curves were added to each well

        for las in las_files:
            well_name = las.well.WELL.value
            well_curves_added[well_name] = []

            # Prepare the execution environment
            local_ns = {}
            for curve in las.curves.keys():
                local_ns[curve] = np.array(las[curve].data)
            # Make numpy available
            local_ns['np'] = np

            try:
                # Execute the calculations
                exec(calculations, {}, local_ns)
            except Exception as e:
                # Enhanced error message with reference to legend
                error_message = f"CALCULATION EXECUTION ERROR\n\n"
                error_message += f"❌ Error in well: {well_name}\n"
                error_message += f"Error details: {str(e)}\n\n"
                error_message += "🔍 COMMON CAUSES:\n"
                error_message += "• Using logs that don't exist in this well\n"
                error_message += "• Referencing logs marked with ⚠️ in the calculator legend\n"
                error_message += "• Syntax errors in calculation expressions\n"
                error_message += "• Division by zero or invalid mathematical operations\n\n"

                error_message += "\n🔧 HOW TO FIX:\n"
                error_message += "1. Use only logs marked with ✅ (available in all wells)\n"
                error_message += "2. Check the calculator legend before using any logs\n"
                error_message += "3. Use 'Check Log Availability' button to validate before submitting\n"
                error_message += "4. Remove or replace references to ⚠️ logs\n\n"
                error_message += "💡 TIP: The calculator legend shows which logs are safe to use!"

                messagebox.showerror("Calculator Execution Error", error_message)
                error_occurred = True
                break  # Break out of the for loop

            # Add new variables as curves to the LAS file
            new_curves_added = []
            for var_name, data in local_ns.items():
                if var_name not in las.curves.keys() and var_name != 'np':
                    # Check if data is array-like and has the correct length
                    if isinstance(data, np.ndarray) and data.shape[0] == len(las['DEPTH'].data):
                        # Add the new curve
                        las.append_curve(var_name, data)
                        new_curves_added.append(var_name)
                        all_new_curves.add(var_name)
                        well_curves_added[well_name].append(var_name)
                        print(f"✅ Added calculated curve '{var_name}' to well {well_name}")
                    else:
                        # Skip variables that are scalars or arrays of incorrect length
                        print(f"⚠️ Skipped variable '{var_name}' in well {well_name}: incorrect shape or type")
                        continue

            if new_curves_added:
                print(f"📊 Well {well_name}: Added {len(new_curves_added)} new calculated curves: {new_curves_added}")
            else:
                print(f"ℹ️ Well {well_name}: No new curves were added from calculations")

            if error_occurred:
                break

        # Check for consistency across wells
        if not error_occurred and all_new_curves:
            print(f"\n🔍 Calculator: Checking curve consistency across {len(las_files)} wells...")
            print(f"📊 Total new curves created: {sorted(all_new_curves)}")

            # Check which curves are missing from which wells
            missing_curves_report = {}
            for curve_name in all_new_curves:
                wells_missing = []
                for las in las_files:
                    well_name = las.well.WELL.value
                    if curve_name not in well_curves_added[well_name]:
                        wells_missing.append(well_name)

                if wells_missing:
                    missing_curves_report[curve_name] = wells_missing
                    print(f"⚠️ Curve '{curve_name}' missing from wells: {wells_missing}")
                else:
                    print(f"✅ Curve '{curve_name}' successfully added to all wells")

            # If some curves are missing from some wells, show a warning
            if missing_curves_report:
                warning_message = "⚠️ CURVE CONSISTENCY WARNING\n\n"
                warning_message += "Some calculated curves were not added to all wells:\n\n"
                for curve_name, missing_wells in missing_curves_report.items():
                    warning_message += f"• '{curve_name}' missing from: {', '.join(missing_wells)}\n"
                warning_message += "\n💡 These curves will not appear in the column selection dropdown\n"
                warning_message += "because only curves present in ALL wells are shown.\n\n"
                warning_message += "This usually happens when:\n"
                warning_message += "• Calculations produce different variable names in different wells\n"
                warning_message += "• Some wells have data issues that prevent calculation\n"
                warning_message += "• Variable assignments are conditional\n\n"
                warning_message += "✅ Curves available in ALL wells will still be usable."

                messagebox.showwarning("Curve Consistency Warning", warning_message)
            else:
                print("✅ All calculated curves are available in all wells!")

        if not error_occurred:
            # Calculations successful for all LAS files
            print("✅ Calculator: All calculations executed successfully!")

            # Show success message with details about new curves
            success_message = f"✅ Calculations completed successfully!\n\n"
            success_message += f"Processed {len(las_files)} wells\n"

            if all_new_curves:
                success_message += f"Added {len(all_new_curves)} new calculated curves:\n"
                for curve_name in sorted(all_new_curves):
                    success_message += f"• {curve_name}\n"
                success_message += "\n✅ All new curves are available in all wells and will appear in column selection."
            else:
                success_message += "No new curves were created (calculations may have been empty or only modified existing data)."

            messagebox.showinfo("Calculator Success", success_message)
            return True
        else:
            # Ask the user if they want to retry or cancel
            retry = messagebox.askretrycancel(
                "Calculation Execution Error",
                "Calculation execution failed. Would you like to:\n\n"
                "• Retry: Modify your calculations and try again\n"
                "• Cancel: Skip calculator and continue with existing logs"
            )
            if not retry:  # User chose Cancel
                print("🔄 Calculator: User chose to skip after execution failure, continuing workflow")
                return True  # Continue workflow without these calculations
            else:  # User chose Retry
                print("🔄 Calculator: User chose to retry after execution failure, showing calculator again")
                continue  # Go to the start of the while loop

def get_column_names_and_depths(las_files, preloaded_excel_df=None): # Added preloaded_excel_df
    print("\n=== DEBUG: COLUMN AND DEPTH SELECTION ===")
    print(f"Number of LAS files: {len(las_files)}")

    # Print available columns in each LAS file
    for i, las in enumerate(las_files):
        well_name = las.well.WELL.value
        print(f"LAS file {i+1} - {well_name} has columns: {list(las.curves.keys())}")

    root = tk.Tk()
    root.title("Select Columns and Depth Ranges")

    x_var = tk.StringVar(root)
    y_var = tk.StringVar(root)
    class_var = tk.StringVar(root)
    z_var = tk.StringVar(root)  # New Z column variable

    # Get columns that are present in all LAS files
    common_columns = set(las_files[0].curves.keys())
    for las in las_files[1:]:
        common_columns.intersection_update(las.curves.keys())
    columns = sorted(common_columns)

    # Also get information about partially available columns for debugging
    all_columns = set()
    column_availability = {}
    for las in las_files:
        well_name = las.well.WELL.value
        well_curves = set(las.curves.keys())
        all_columns.update(well_curves)

        for curve in well_curves:
            if curve not in column_availability:
                column_availability[curve] = []
            column_availability[curve].append(well_name)

    print(f"\n=== COLUMN AVAILABILITY ANALYSIS ===")
    print(f"Common columns across ALL {len(las_files)} LAS files: {len(columns)}")
    print(f"Columns available in all wells: {columns}")

    # Show partially available columns (might include calculated curves that failed)
    partial_columns = {}
    for curve, wells in column_availability.items():
        if len(wells) < len(las_files) and len(wells) > 0:
            partial_columns[curve] = wells

    if partial_columns:
        print(f"\n⚠️ Partially available columns (not in all wells):")
        for curve, wells in partial_columns.items():
            missing_wells = [las.well.WELL.value for las in las_files if las.well.WELL.value not in wells]
            print(f"  • '{curve}': Available in {len(wells)}/{len(las_files)} wells, missing from: {missing_wells}")
        print("Note: These columns won't appear in dropdown menus because they're not in all wells.")

    print("=" * 50)

    if not columns:
        error_msg = "ERROR: No common columns found across all LAS files.\n\n"
        if partial_columns:
            error_msg += "However, some columns are available in some wells:\n"
            for curve, wells in list(partial_columns.items())[:5]:  # Show first 5
                error_msg += f"• '{curve}': Available in {len(wells)}/{len(las_files)} wells\n"
            error_msg += "\nThis often happens when calculated curves fail to be added to all wells."

            # Offer to use curves available in most wells
            use_partial = messagebox.askyesno(
                "No Common Columns",
                error_msg + "\n\nWould you like to include columns that are available in most wells?\n\n"
                "⚠️ Warning: This may cause errors for wells missing these columns."
            )

            if use_partial:
                # Use curves available in at least 80% of wells
                min_wells_required = max(1, int(0.8 * len(las_files)))
                partial_columns_filtered = {curve: wells for curve, wells in partial_columns.items()
                                          if len(wells) >= min_wells_required}

                if partial_columns_filtered:
                    columns = sorted(list(partial_columns_filtered.keys()))
                    print(f"✅ Using {len(columns)} columns available in most wells: {columns}")

                    # Show warning about which wells might have issues
                    warning_msg = "⚠️ USING PARTIALLY AVAILABLE COLUMNS\n\n"
                    warning_msg += f"Selected {len(columns)} columns available in at least {min_wells_required}/{len(las_files)} wells.\n\n"
                    warning_msg += "Potential issues by well:\n"
                    for las in las_files:
                        well_name = las.well.WELL.value
                        missing_in_well = [col for col in columns if col not in las.curves.keys()]
                        if missing_in_well:
                            warning_msg += f"• {well_name}: Missing {missing_in_well}\n"
                    warning_msg += "\n⚠️ Plots may show gaps or errors for wells with missing columns."

                    messagebox.showwarning("Partial Column Usage", warning_msg)
                else:
                    print("No columns meet the minimum availability threshold.")
                    messagebox.showerror("Error", "No columns are available in enough wells to proceed.")
                    root.destroy()
                    return None
            else:
                root.destroy()
                return None
        else:
            print(error_msg)
            messagebox.showerror("Error", error_msg)
            root.destroy()
            return None

    tk.Label(root, text="X Column:").grid(row=0, column=0, padx=5, pady=5)
    x_menu = ttk.Combobox(root, textvariable=x_var, values=columns)
    x_menu.grid(row=0, column=1, padx=5, pady=5)

    tk.Label(root, text="Y Column:").grid(row=1, column=0, padx=5, pady=5)
    y_menu = ttk.Combobox(root, textvariable=y_var, values=columns)
    y_menu.grid(row=1, column=1, padx=5, pady=5)

    # Class column selection
    class_options = ["(Optional) None"] + columns
    tk.Label(root, text="Class Column:").grid(row=2, column=0, padx=5, pady=5)
    class_menu = ttk.Combobox(root, textvariable=class_var, values=class_options)
    class_menu.grid(row=2, column=1, padx=5, pady=5)
    class_var.set("(Optional) None") # Default to None

    # Z column selection (for continuous colormap)
    z_options = ["(Optional) None"] + columns
    tk.Label(root, text="Z Column (Colormap):").grid(row=3, column=0, padx=5, pady=5)
    z_menu = ttk.Combobox(root, textvariable=z_var, values=z_options)
    z_menu.grid(row=3, column=1, padx=5, pady=5)
    z_var.set("(Optional) None") # Default to None

    # Set default values if common curves are present
    if 'DT' in columns:
        x_var.set('DT')
    if 'RHOB' in columns:
        y_var.set('RHOB')

    if 'LITHO_PETREL' in columns:
        pass
    elif 'FLUID_PETREL' in columns:
        pass

    # Function to handle mutual exclusivity between class and Z columns
    def on_class_change(*_args):
        selected_class = class_var.get()
        if selected_class != "(Optional) None":
            z_menu.set("(Optional) None")
            z_menu.config(state="disabled")
        else:
            z_menu.config(state="normal")

    def on_z_change(*_args):
        selected_z = z_var.get()
        if selected_z != "(Optional) None":
            class_menu.set("(Optional) None")
            class_menu.config(state="disabled")
        else:
            class_menu.config(state="normal")

    class_var.trace_add("write", on_class_change)
    z_var.trace_add("write", on_z_change)

    # Call the new get_depth_ranges function
    # Assuming log_keywords is defined globally or passed appropriately
    # For now, using a placeholder for log_keywords_for_finding_cols
    # TODO: Ensure log_keywords (or a similar dict) is available here
    # log_keywords_placeholder = {} # Placeholder
    selected_depth_ranges = get_depth_ranges(las_files, log_keywords, preloaded_excel_df)

    if selected_depth_ranges is None: # User might have cancelled
        root.destroy()
        return None

    result = {"x_col": None, "y_col": None, "class_col": None, "z_col": None, "depth_ranges": selected_depth_ranges}

    def on_submit():
        print("\n--- DEBUG: Column Selection Submission ---")
        x_col_val = x_var.get()
        print(f"Selected X Column: '{x_col_val}'")
        if not x_col_val or x_col_val == "(Optional) None":
            print("ERROR: X Column not selected")
            messagebox.showerror("Input Error", "X Column must be selected.")
            return

        y_col_val = y_var.get()
        print(f"Selected Y Column: '{y_col_val}'")
        if not y_col_val or y_col_val == "(Optional) None":
            print("ERROR: Y Column not selected")
            messagebox.showerror("Input Error", "Y Column must be selected.")
            return

        result["x_col"] = x_col_val
        result["y_col"] = y_col_val

        selected_class_val = class_var.get()
        print(f"Selected Class Column: '{selected_class_val}'")
        if selected_class_val == "(Optional) None" or not selected_class_val:
            result["class_col"] = None
            print("Class Column set to None (optional)")
        else:
            result["class_col"] = selected_class_val
            print(f"Class Column set to: {selected_class_val}")

        selected_z_val = z_var.get()
        print(f"Selected Z Column: '{selected_z_val}'")
        if selected_z_val == "(Optional) None" or not selected_z_val:
            result["z_col"] = None
            print("Z Column set to None (optional)")
        else:
            result["z_col"] = selected_z_val
            print(f"Z Column set to: {selected_z_val}")

        if result["class_col"] is not None and result["z_col"] is not None:
            print("ERROR: Both Class and Z columns cannot be selected simultaneously")
            messagebox.showerror("Input Error", "Both Class and Z columns cannot be selected simultaneously. Please choose only one.")
            return

        # Depth ranges are already in result["depth_ranges"] from get_depth_ranges call

        validation_result = validate_data_for_plotting(las_files, result["x_col"], result["y_col"],
                                                      result["class_col"], result["depth_ranges"], result["z_col"])

        if not validation_result['valid']:
            error_message = "The following issues were detected with the selected data:\n\n"
            for issue in validation_result['issues']:
                error_message += f"• {issue}\n"
            error_message += "\nThe plot may not display correctly. Do you want to proceed anyway?"
            proceed = messagebox.askyesno("Data Validation Warning", error_message)
            if not proceed:
                return

        elif validation_result['warnings']:
            warning_message = "The following issues were detected with the selected data:\n\n"
            for warning in validation_result['warnings']:
                warning_message += f"• {warning}\n"
            warning_message += "\nThe plot may not display optimally. Do you want to proceed?"
            proceed = messagebox.askyesno("Data Validation Warning", warning_message)
            if not proceed:
                return

        result["validation_stats"] = validation_result['stats']

        # The button for stats is now part of the get_depth_ranges dialog if Excel method is chosen,
        # or could be added here if manual method is chosen and stats are desired at this stage.
        # For simplicity, removing the explicit stats button here as it's complex to place conditionally
        # without knowing the exact layout after get_depth_ranges.
        # stats_button = tk.Button(root, text="Show Data Statistics",
        #                         command=lambda: show_data_statistics(validation_result['stats']))
        # stats_button.grid(row=4, column=0, columnspan=2, padx=5, pady=5) # Example placement

        root.quit()

    # The submit button's row needs to be adjusted based on the new layout after get_depth_ranges
    # For now, placing it at a generic row 4. This might need further adjustment.
    tk.Button(root, text="Submit Columns", command=on_submit).grid(row=4, column=0, columnspan=2, padx=5, pady=10)

    root.mainloop()
    root.destroy()

    return result

def get_plot_settings(las_files, x_col, y_col, class_col, depth_ranges, z_col=None):
    root = tk.Tk()
    root.title("Plot Settings")

    # Calculate initial min/max for x and y data based on selected columns and depth ranges
    initial_x_min, initial_x_max = float('inf'), float('-inf')
    initial_y_min, initial_y_max = float('inf'), float('-inf')

    if depth_ranges:
        for las in las_files:
            well_name = las.well.WELL.value
            if well_name in depth_ranges:
                top_depth, bottom_depth = depth_ranges[well_name]

                depth_curve = las.curves.get('DEPTH')
                if depth_curve is None:
                    continue # Skip if no DEPTH curve

                depth = np.array(depth_curve.data)
                mask = (depth >= top_depth) & (depth <= bottom_depth)

                if x_col in las.curves.keys():
                    x_data = np.array(las[x_col].data)[mask]
                    x_data_filtered = x_data[~np.isnan(x_data)]
                    if x_data_filtered.size > 0:
                        initial_x_min = min(initial_x_min, np.min(x_data_filtered))
                        initial_x_max = max(initial_x_max, np.max(x_data_filtered))

                if y_col in las.curves.keys():
                    y_data = np.array(las[y_col].data)[mask]
                    y_data_filtered = y_data[~np.isnan(y_data)]
                    if y_data_filtered.size > 0:
                        initial_y_min = min(initial_y_min, np.min(y_data_filtered))
                        initial_y_max = max(initial_y_max, np.max(y_data_filtered))
    else: # Fallback if depth_ranges is not available (e.g. called directly without full workflow)
        for las in las_files:
            if x_col in las.curves.keys():
                x_data = np.array(las[x_col].data)
                x_data_filtered = x_data[~np.isnan(x_data)]
                if x_data_filtered.size > 0:
                    initial_x_min = min(initial_x_min, np.min(x_data_filtered))
                    initial_x_max = max(initial_x_max, np.max(x_data_filtered))
            if y_col in las.curves.keys():
                y_data = np.array(las[y_col].data)
                y_data_filtered = y_data[~np.isnan(y_data)]
                if y_data_filtered.size > 0:
                    initial_y_min = min(initial_y_min, np.min(y_data_filtered))
                    initial_y_max = max(initial_y_max, np.max(y_data_filtered))

    # Create a canvas and scrollbar to make the window scrollable
    canvas = tk.Canvas(root)
    scrollbar = tk.Scrollbar(root, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda _e: canvas.configure(
            scrollregion=canvas.bbox("all")
        )
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Point size
    tk.Label(scrollable_frame, text="Point Size:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
    point_size = tk.Entry(scrollable_frame)
    point_size.insert(0, "50")  # Default value
    point_size.grid(row=0, column=1, padx=5, pady=5, sticky="w")

    # Histogram bin size
    tk.Label(scrollable_frame, text="Histogram Bins:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
    bin_size = tk.Entry(scrollable_frame)
    bin_size.insert(0, "30")  # Default value
    bin_size.grid(row=1, column=1, padx=5, pady=5, sticky="w")

    # Marginal plot options
    tk.Label(scrollable_frame, text="Marginal Plot Type:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
    marginal_plot_type = tk.StringVar(root)
    marginal_plot_type.set("Both")  # Default to showing both
    marginal_options = ttk.Combobox(scrollable_frame, textvariable=marginal_plot_type, values=["Histogram", "KDE", "Both"])
    marginal_options.grid(row=2, column=1, padx=5, pady=5, sticky="w")

    # Add legend style option
    tk.Label(scrollable_frame, text="Legend Style:").grid(row=3, column=0, padx=5, pady=5, sticky="w")
    legend_style = tk.StringVar(root)
    legend_options = ttk.Combobox(scrollable_frame, textvariable=legend_style, values=["Well-Class", "Class-Only", "Simple"])
    legend_options.grid(row=3, column=1, padx=5, pady=5, sticky="w")
    if class_col:
        legend_style.set("Well-Class")  # Default to Well-Class if class_col exists
    else:
        legend_style.set("Simple") # Default to simple if no class_col
        legend_options.config(values=["Simple"]) # Limit options if no class_col


    # Legend Font Size
    tk.Label(scrollable_frame, text="Legend Font Size:").grid(row=4, column=0, padx=5, pady=5, sticky="w")
    legend_font_size = tk.Entry(scrollable_frame)
    legend_font_size.insert(0, "10")  # Default font size
    legend_font_size.grid(row=4, column=1, padx=5, pady=5, sticky="w")

    # Legend Font Weight
    tk.Label(scrollable_frame, text="Legend Font Weight:").grid(row=5, column=0, padx=5, pady=5, sticky="w")
    legend_font_weight = tk.StringVar(root)
    legend_font_weight.set("normal")  # Default to normal
    legend_weight_options = ttk.Combobox(scrollable_frame, textvariable=legend_font_weight, values=["normal", "bold", "heavy", "light"])
    legend_weight_options.grid(row=5, column=1, padx=5, pady=5, sticky="w")

    # Spacer
    tk.Label(scrollable_frame, text="").grid(row=6, column=0, pady=10)

    # Symbol, color, and class name selection
    symbols = ['o', 's', 'D', '^', 'v', '>', '<', 'p', '*', 'h', 'H', '+', 'x', 'd', '|', '_']
    symbol_vars = {}
    color_vars = {}
    color_buttons = {}
    class_names_vars = {}

    class_customization_offset = 7 # Starting row for class customization

    if class_col: # Only show class customization if a class column is selected
        tk.Label(scrollable_frame, text="Select class names, symbols, and colors for each class:").grid(row=class_customization_offset, column=0, columnspan=4, padx=5, pady=5, sticky="w")

        # Get unique classes across all LAS files, handling NaN values
        unique_classes = set()
        for las in las_files:
            if class_col in las.curves: # Ensure class_col exists in the current las file
                class_data = np.array(las[class_col].data)
                unique_classes.update(set(class_data[~np.isnan(class_data)]))  # Exclude NaN values
        unique_classes = sorted(unique_classes)

        has_nan_class = False
        for las in las_files:
            if class_col in las.curves:
                 if np.isnan(np.array(las[class_col].data)).any():
                    has_nan_class = True
                    break

        current_row = class_customization_offset + 1
        for i, class_value in enumerate(unique_classes):
            tk.Label(scrollable_frame, text=f"Class Value {class_value}:").grid(row=current_row + i, column=0, padx=5, pady=5, sticky="w")
            class_names_vars[class_value] = tk.Entry(scrollable_frame)
            class_names_vars[class_value].insert(0, f"Class {class_value}")
            class_names_vars[class_value].grid(row=current_row + i, column=1, padx=5, pady=5)
            symbol_vars[class_value] = tk.StringVar(root)
            symbol_vars[class_value].set(symbols[i % len(symbols)])
            symbol_menu = ttk.Combobox(scrollable_frame, textvariable=symbol_vars[class_value], values=symbols)
            symbol_menu.grid(row=current_row + i, column=2, padx=5, pady=5)
            color_vars[class_value] = tk.StringVar(root)

            # Get color from colormap and convert to hex string for Tkinter
            if len(unique_classes) > 0:
                # Use pyplot's get_cmap instead of plt.cm.get_cmap (to avoid deprecation warning)
                cmap = plt.colormaps['viridis']
                rgba_color = cmap(i / max(1, len(unique_classes) - 1))
                hex_color = "#{:02x}{:02x}{:02x}".format(
                    int(rgba_color[0] * 255),
                    int(rgba_color[1] * 255),
                    int(rgba_color[2] * 255)
                )
                color_vars[class_value].set(hex_color)
            else:
                color_vars[class_value].set("#000000")  # Default to black

            def make_color_chooser(class_val_local): # Use a local copy for the closure
                def choose_color():
                    color_result = colorchooser.askcolor(parent=root, title=f"Choose color for {class_val_local}")
                    if color_result[1]:
                        color_vars[class_val_local].set(color_result[1])
                        color_buttons[class_val_local].config(bg=color_result[1])
                return choose_color

            color_buttons[class_value] = tk.Button(scrollable_frame, text="Choose Color", command=make_color_chooser(class_value))
            color_buttons[class_value].grid(row=current_row + i, column=3, padx=5, pady=5)
            if color_vars[class_value].get(): # Set initial button color
                 color_buttons[class_value].config(bg=color_vars[class_value].get())


        nan_row_offset = current_row + len(unique_classes)
        if has_nan_class:
            tk.Label(scrollable_frame, text="NaN (No Data):").grid(row=nan_row_offset, column=0, padx=5, pady=5, sticky="w")
            class_names_vars['NaN'] = tk.Entry(scrollable_frame)
            class_names_vars['NaN'].insert(0, "No Data")
            class_names_vars['NaN'].grid(row=nan_row_offset, column=1, padx=5, pady=5)
            symbol_vars["NaN"] = tk.StringVar(value="x")
            symbol_menu_nan = ttk.Combobox(scrollable_frame, textvariable=symbol_vars["NaN"], values=symbols)
            symbol_menu_nan.grid(row=nan_row_offset, column=2, padx=5, pady=5)
            color_vars["NaN"] = tk.StringVar(value="#808080")
            color_buttons["NaN"] = tk.Button(scrollable_frame, text="Choose Color", command=make_color_chooser("NaN"))
            color_buttons["NaN"].grid(row=nan_row_offset, column=3, padx=5, pady=5)
            color_buttons["NaN"].config(bg=color_vars["NaN"].get())
            axis_offset = nan_row_offset + 2
        else:
            axis_offset = nan_row_offset + 1 # No NaN row, so next section starts sooner
    else: # No class_col selected
        tk.Label(scrollable_frame, text="No class column selected. Class-specific settings disabled.").grid(row=class_customization_offset, column=0, columnspan=4, padx=5, pady=5, sticky="w")
        axis_offset = class_customization_offset + 1 # Row for next section
        if legend_options: legend_options.config(state=tk.DISABLED)


    # Axis Title and Tick Label Customization (adjust row based on whether class section was shown)
    # X-axis title customization
    tk.Label(scrollable_frame, text="X-axis Title Text:").grid(row=axis_offset, column=0, padx=5, pady=5, sticky="w")
    x_title_text = tk.Entry(scrollable_frame)
    x_title_text.insert(0, x_col)  # Default to x_col
    x_title_text.grid(row=axis_offset, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="X-axis Title Font Size:").grid(row=axis_offset+1, column=0, padx=5, pady=5, sticky="w")
    x_title_size = tk.Entry(scrollable_frame)
    x_title_size.insert(0, "12")  # Default font size
    x_title_size.grid(row=axis_offset+1, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="X-axis Title Font Weight:").grid(row=axis_offset+2, column=0, padx=5, pady=5, sticky="w")
    x_title_weight = tk.StringVar(root)
    x_title_weight.set("normal")  # Default to normal
    x_weight_menu = ttk.Combobox(scrollable_frame, textvariable=x_title_weight, values=["normal", "bold", "heavy", "light"])
    x_weight_menu.grid(row=axis_offset+2, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="X-axis Title Font Style:").grid(row=axis_offset+3, column=0, padx=5, pady=5, sticky="w")
    x_title_style = tk.StringVar(root)
    x_title_style.set("normal")  # Default to normal
    x_style_menu = ttk.Combobox(scrollable_frame, textvariable=x_title_style, values=["normal", "italic", "oblique"])
    x_style_menu.grid(row=axis_offset+3, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="X-axis Title Font Family:").grid(row=axis_offset+4, column=0, padx=5, pady=5, sticky="w")
    x_title_family = tk.Entry(scrollable_frame)
    x_title_family.insert(0, "Arial")  # Default font family
    x_title_family.grid(row=axis_offset+4, column=1, padx=5, pady=5)

    # Spacer
    tk.Label(scrollable_frame, text="").grid(row=axis_offset+5, column=0, pady=10)

    # Y-axis title customization
    y_axis_offset = axis_offset + 6
    tk.Label(scrollable_frame, text="Y-axis Title Text:").grid(row=y_axis_offset, column=0, padx=5, pady=5, sticky="w")
    y_title_text = tk.Entry(scrollable_frame)
    y_title_text.insert(0, y_col)
    y_title_text.grid(row=y_axis_offset, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="Y-axis Title Font Size:").grid(row=y_axis_offset+1, column=0, padx=5, pady=5, sticky="w")
    y_title_size = tk.Entry(scrollable_frame)
    y_title_size.insert(0, "12")
    y_title_size.grid(row=y_axis_offset+1, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="Y-axis Title Font Weight:").grid(row=y_axis_offset+2, column=0, padx=5, pady=5, sticky="w")
    y_title_weight = tk.StringVar(root)
    y_title_weight.set("normal")
    y_weight_menu = ttk.Combobox(scrollable_frame, textvariable=y_title_weight, values=["normal", "bold", "heavy", "light"])
    y_weight_menu.grid(row=y_axis_offset+2, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="Y-axis Title Font Style:").grid(row=y_axis_offset+3, column=0, padx=5, pady=5, sticky="w")
    y_title_style = tk.StringVar(root)
    y_title_style.set("normal")
    y_style_menu = ttk.Combobox(scrollable_frame, textvariable=y_title_style, values=["normal", "italic", "oblique"])
    y_style_menu.grid(row=y_axis_offset+3, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="Y-axis Title Font Family:").grid(row=y_axis_offset+4, column=0, padx=5, pady=5, sticky="w")
    y_title_family = tk.Entry(scrollable_frame)
    y_title_family.insert(0, "Arial")
    y_title_family.grid(row=y_axis_offset+4, column=1, padx=5, pady=5)

    # Spacer
    tk.Label(scrollable_frame, text="").grid(row=y_axis_offset+5, column=0, pady=10)

    # Axis Limits Section
    axis_limits_offset = y_axis_offset + 6
    tk.Label(scrollable_frame, text="--- Axis Limits (leave blank/invalid for auto) ---", font=('Helvetica', 10, 'italic')).grid(row=axis_limits_offset, column=0, columnspan=2, padx=5, pady=5, sticky="w")

    tk.Label(scrollable_frame, text="X-axis Min:").grid(row=axis_limits_offset+1, column=0, padx=5, pady=5, sticky="w")
    x_min_entry = tk.Entry(scrollable_frame)
    if initial_x_min != float('inf'): x_min_entry.insert(0, str(initial_x_min))
    x_min_entry.grid(row=axis_limits_offset+1, column=1, padx=5, pady=5, sticky="w")

    tk.Label(scrollable_frame, text="X-axis Max:").grid(row=axis_limits_offset+2, column=0, padx=5, pady=5, sticky="w")
    x_max_entry = tk.Entry(scrollable_frame)
    if initial_x_max != float('-inf'): x_max_entry.insert(0, str(initial_x_max))
    x_max_entry.grid(row=axis_limits_offset+2, column=1, padx=5, pady=5, sticky="w")

    tk.Label(scrollable_frame, text="Y-axis Min:").grid(row=axis_limits_offset+3, column=0, padx=5, pady=5, sticky="w")
    y_min_entry = tk.Entry(scrollable_frame)
    if initial_y_min != float('inf'): y_min_entry.insert(0, str(initial_y_min))
    y_min_entry.grid(row=axis_limits_offset+3, column=1, padx=5, pady=5, sticky="w")

    tk.Label(scrollable_frame, text="Y-axis Max:").grid(row=axis_limits_offset+4, column=0, padx=5, pady=5, sticky="w")
    y_max_entry = tk.Entry(scrollable_frame)
    if initial_y_max != float('-inf'): y_max_entry.insert(0, str(initial_y_max))
    y_max_entry.grid(row=axis_limits_offset+4, column=1, padx=5, pady=5, sticky="w")

    # Downsampling Factor
    downsampling_offset = axis_limits_offset + 5 # Adjusted offset
    tk.Label(scrollable_frame, text="Downsampling Factor (1=None):").grid(row=downsampling_offset, column=0, padx=5, pady=5, sticky="w")
    downsampling_factor_entry = tk.Entry(scrollable_frame)
    downsampling_factor_entry.insert(0, "1") # Default to no downsampling
    downsampling_factor_entry.grid(row=downsampling_offset, column=1, padx=5, pady=5)

    # Enhanced Z Column Settings (only shown when z_col is selected)
    z_settings_offset = downsampling_offset + 1
    if z_col is not None:
        tk.Label(scrollable_frame, text="--- Z Column Settings ---", font=('Helvetica', 10, 'italic')).grid(row=z_settings_offset, column=0, columnspan=2, padx=5, pady=5, sticky="w")

        # Colormap Category selection
        tk.Label(scrollable_frame, text="Colormap Category:").grid(row=z_settings_offset+1, column=0, padx=5, pady=5, sticky="w")
        colormap_category_var = tk.StringVar(root)
        colormap_category_var.set("Sequential")  # Default category
        colormap_category_options = ttk.Combobox(scrollable_frame, textvariable=colormap_category_var,
                                               values=list(COLORMAP_CATEGORIES.keys()), state="readonly")
        colormap_category_options.grid(row=z_settings_offset+1, column=1, padx=5, pady=5, sticky="w")

        # Sequential Subcategory selection (only for Sequential category)
        tk.Label(scrollable_frame, text="Subcategory:").grid(row=z_settings_offset+2, column=0, padx=5, pady=5, sticky="w")
        colormap_subcategory_var = tk.StringVar(root)
        colormap_subcategory_var.set("Perceptually Uniform")  # Default subcategory
        colormap_subcategory_options = ttk.Combobox(scrollable_frame, textvariable=colormap_subcategory_var,
                                                  values=get_sequential_subcategories(), state="readonly")
        colormap_subcategory_options.grid(row=z_settings_offset+2, column=1, padx=5, pady=5, sticky="w")

        # Colormap selection
        tk.Label(scrollable_frame, text="Colormap:").grid(row=z_settings_offset+3, column=0, padx=5, pady=5, sticky="w")
        colormap_var = tk.StringVar(root)
        colormap_var.set("viridis")  # Default colormap
        colormap_options = ttk.Combobox(scrollable_frame, textvariable=colormap_var,
                                      values=get_colormap_options("Sequential", "Perceptually Uniform"), state="readonly")
        colormap_options.grid(row=z_settings_offset+3, column=1, padx=5, pady=5, sticky="w")

        # Colormap reversal checkbox
        reverse_colormap_var = tk.BooleanVar(root)
        reverse_colormap_var.set(False)  # Default to not reversed
        reverse_colormap_check = tk.Checkbutton(scrollable_frame, text="Reverse Colormap", variable=reverse_colormap_var)
        reverse_colormap_check.grid(row=z_settings_offset+4, column=0, columnspan=2, padx=5, pady=5, sticky="w")

        # Show colorbar checkbox
        show_colorbar_var = tk.BooleanVar(root)
        show_colorbar_var.set(True)  # Default to showing colorbar
        show_colorbar_check = tk.Checkbutton(scrollable_frame, text="Show Colorbar", variable=show_colorbar_var)
        show_colorbar_check.grid(row=z_settings_offset+5, column=0, columnspan=2, padx=5, pady=5, sticky="w")

        # Colorbar label
        tk.Label(scrollable_frame, text="Colorbar Label:").grid(row=z_settings_offset+6, column=0, padx=5, pady=5, sticky="w")
        colorbar_label_entry = tk.Entry(scrollable_frame)
        colorbar_label_entry.insert(0, z_col)  # Default to z_col name
        colorbar_label_entry.grid(row=z_settings_offset+6, column=1, padx=5, pady=5, sticky="w")

        # Colorbar position
        tk.Label(scrollable_frame, text="Colorbar Position:").grid(row=z_settings_offset+7, column=0, padx=5, pady=5, sticky="w")
        colorbar_position_var = tk.StringVar(root)
        colorbar_position_var.set("right")  # Default position
        colorbar_position_options = ttk.Combobox(scrollable_frame, textvariable=colorbar_position_var,
                                               values=["right", "left", "top", "bottom"], state="readonly")
        colorbar_position_options.grid(row=z_settings_offset+7, column=1, padx=5, pady=5, sticky="w")

        # Colorbar size
        tk.Label(scrollable_frame, text="Colorbar Size:").grid(row=z_settings_offset+8, column=0, padx=5, pady=5, sticky="w")
        colorbar_size_entry = tk.Entry(scrollable_frame)
        colorbar_size_entry.insert(0, "0.05")  # Default size (5% of plot)
        colorbar_size_entry.grid(row=z_settings_offset+8, column=1, padx=5, pady=5, sticky="w")

        # Callback functions for dynamic updates
        def update_colormap_options_callback(*_args):
            """Update colormap options based on category and subcategory selection."""
            category = colormap_category_var.get()
            subcategory = colormap_subcategory_var.get() if category == "Sequential" else None

            # Update subcategory visibility
            if category == "Sequential":
                colormap_subcategory_options.config(state="readonly")
            else:
                colormap_subcategory_options.config(state="disabled")
                colormap_subcategory_var.set("")

            # Update colormap options
            new_options = get_colormap_options(category, subcategory)
            colormap_options.config(values=new_options)

            # Set default colormap for the category
            if new_options:
                colormap_var.set(new_options[0])

        # Bind callback functions
        colormap_category_var.trace_add("write", update_colormap_options_callback)
        colormap_subcategory_var.trace_add("write", update_colormap_options_callback)

        # Update tick_offset to account for enhanced Z settings
        tick_offset = z_settings_offset + 9
    else:
        # No Z column selected, tick_offset remains as before
        tick_offset = downsampling_offset + 1

    # Tick label customization
    tk.Label(scrollable_frame, text="Tick Label Font Size:").grid(row=tick_offset, column=0, padx=5, pady=5, sticky="w")
    tick_label_size = tk.Entry(scrollable_frame)
    tick_label_size.insert(0, "10")
    tick_label_size.grid(row=tick_offset, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="Tick Label Font Weight:").grid(row=tick_offset+1, column=0, padx=5, pady=5, sticky="w")
    tick_label_weight = tk.StringVar(root)
    tick_label_weight.set("normal")
    tick_weight_menu = ttk.Combobox(scrollable_frame, textvariable=tick_label_weight, values=["normal", "bold", "heavy", "light"])
    tick_weight_menu.grid(row=tick_offset+1, column=1, padx=5, pady=5)

    tk.Label(scrollable_frame, text="Tick Label Font Style:").grid(row=tick_offset+2, column=0, padx=5, pady=5, sticky="w")
    tick_label_style = tk.StringVar(root)
    tick_label_style.set("normal")
    tick_style_menu = ttk.Combobox(scrollable_frame, textvariable=tick_label_style, values=["normal", "italic", "oblique"])
    tick_style_menu.grid(row=tick_offset+2, column=1, padx=5, pady=5)

    settings = {}

    def on_submit():
        try:
            settings['point_size'] = float(point_size.get())
            settings['bin_size'] = int(bin_size.get())
            settings['marginal_plot_type'] = marginal_plot_type.get()
            settings['legend_style'] = legend_style.get()
            settings['legend_font_size'] = float(legend_font_size.get())
            settings['legend_font_weight'] = legend_font_weight.get()

            if class_col: # Only populate these if class_col was active
                settings['symbols'] = {t: symbol_vars[t].get() for t in symbol_vars}
                settings['colors'] = {t: color_vars[t].get() for t in color_vars}
                settings['class_names'] = {t: class_names_vars[t].get() for t in class_names_vars}
            else:
                settings['symbols'] = {}
                settings['colors'] = {}
                settings['class_names'] = {}

            settings['x_title_text'] = x_title_text.get()
            settings['x_title_size'] = float(x_title_size.get())
            settings['x_title_style'] = x_title_style.get()
            settings['x_title_weight'] = x_title_weight.get()
            settings['x_title_family'] = x_title_family.get()

            settings['y_title_text'] = y_title_text.get()
            settings['y_title_size'] = float(y_title_size.get())
            settings['y_title_style'] = y_title_style.get()
            settings['y_title_weight'] = y_title_weight.get()
            settings['y_title_family'] = y_title_family.get()

            # Retrieve user-defined axis limits
            try: settings['x_axis_min_user'] = float(x_min_entry.get())
            except ValueError: settings['x_axis_min_user'] = None
            try: settings['x_axis_max_user'] = float(x_max_entry.get())
            except ValueError: settings['x_axis_max_user'] = None
            try: settings['y_axis_min_user'] = float(y_min_entry.get())
            except ValueError: settings['y_axis_min_user'] = None
            try: settings['y_axis_max_user'] = float(y_max_entry.get())
            except ValueError: settings['y_axis_max_user'] = None

            settings['tick_label_size'] = float(tick_label_size.get())
            settings['tick_label_style'] = tick_label_style.get()
            settings['tick_label_weight'] = tick_label_weight.get()

            settings['downsampling_factor'] = int(downsampling_factor_entry.get())
            if settings['downsampling_factor'] < 1:
                messagebox.showerror("Error", "Downsampling factor must be 1 or greater.")
                return # Stay in dialog if error

            # Collect enhanced Z column settings if z_col is provided
            if z_col is not None:
                # Get base colormap and apply reversal if needed
                base_colormap = colormap_var.get()
                reverse_colormap = reverse_colormap_var.get()
                final_colormap = apply_colormap_reversal(base_colormap, reverse_colormap)

                # Validate colormap
                if not validate_colormap(final_colormap):
                    messagebox.showerror("Error", f"Colormap '{final_colormap}' is not available. Using 'viridis' as fallback.")
                    final_colormap = 'viridis'

                settings['colormap'] = final_colormap
                settings['colormap_category'] = colormap_category_var.get()
                settings['colormap_subcategory'] = colormap_subcategory_var.get() if colormap_category_var.get() == "Sequential" else None
                settings['reverse_colormap'] = reverse_colormap
                settings['show_colorbar'] = show_colorbar_var.get()
                settings['colorbar_label'] = colorbar_label_entry.get()
                settings['colorbar_position'] = colorbar_position_var.get()

                # Validate and set colorbar size
                try:
                    colorbar_size = float(colorbar_size_entry.get())
                    if colorbar_size <= 0 or colorbar_size > 0.5:
                        messagebox.showerror("Error", "Colorbar size must be between 0 and 0.5 (0-50% of plot).")
                        return
                    settings['colorbar_size'] = colorbar_size
                except ValueError:
                    messagebox.showerror("Error", "Colorbar size must be a valid number.")
                    return
            else:
                settings['colormap'] = None
                settings['colormap_category'] = None
                settings['colormap_subcategory'] = None
                settings['reverse_colormap'] = False
                settings['show_colorbar'] = False
                settings['colorbar_label'] = None
                settings['colorbar_position'] = 'right'
                settings['colorbar_size'] = 0.05

            root.quit()
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for all numeric setting fields (e.g., Point Size, Font Sizes, Bins).")

    submit_row = tick_offset + 3
    tk.Button(scrollable_frame, text="Submit", command=on_submit).grid(row=submit_row, column=1, columnspan=2, padx=5, pady=10)

    root.mainloop()
    root.destroy()

    return settings

def create_plot(las_files, x_col, y_col, class_col, depth_ranges, settings, z_col=None):
    print("\n=== DEBUG: PLOT CREATION ===")
    print(f"Creating plot for: x_col={x_col}, y_col={y_col}, class_col={class_col}, z_col={z_col}")
    print(f"Settings: {settings}")

    # Validate data before attempting to create plot
    validation_result = validate_data_for_plotting(las_files, x_col, y_col, class_col, depth_ranges, z_col)

    # Create figure
    fig = plt.figure(figsize=(12, 10))

    # If data is invalid, show error message and return
    if not validation_result['valid']:
        print("Validation failed - cannot create plot")
        ax = fig.add_subplot(111)

        error_message = "Cannot create plot:\n\n"
        for issue in validation_result['issues']:
            error_message += f"• {issue}\n"

        if validation_result['warnings']:
            error_message += "\nWarnings:\n"
            for warning in validation_result['warnings']:
                error_message += f"• {warning}\n"

        ax.text(0.5, 0.5, error_message,
                horizontalalignment='center',
                verticalalignment='center',
                transform=ax.transAxes,
                wrap=True,
                fontsize=12)

        ax.set_xticks([])
        ax.set_yticks([])
        plt.title(f"Plot Error: {x_col} vs {y_col}", fontsize=14, fontweight='bold')
        plt.show()
        return

    print("Validation passed - proceeding with plot creation")

    # Setup plot layout
    ax_histx, ax_histy = None, None # Initialize

    if class_col and z_col is None: # If a class column was selected and no Z column, setup for marginal plots
        gs = fig.add_gridspec(4, 4, hspace=0.05, wspace=0.05) # Add some minimal space
        ax_main = fig.add_subplot(gs[1:, :-1])
        ax_histx = fig.add_subplot(gs[0, :-1], sharex=ax_main)
        ax_histy = fig.add_subplot(gs[1:, -1], sharey=ax_main)
        # Hide ticks and labels for marginal plots initially
        plt.setp(ax_histx.get_xticklabels(), visible=False)
        plt.setp(ax_histx.get_yticklabels(), visible=False)
        ax_histx.set_yticks([])
        plt.setp(ax_histy.get_xticklabels(), visible=False)
        plt.setp(ax_histy.get_yticklabels(), visible=False)
        ax_histy.set_xticks([])
        print("Setup plot with marginal histograms (class column selected)")
    else: # No class column selected OR Z column selected, main plot only
        gs = fig.add_gridspec(1, 1)
        ax_main = fig.add_subplot(gs[0, 0])
        if z_col:
            print("Setup simple plot (Z column selected - no marginal plots)")
        else:
            print("Setup simple plot (no class column)")

    all_x_data = []
    all_y_data = []
    all_class_data_for_plot = [] # Will store actual class values for plotted points or be empty
    all_z_data = [] # Will store Z values for continuous colormap

    overall_x_min, overall_x_max = float('inf'), float('-inf')
    overall_y_min, overall_y_max = float('inf'), float('-inf')

    # Track wells with data issues for better user feedback
    wells_with_issues = []

    print("\nProcessing data for each well:")
    for las in las_files:
        well_name = las.well.WELL.value
        print(f"\nProcessing well: {well_name}")

        # Ensure depth_ranges has entry for well_name, critical for processing
        if well_name not in depth_ranges:
            print(f"  WARNING: No depth range specified for well {well_name}")
            wells_with_issues.append(f"{well_name}: No depth range specified")
            continue
        top_depth, bottom_depth = depth_ranges[well_name]
        print(f"  Depth range: {top_depth} to {bottom_depth}")

        depth_curve = las.curves.get('DEPTH')
        if depth_curve is None:
            print(f"  WARNING: Missing DEPTH curve for well {well_name}")
            wells_with_issues.append(f"{well_name}: Missing DEPTH curve")
            continue
        depth = np.array(depth_curve.data)
        print(f"  DEPTH curve range: {np.min(depth)} to {np.max(depth)}")

        # Check if required columns exist
        missing_cols = []
        if x_col not in las.curves:
            missing_cols.append(x_col)
        if y_col not in las.curves:
            missing_cols.append(y_col)

        if missing_cols:
            print(f"  WARNING: Missing columns for well {well_name}: {', '.join(missing_cols)}")
            wells_with_issues.append(f"{well_name}: Missing columns: {', '.join(missing_cols)}")
            continue

        # Create depth mask - with additional debugging
        print(f"  Creating depth mask for range: {top_depth} to {bottom_depth}")
        print(f"  Actual depth range in data: {np.min(depth)} to {np.max(depth)}")

        # Check if depth values are within a reasonable range
        if np.min(depth) > 1e10 or np.max(depth) < -1e10:
            print(f"  WARNING: Suspicious depth values detected for well {well_name}")
            print(f"  Sample depth values: {depth[:10]}")

        # TROUBLESHOOTING: Use a more lenient depth range if needed
        # If the specified range doesn't include any points, try using the full range
        mask = (depth >= top_depth) & (depth <= bottom_depth)
        points_in_range = np.sum(mask)
        print(f"  Points in depth range: {points_in_range}/{len(depth)} ({points_in_range/len(depth)*100:.1f}%)")

        if not np.any(mask): # Skip if no data in range
            print(f"  WARNING: No data in specified depth range for well {well_name}")
            print(f"  TROUBLESHOOTING: Attempting to use full depth range instead")

            # Try using the full depth range as a fallback
            full_range_mask = np.ones_like(depth, dtype=bool)
            if np.any(full_range_mask):
                print(f"  Using full depth range as fallback")
                mask = full_range_mask
                points_in_range = np.sum(mask)
                print(f"  Points in full range: {points_in_range}/{len(depth)}")
            else:
                wells_with_issues.append(f"{well_name}: No data in specified depth range ({top_depth}-{bottom_depth})")
                continue

        # Extract data within depth range
        x_data_full = np.array(las[x_col].data)[mask]
        y_data_full = np.array(las[y_col].data)[mask]

        # Extract Z data if available
        z_data_full = None
        if z_col and z_col in las.curves:
            z_data_full = np.array(las[z_col].data)[mask]
            z_nan_count = np.sum(np.isnan(z_data_full))
            print(f"  {z_col} NaN values: {z_nan_count}/{len(z_data_full)} ({z_nan_count/len(z_data_full)*100:.1f}%)")

        # Debug: Check for NaN values in x_data and y_data
        x_nan_count = np.sum(np.isnan(x_data_full))
        y_nan_count = np.sum(np.isnan(y_data_full))
        print(f"  {x_col} NaN values: {x_nan_count}/{len(x_data_full)} ({x_nan_count/len(x_data_full)*100:.1f}%)")
        print(f"  {y_col} NaN values: {y_nan_count}/{len(y_data_full)} ({y_nan_count/len(y_data_full)*100:.1f}%)")

        # Extract class data if available
        current_las_class_data = None
        if class_col and class_col in las.curves:
            current_las_class_data = np.array(las[class_col].data)[mask]
            class_nan_count = np.sum(np.isnan(current_las_class_data))
            print(f"  {class_col} NaN values: {class_nan_count}/{len(current_las_class_data)} ({class_nan_count/len(current_las_class_data)*100:.1f}%)")

        # Filter out NaN values
        valid_mask_xy = ~np.isnan(x_data_full) & ~np.isnan(y_data_full)

        # If Z column is used, also filter by Z validity
        if z_data_full is not None:
            valid_mask_xy = valid_mask_xy & ~np.isnan(z_data_full)

        valid_count = np.sum(valid_mask_xy)
        print(f"  Valid (non-NaN) points for both {x_col} and {y_col}: {valid_count}/{len(x_data_full)} ({valid_count/max(1,len(x_data_full))*100:.1f}%)")

        # TROUBLESHOOTING: Print sample values before filtering
        print(f"  Sample {x_col} values before filtering: {x_data_full[:5]}")
        print(f"  Sample {y_col} values before filtering: {y_data_full[:5]}")
        if z_data_full is not None:
            print(f"  Sample {z_col} values before filtering: {z_data_full[:5]}")

        x_data_clean = x_data_full[valid_mask_xy]
        y_data_clean = y_data_full[valid_mask_xy]
        z_data_clean = z_data_full[valid_mask_xy] if z_data_full is not None else None

        if x_data_clean.size == 0: # Skip if no valid X,Y data points
            print(f"  WARNING: No valid data points (all NaN values) for well {well_name}")
            print(f"  TROUBLESHOOTING: Attempting to use interpolation to fill NaN values")

            # Try to interpolate NaN values as a fallback
            try:
                # Create a simple linear interpolation for NaN values
                x_indices = np.arange(len(x_data_full))
                y_indices = np.arange(len(y_data_full))

                # For X data
                x_valid_mask = ~np.isnan(x_data_full)
                if np.any(x_valid_mask):
                    x_valid_indices = x_indices[x_valid_mask]
                    x_valid_values = x_data_full[x_valid_mask]
                    if len(x_valid_indices) > 0:
                        # Use nearest neighbor interpolation for simplicity
                        x_data_clean = np.interp(x_indices, x_valid_indices, x_valid_values,
                                               left=np.nanmean(x_data_full), right=np.nanmean(x_data_full))
                        print(f"  Interpolated {x_col} values: {x_data_clean[:5]}")

                # For Y data
                y_valid_mask = ~np.isnan(y_data_full)
                if np.any(y_valid_mask):
                    y_valid_indices = y_indices[y_valid_mask]
                    y_valid_values = y_data_full[y_valid_mask]
                    if len(y_valid_indices) > 0:
                        # Use nearest neighbor interpolation for simplicity
                        y_data_clean = np.interp(y_indices, y_valid_indices, y_valid_values,
                                               left=np.nanmean(y_data_full), right=np.nanmean(y_data_full))
                        print(f"  Interpolated {y_col} values: {y_data_clean[:5]}")

                # Check if we now have valid data
                if len(x_data_clean) > 0 and len(y_data_clean) > 0:
                    print(f"  Successfully interpolated data: {len(x_data_clean)} points")
                else:
                    wells_with_issues.append(f"{well_name}: No valid data points (all NaN values)")
                    continue
            except Exception as e:
                print(f"  ERROR during interpolation: {str(e)}")
                wells_with_issues.append(f"{well_name}: No valid data points (all NaN values)")
                continue

        # Debug: Print sample values
        if len(x_data_clean) > 0:
            print(f"  Sample {x_col} values: {x_data_clean[:5]}")
            print(f"  Sample {y_col} values: {y_data_clean[:5]}")

        # Process class data
        if current_las_class_data is not None:
            class_data_for_xy_clean = current_las_class_data[valid_mask_xy]
            if len(class_data_for_xy_clean) > 0:
                print(f"  Sample {class_col} values: {class_data_for_xy_clean[:5]}")
        else:
            class_data_for_xy_clean = np.empty(x_data_clean.shape) # Placeholder if no class_col
            class_data_for_xy_clean[:] = np.nan # Fill with NaN or a specific marker
            print("  No class data available for this well")

        # Update overall min/max values
        if x_data_clean.size > 0:
            overall_x_min = min(overall_x_min, np.min(x_data_clean))
            overall_x_max = max(overall_x_max, np.max(x_data_clean))
        if y_data_clean.size > 0:
            overall_y_min = min(overall_y_min, np.min(y_data_clean))
            overall_y_max = max(overall_y_max, np.max(y_data_clean))

        # Apply downsampling if needed
        downsample_factor = settings.get('downsampling_factor', 1)
        if downsample_factor > 1 and len(x_data_clean) > downsample_factor:
            print(f"  Applying downsampling factor: {downsample_factor}")
            indices = np.arange(len(x_data_clean))
            sampled_indices = indices[::downsample_factor]
            x_data_plot = x_data_clean[sampled_indices]
            y_data_plot = y_data_clean[sampled_indices]
            z_data_plot = z_data_clean[sampled_indices] if z_data_clean is not None else None
            class_data_to_extend = class_data_for_xy_clean[sampled_indices] if class_col else np.full(len(x_data_plot), np.nan)
            print(f"  After downsampling: {len(x_data_plot)} points")
        else:
            x_data_plot = x_data_clean
            y_data_plot = y_data_clean
            z_data_plot = z_data_clean
            class_data_to_extend = class_data_for_xy_clean if class_col else np.full(len(x_data_plot), np.nan)
            print(f"  No downsampling applied: {len(x_data_plot)} points")

        # Add data to overall collections
        all_x_data.extend(x_data_plot)
        all_y_data.extend(y_data_plot)
        if class_col: # Only extend class data if class_col was active
             all_class_data_for_plot.extend(class_data_to_extend)
        if z_data_plot is not None: # Only extend Z data if z_col was active
             all_z_data.extend(z_data_plot)

        print(f"  Successfully added {len(x_data_plot)} data points from well {well_name}")


    # Convert lists to numpy arrays
    all_x_data_np = np.array(all_x_data)
    all_y_data_np = np.array(all_y_data)
    all_z_data_np = np.array(all_z_data) if all_z_data else None

    # Debug: Check final data arrays
    print(f"\nFinal data arrays:")
    print(f"all_x_data_np shape: {all_x_data_np.shape}")
    print(f"all_y_data_np shape: {all_y_data_np.shape}")
    if all_z_data_np is not None:
        print(f"all_z_data_np shape: {all_z_data_np.shape}")

    if len(all_x_data_np) > 0:
        print(f"Sample x values: {all_x_data_np[:5]}")
        print(f"Sample y values: {all_y_data_np[:5]}")
        if all_z_data_np is not None and len(all_z_data_np) > 0:
            print(f"Sample z values: {all_z_data_np[:5]}")
        print(f"X range: {np.min(all_x_data_np)} to {np.max(all_x_data_np)}")
        print(f"Y range: {np.min(all_y_data_np)} to {np.max(all_y_data_np)}")
        if all_z_data_np is not None and len(all_z_data_np) > 0:
            print(f"Z range: {np.min(all_z_data_np)} to {np.max(all_z_data_np)}")

    # Ensure all_class_data_for_plot is an array, even if empty or from non-class_col case
    all_class_data_np = np.array(all_class_data_for_plot) if class_col and len(all_class_data_for_plot) > 0 else None
    if all_class_data_np is not None:
        print(f"all_class_data_np shape: {all_class_data_np.shape}")
        if len(all_class_data_np) > 0:
            print(f"Sample class values: {all_class_data_np[:5]}")
            unique_classes = np.unique(all_class_data_np[~np.isnan(all_class_data_np)])
            print(f"Unique class values: {unique_classes}")


    if all_x_data_np.size == 0: # No data to plot
        print("ERROR: No data to plot - all arrays are empty")
        error_message = "No data to display for the selected criteria.\n\n"
        if wells_with_issues:
            error_message += "Issues detected:\n"
            for issue in wells_with_issues:
                error_message += f"• {issue}\n"

        ax_main.text(0.5, 0.5, error_message,
                    horizontalalignment='center',
                    verticalalignment='center',
                    transform=ax_main.transAxes,
                    wrap=True,
                    fontsize=12)
        plt.show()
        return

    # Main scatter plot
    if z_col and all_z_data_np is not None and len(all_z_data_np) > 0:
        # Z column continuous colormap plot
        scatter = ax_main.scatter(all_x_data_np, all_y_data_np,
                                 c=all_z_data_np,
                                 s=settings.get('point_size', 50),
                                 cmap=settings.get('colormap', 'viridis'),
                                 alpha=0.7)

        # Add enhanced colorbar if requested
        if settings.get('show_colorbar', True):
            # Get colorbar position and size
            colorbar_position = settings.get('colorbar_position', 'right')
            colorbar_size = settings.get('colorbar_size', 0.05)

            # Create colorbar with enhanced positioning
            if colorbar_position in ['right', 'left']:
                # Vertical colorbar
                cbar = plt.colorbar(scatter, ax=ax_main,
                                  location=colorbar_position,
                                  shrink=0.8,
                                  aspect=20,
                                  pad=0.02 if colorbar_position == 'right' else 0.1,
                                  fraction=colorbar_size)
            else:
                # Horizontal colorbar (top or bottom)
                cbar = plt.colorbar(scatter, ax=ax_main,
                                  location=colorbar_position,
                                  shrink=0.8,
                                  aspect=30,
                                  pad=0.1 if colorbar_position == 'top' else 0.05,
                                  fraction=colorbar_size,
                                  orientation='horizontal')

            # Set colorbar label with enhanced formatting
            cbar.set_label(settings.get('colorbar_label', z_col),
                          fontsize=settings.get('y_title_size', 12),
                          fontweight=settings.get('y_title_weight', 'normal'))

            # Format colorbar tick labels
            cbar.ax.tick_params(labelsize=settings.get('tick_label_size', 10))

            # Add colormap information to debug output
            print(f"Applied colormap: {settings.get('colormap', 'viridis')}")
            print(f"Colorbar position: {colorbar_position}")
            print(f"Colorbar size: {colorbar_size}")

    elif class_col and all_class_data_np is not None and settings.get('symbols') and settings['symbols']:
        # Class-based coloring
        unique_plot_classes = np.unique(all_class_data_np[~np.isnan(all_class_data_np)])

        for class_val in unique_plot_classes:
            class_mask = (all_class_data_np == class_val)
            ax_main.scatter(all_x_data_np[class_mask], all_y_data_np[class_mask],
                            s=settings.get('point_size', 50),
                            color=settings['colors'].get(class_val, '#000000'),
                            marker=settings['symbols'].get(class_val, 'o'),
                            label=settings['class_names'].get(class_val, f'Class {class_val}'),
                            alpha=0.7)

        if 'NaN' in settings.get('symbols', {}) and 'NaN' in settings.get('colors', {}): # Check if 'NaN' key exists
            nan_mask = np.isnan(all_class_data_np)
            if np.any(nan_mask):
                ax_main.scatter(all_x_data_np[nan_mask], all_y_data_np[nan_mask],
                                s=settings.get('point_size', 50),
                                color=settings['colors'].get('NaN', '#808080'),
                                marker=settings['symbols'].get('NaN', 'x'),
                                label=settings['class_names'].get('NaN', 'No Data'),
                                alpha=0.7)
    else:
        # Default plot if no class_col or z_col
        ax_main.scatter(all_x_data_np, all_y_data_np,
                        s=settings.get('point_size', 50),
                        color='blue', marker='o', alpha=0.7,
                        label=f'{x_col} vs {y_col}')

    # Set axis limits using the robust limits function
    user_x_min, user_x_max = settings.get('x_axis_min_user'), settings.get('x_axis_max_user')
    user_y_min, user_y_max = settings.get('y_axis_min_user'), settings.get('y_axis_max_user')

    # Use robust limits if user hasn't specified limits
    if all_x_data_np.size > 0 and (user_x_min is None or user_x_max is None):
        robust_x_min, robust_x_max = get_robust_limits(all_x_data_np)
        final_x_min = user_x_min if user_x_min is not None else robust_x_min
        final_x_max = user_x_max if user_x_max is not None else robust_x_max
    else:
        # Fallback to original method if no data or user specified both limits
        final_x_min = user_x_min if user_x_min is not None else (overall_x_min if overall_x_min != float('inf') else None)
        final_x_max = user_x_max if user_x_max is not None else (overall_x_max if overall_x_max != float('-inf') else None)

    if all_y_data_np.size > 0 and (user_y_min is None or user_y_max is None):
        robust_y_min, robust_y_max = get_robust_limits(all_y_data_np)
        final_y_min = user_y_min if user_y_min is not None else robust_y_min
        final_y_max = user_y_max if user_y_max is not None else robust_y_max
    else:
        # Fallback to original method if no data or user specified both limits
        final_y_min = user_y_min if user_y_min is not None else (overall_y_min if overall_y_min != float('inf') else None)
        final_y_max = user_y_max if user_y_max is not None else (overall_y_max if overall_y_max != float('-inf') else None)

    # Apply limits with validation
    if final_x_min is not None and final_x_max is not None:
        if final_x_min < final_x_max:
            ax_main.set_xlim(final_x_min, final_x_max)
        else:
            # Handle equal or inverted limits
            if final_x_min == final_x_max:  # Equal limits case
                padding = max(abs(final_x_min) * 0.05, 1.0)  # 5% padding or at least 1.0
                ax_main.set_xlim(final_x_min - padding, final_x_max + padding)
            else:  # Inverted limits case (shouldn't happen with robust limits)
                ax_main.set_xlim(min(final_x_min, final_x_max), max(final_x_min, final_x_max))

    if final_y_min is not None and final_y_max is not None:
        if final_y_min < final_y_max:
            ax_main.set_ylim(final_y_min, final_y_max)
        else:
            # Handle equal or inverted limits
            if final_y_min == final_y_max:  # Equal limits case
                padding = max(abs(final_y_min) * 0.05, 1.0)  # 5% padding or at least 1.0
                ax_main.set_ylim(final_y_min - padding, final_y_max + padding)
            else:  # Inverted limits case (shouldn't happen with robust limits)
                ax_main.set_ylim(min(final_y_min, final_y_max), max(final_y_min, final_y_max))


    # Marginal plots (Histogram/KDE) - only if ax_histx and ax_histy were created
    if ax_histx and ax_histy: # Implies class_col was selected
        print("Creating class-based marginal plots using StatisticalVisualizer")

        bin_size = settings.get('bin_size', 30)
        marginal_plot_type = settings.get('marginal_plot_type', 'Both')

        # Prepare data for the statistical visualizer
        # Use clean data (no NaNs) for marginal plots
        x_data_for_marginal = all_x_data_np[~np.isnan(all_x_data_np)]
        y_data_for_marginal = all_y_data_np[~np.isnan(all_y_data_np)]

        # Prepare class data if available
        class_data_for_marginal = None
        colors_for_marginal = {}
        class_names_for_marginal = {}

        if all_class_data_np is not None and len(all_class_data_np) > 0:
            # Filter class data to match the clean x/y data
            valid_mask = ~np.isnan(all_x_data_np) & ~np.isnan(all_y_data_np)
            class_data_for_marginal = all_class_data_np[valid_mask]

            # Extract colors and class names from settings if available
            if settings.get('colors'):
                colors_for_marginal = settings['colors']
            if settings.get('class_names'):
                class_names_for_marginal = settings['class_names']

            print(f"Using class-specific marginal plots with {len(np.unique(class_data_for_marginal[~np.isnan(class_data_for_marginal)]))} unique classes")
        else:
            print("Using aggregate marginal plots (no class data)")

        try:
            # Create the statistical visualizer with class-based functionality
            visualizer = StatisticalVisualizer(
                x_data=x_data_for_marginal,
                y_data=y_data_for_marginal,
                class_data=class_data_for_marginal,
                colors=colors_for_marginal,
                class_names=class_names_for_marginal
            )

            # Update histogram bins in the analyzers
            visualizer.x_histogram.bins = bin_size
            visualizer.y_histogram.bins = bin_size

            # Get axis ranges for KDE evaluation
            x_range = ax_main.get_xlim()
            y_range = ax_main.get_ylim()

            # Plot marginal distributions using the class-based approach
            visualizer.plot_marginal_distributions(
                ax_histx=ax_histx,
                ax_histy=ax_histy,
                plot_type=marginal_plot_type,
                x_range=x_range,
                y_range=y_range
            )

            print(f"Successfully created {marginal_plot_type} marginal plots")

        except Exception as e:
            print(f"Error creating class-based marginal plots: {e}")
            print("Falling back to simple aggregate plots")

            # Fallback to simple aggregate plots if class-based approach fails
            if marginal_plot_type in ["Histogram", "Both"] and len(x_data_for_marginal) > 0:
                try:
                    bins_x = min(bin_size, max(5, len(np.unique(x_data_for_marginal))))
                    ax_histx.hist(x_data_for_marginal, bins=bins_x, color='gray', alpha=0.7, density=True)
                except:
                    if len(np.unique(x_data_for_marginal)) == 1:
                        ax_histx.axvline(x=x_data_for_marginal[0], color='gray', alpha=0.7)

            if marginal_plot_type in ["Histogram", "Both"] and len(y_data_for_marginal) > 0:
                try:
                    bins_y = min(bin_size, max(5, len(np.unique(y_data_for_marginal))))
                    ax_histy.hist(y_data_for_marginal, bins=bins_y, orientation='horizontal',
                                 color='gray', alpha=0.7, density=True)
                except:
                    if len(np.unique(y_data_for_marginal)) == 1:
                        ax_histy.axhline(y=y_data_for_marginal[0], color='gray', alpha=0.7)

            if marginal_plot_type in ["KDE", "Both"]:
                if len(x_data_for_marginal) > 1 and np.var(x_data_for_marginal) > 1e-10:
                    try:
                        kde_x = stats.gaussian_kde(x_data_for_marginal)
                        x_range_kde = np.linspace(x_range[0], x_range[1], 100)
                        ax_histx.plot(x_range_kde, kde_x(x_range_kde), color='blue', linestyle='--')
                    except:
                        pass

                if len(y_data_for_marginal) > 1 and np.var(y_data_for_marginal) > 1e-10:
                    try:
                        kde_y = stats.gaussian_kde(y_data_for_marginal)
                        y_range_kde = np.linspace(y_range[0], y_range[1], 100)
                        ax_histy.plot(kde_y(y_range_kde), y_range_kde, color='blue', linestyle='--')
                    except:
                        pass

    # Axis titles and labels
    ax_main.set_xlabel(settings.get('x_title_text', x_col),
                       fontsize=settings.get('x_title_size', 12),
                       fontweight=settings.get('x_title_weight', 'normal'),
                       fontstyle=settings.get('x_title_style', 'normal'),
                       fontfamily=settings.get('x_title_family', 'Arial'))
    ax_main.set_ylabel(settings.get('y_title_text', y_col),
                       fontsize=settings.get('y_title_size', 12),
                       fontweight=settings.get('y_title_weight', 'normal'),
                       fontstyle=settings.get('y_title_style', 'normal'),
                       fontfamily=settings.get('y_title_family', 'Arial'))

    # Tick label customization
    ax_main.tick_params(axis='both', which='major',
                        labelsize=settings.get('tick_label_size', 10))
    for tick in ax_main.get_xticklabels():
        tick.set_fontweight(settings.get('tick_label_weight', 'normal'))
        tick.set_fontstyle(settings.get('tick_label_style', 'normal'))
    for tick in ax_main.get_yticklabels():
        tick.set_fontweight(settings.get('tick_label_weight', 'normal'))
        tick.set_fontstyle(settings.get('tick_label_style', 'normal'))

    # Legend
    handles, labels = ax_main.get_legend_handles_labels()
    if handles: # Only show legend if there are items to show
        legend_title = class_col if class_col and settings.get('class_names') and settings['class_names'] else None
        ax_main.legend(handles, labels,
                       fontsize=settings.get('legend_font_size', 10),
                       title=legend_title,
                       title_fontsize=str(int(settings.get('legend_font_size', 10)) + 1) if legend_title else None, # Make title slightly larger
                       loc='best') # Changed to 'best' for better auto-placement

    fig.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout to make space for suptitle and ensure labels fit

    # Create enhanced title with Z column and colormap info if applicable
    if z_col:
        colormap_info = settings.get('colormap', 'viridis')
        if settings.get('reverse_colormap', False):
            colormap_info += " (reversed)"
        title = f"Crossplot: {settings.get('x_title_text', x_col)} vs {settings.get('y_title_text', y_col)} (colored by {settings.get('colorbar_label', z_col)}, {colormap_info})"
    else:
        title = f"Crossplot: {settings.get('x_title_text', x_col)} vs {settings.get('y_title_text', y_col)}"

    fig.suptitle(title, fontsize=16, fontweight='bold')
    plt.show()

def interpolate_nan(depth, data):
    mask = ~np.isnan(data)
    if np.sum(mask) < 2:
        # Not enough data points to interpolate
        return data
    f = interpolate.interp1d(depth[mask], data[mask], kind='linear', fill_value='extrapolate')
    return f(depth)

def interpolate_class(depth, class_data):
    mask = ~np.isnan(class_data)
    if np.sum(mask) < 1:
        # No valid class data
        return class_data
    f = interpolate.interp1d(depth[mask], class_data[mask], kind='nearest', fill_value='extrapolate')
    return f(depth)

def validate_data_for_plotting(las_files, x_col, y_col, class_col, depth_ranges, z_col=None):
    """
    Validates data for plotting and returns a dictionary with validation results.

    Returns:
    {
        'valid': bool,  # Overall validity
        'issues': list,  # List of issue descriptions
        'warnings': list,  # List of warning descriptions
        'stats': {  # Statistics about the data
            'total_points': int,
            'valid_points': int,
            'x_unique_values': int,
            'y_unique_values': int,
            'class_unique_values': int or None,
            'wells_with_data': list,
            'wells_without_data': list
        }
    }
    """
    print("\n=== DEBUG: DATA VALIDATION ===")
    print(f"Validating data for plotting: x_col={x_col}, y_col={y_col}, class_col={class_col}, z_col={z_col}")
    print(f"Number of LAS files: {len(las_files)}")
    print(f"Depth ranges: {depth_ranges}")

    result = {
        'valid': True,
        'issues': [],
        'warnings': [],
        'stats': {
            'total_points': 0,
            'valid_points': 0,
            'x_unique_values': 0,
            'y_unique_values': 0,
            'class_unique_values': None,
            'z_unique_values': None,
            'wells_with_data': [],
            'wells_without_data': []
        }
    }

    # Minimum required data points for a valid plot - reduced from 5 to 1 for troubleshooting
    MIN_VALID_POINTS = 1  # Temporarily reduced to allow plots with minimal data
    # Minimum required unique values for meaningful visualization - reduced from 2 to 1 for troubleshooting
    MIN_UNIQUE_VALUES = 1  # Temporarily reduced to allow plots with single value datasets

    all_x_data = []
    all_y_data = []
    all_class_data = []
    all_z_data = []

    for las in las_files:
        well_name = las.well.WELL.value
        print(f"\nProcessing well: {well_name}")

        # Check if depth range is specified
        if well_name not in depth_ranges:
            print(f"  WARNING: Depth range not specified for well {well_name}. This well will be skipped.")
            result['warnings'].append(f"Depth range not specified for well {well_name}. This well will be skipped.")
            result['stats']['wells_without_data'].append(well_name)
            continue

        top_depth, bottom_depth = depth_ranges[well_name]
        print(f"  Depth range: {top_depth} to {bottom_depth}")

        # Check if required curves exist
        missing_curves = []
        if 'DEPTH' not in las.curves:
            missing_curves.append('DEPTH')
        if x_col not in las.curves:
            missing_curves.append(x_col)
        if y_col not in las.curves:
            missing_curves.append(y_col)
        if z_col and z_col not in las.curves:
            missing_curves.append(z_col)

        if missing_curves:
            print(f"  WARNING: Well {well_name} is missing required curves: {', '.join(missing_curves)}. This well will be skipped.")
            result['warnings'].append(f"Well {well_name} is missing required curves: {', '.join(missing_curves)}. This well will be skipped.")
            result['stats']['wells_without_data'].append(well_name)
            continue

        # Get data within depth range
        depth = np.array(las['DEPTH'].data)
        print(f"  Depth range in data: {np.min(depth)} to {np.max(depth)}")
        print(f"  Specified depth range: {top_depth} to {bottom_depth}")

        mask = (depth >= top_depth) & (depth <= bottom_depth)
        points_in_range = np.sum(mask)
        print(f"  Points in depth range: {points_in_range}/{len(depth)} ({points_in_range/len(depth)*100:.1f}%)")

        if not np.any(mask):
            print(f"  WARNING: No data points within specified depth range for well {well_name}.")
            print(f"  TROUBLESHOOTING: Attempting to use full depth range instead")

            # Try using the full depth range as a fallback for validation
            full_range_mask = np.ones_like(depth, dtype=bool)
            if np.any(full_range_mask):
                print(f"  Using full depth range as fallback for validation")
                mask = full_range_mask
                points_in_range = np.sum(mask)
                print(f"  Points in full range: {points_in_range}/{len(depth)}")
            else:
                result['warnings'].append(f"No data points within specified depth range for well {well_name}.")
                result['stats']['wells_without_data'].append(well_name)
                continue

        x_data = np.array(las[x_col].data)[mask]
        y_data = np.array(las[y_col].data)[mask]

        # Extract Z data if specified
        z_data = None
        if z_col and z_col in las.curves:
            z_data = np.array(las[z_col].data)[mask]
            z_nan_count = np.sum(np.isnan(z_data))
            print(f"  {z_col} NaN values: {z_nan_count}/{len(z_data)} ({z_nan_count/len(z_data)*100:.1f}%)")

        # Debug: Check for NaN values in x_data and y_data
        x_nan_count = np.sum(np.isnan(x_data))
        y_nan_count = np.sum(np.isnan(y_data))
        print(f"  {x_col} NaN values: {x_nan_count}/{len(x_data)} ({x_nan_count/len(x_data)*100:.1f}%)")
        print(f"  {y_col} NaN values: {y_nan_count}/{len(y_data)} ({y_nan_count/len(y_data)*100:.1f}%)")

        # Check for valid (non-NaN) data points
        valid_mask = ~np.isnan(x_data) & ~np.isnan(y_data)

        # If Z data exists, also filter by Z validity
        if z_data is not None:
            valid_mask = valid_mask & ~np.isnan(z_data)

        valid_x = x_data[valid_mask]
        valid_y = y_data[valid_mask]
        valid_z = z_data[valid_mask] if z_data is not None else None

        valid_points = len(valid_x)
        print(f"  Valid (non-NaN) points for both {x_col} and {y_col}: {valid_points}/{len(x_data)} ({valid_points/max(1,len(x_data))*100:.1f}%)")

        # Debug: Print some sample values
        print(f"  Sample {x_col} values before filtering: {x_data[:5]}")
        print(f"  Sample {y_col} values before filtering: {y_data[:5]}")

        if valid_points > 0:
            print(f"  Sample {x_col} values after filtering: {valid_x[:5]}")
            print(f"  Sample {y_col} values after filtering: {valid_y[:5]}")

        result['stats']['total_points'] += len(x_data)
        result['stats']['valid_points'] += len(valid_x)

        if len(valid_x) == 0:
            print(f"  WARNING: Well {well_name} has no valid data points (all NaN values).")
            print(f"  TROUBLESHOOTING: Attempting to use interpolation to fill NaN values")

            # Try to interpolate NaN values as a fallback for validation
            try:
                # Create a simple linear interpolation for NaN values
                x_indices = np.arange(len(x_data))
                y_indices = np.arange(len(y_data))

                # For X data
                x_valid_mask = ~np.isnan(x_data)
                if np.any(x_valid_mask):
                    x_valid_indices = x_indices[x_valid_mask]
                    x_valid_values = x_data[x_valid_mask]
                    if len(x_valid_indices) > 0:
                        # Use nearest neighbor interpolation for simplicity
                        valid_x = np.interp(x_indices, x_valid_indices, x_valid_values,
                                          left=np.nanmean(x_data) if not np.all(np.isnan(x_data)) else 0,
                                          right=np.nanmean(x_data) if not np.all(np.isnan(x_data)) else 0)
                        print(f"  Interpolated {x_col} values: {valid_x[:5]}")

                # For Y data
                y_valid_mask = ~np.isnan(y_data)
                if np.any(y_valid_mask):
                    y_valid_indices = y_indices[y_valid_mask]
                    y_valid_values = y_data[y_valid_mask]
                    if len(y_valid_indices) > 0:
                        # Use nearest neighbor interpolation for simplicity
                        valid_y = np.interp(y_indices, y_valid_indices, y_valid_values,
                                          left=np.nanmean(y_data) if not np.all(np.isnan(y_data)) else 0,
                                          right=np.nanmean(y_data) if not np.all(np.isnan(y_data)) else 0)
                        print(f"  Interpolated {y_col} values: {valid_y[:5]}")

                # Check if we now have valid data
                if len(valid_x) > 0 and len(valid_y) > 0:
                    print(f"  Successfully interpolated data: {len(valid_x)} points")
                    # Update stats with interpolated data
                    result['stats']['valid_points'] += len(valid_x)
                else:
                    result['warnings'].append(f"Well {well_name} has no valid data points (all NaN values).")
                    result['stats']['wells_without_data'].append(well_name)
                    continue
            except Exception as e:
                print(f"  ERROR during interpolation: {str(e)}")
                result['warnings'].append(f"Well {well_name} has no valid data points (all NaN values).")
                result['stats']['wells_without_data'].append(well_name)
                continue

        # Check class data if specified
        if class_col:
            if class_col in las.curves:
                class_data = np.array(las[class_col].data)[mask]
                class_nan_count = np.sum(np.isnan(class_data))
                print(f"  {class_col} NaN values: {class_nan_count}/{len(class_data)} ({class_nan_count/len(class_data)*100:.1f}%)")

                class_data_valid = class_data[valid_mask]
                if len(class_data_valid) > 0:
                    print(f"  Sample {class_col} values: {class_data_valid[:5]}")
                all_class_data.extend(class_data_valid)
            else:
                print(f"  WARNING: Class column '{class_col}' not found in well {well_name}.")
                result['warnings'].append(f"Class column '{class_col}' not found in well {well_name}.")

        # Add data to overall collections
        all_x_data.extend(valid_x)
        all_y_data.extend(valid_y)
        if valid_z is not None:
            all_z_data.extend(valid_z)
        result['stats']['wells_with_data'].append(well_name)
        print(f"  Added {len(valid_x)} valid data points from well {well_name}")

    # Convert to numpy arrays for analysis
    all_x_data = np.array(all_x_data)
    all_y_data = np.array(all_y_data)

    print(f"\nTotal data points collected: {len(all_x_data)}")

    # Check if we have enough data points overall
    if len(all_x_data) < MIN_VALID_POINTS:
        print(f"WARNING: Insufficient data points ({len(all_x_data)}) for plotting. At least {MIN_VALID_POINTS} valid points are required.")
        result['valid'] = False
        result['issues'].append(f"Insufficient data points ({len(all_x_data)}) for plotting. At least {MIN_VALID_POINTS} valid points are required.")

    # Check for unique values
    x_unique = np.unique(all_x_data)
    y_unique = np.unique(all_y_data)

    result['stats']['x_unique_values'] = len(x_unique)
    result['stats']['y_unique_values'] = len(y_unique)

    print(f"Unique {x_col} values: {len(x_unique)}")
    print(f"Unique {y_col} values: {len(y_unique)}")

    # Check Z data if provided
    if z_col and all_z_data:
        z_unique = np.unique(all_z_data)
        result['stats']['z_unique_values'] = len(z_unique)
        print(f"Unique {z_col} values: {len(z_unique)}")

        if len(z_unique) < MIN_UNIQUE_VALUES:
            print(f"WARNING: Z-axis column '{z_col}' has only {len(z_unique)} unique values, which may not produce a meaningful colormap.")
            result['warnings'].append(f"Z-axis column '{z_col}' has only {len(z_unique)} unique values, which may not produce a meaningful colormap.")

    if len(x_unique) < MIN_UNIQUE_VALUES:
        print(f"WARNING: X-axis column '{x_col}' has only {len(x_unique)} unique values, which may not produce a meaningful plot.")
        result['warnings'].append(f"X-axis column '{x_col}' has only {len(x_unique)} unique values, which may not produce a meaningful plot.")

    if len(y_unique) < MIN_UNIQUE_VALUES:
        print(f"WARNING: Y-axis column '{y_col}' has only {len(y_unique)} unique values, which may not produce a meaningful plot.")
        result['warnings'].append(f"Y-axis column '{y_col}' has only {len(y_unique)} unique values, which may not produce a meaningful plot.")

    # Check class data if provided
    if class_col and all_class_data:
        all_class_data = np.array(all_class_data)
        class_unique = np.unique(all_class_data[~np.isnan(all_class_data)])
        result['stats']['class_unique_values'] = len(class_unique)

        print(f"Unique {class_col} values: {len(class_unique)}")

        if len(class_unique) == 0:
            print(f"WARNING: Class column '{class_col}' has no valid values (all NaN).")
            result['warnings'].append(f"Class column '{class_col}' has no valid values (all NaN).")
        elif len(class_unique) == 1:
            print(f"WARNING: Class column '{class_col}' has only one unique value, which won't produce a meaningful classification in the plot.")
            result['warnings'].append(f"Class column '{class_col}' has only one unique value, which won't produce a meaningful classification in the plot.")

    # Check if any wells have data
    if not result['stats']['wells_with_data']:
        print("WARNING: No wells contain valid data for the selected columns and depth ranges.")
        result['valid'] = False
        result['issues'].append("No wells contain valid data for the selected columns and depth ranges.")

    print(f"Validation result: {'VALID' if result['valid'] else 'INVALID'}")
    print(f"Wells with data: {result['stats']['wells_with_data']}")
    print(f"Wells without data: {result['stats']['wells_without_data']}")
    print("=============================\n")

    return result

def show_data_statistics(validation_stats):
    """Display a dialog with statistics about the data being plotted."""
    stats_window = tk.Toplevel()
    stats_window.title("Data Statistics")
    stats_window.geometry("500x400")

    # Create a scrollable text widget
    text_frame = tk.Frame(stats_window)
    text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    text_widget = tk.Text(text_frame, wrap=tk.WORD)
    scrollbar = tk.Scrollbar(text_frame, command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)

    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # Add statistics to the text widget
    text_widget.insert(tk.END, "Data Statistics\n", "heading")
    text_widget.insert(tk.END, "=============\n\n")

    text_widget.insert(tk.END, f"Total data points: {validation_stats['total_points']}\n")
    text_widget.insert(tk.END, f"Valid data points: {validation_stats['valid_points']} ")
    text_widget.insert(tk.END, f"({validation_stats['valid_points']/max(1, validation_stats['total_points'])*100:.1f}%)\n\n")

    text_widget.insert(tk.END, f"X-axis unique values: {validation_stats['x_unique_values']}\n")
    text_widget.insert(tk.END, f"Y-axis unique values: {validation_stats['y_unique_values']}\n")

    if validation_stats['class_unique_values'] is not None:
        text_widget.insert(tk.END, f"Class unique values: {validation_stats['class_unique_values']}\n")
    else:
        text_widget.insert(tk.END, "No class column selected.\n")

    if validation_stats.get('z_unique_values') is not None:
        text_widget.insert(tk.END, f"Z-axis unique values: {validation_stats['z_unique_values']}\n\n")
    else:
        text_widget.insert(tk.END, "No Z column selected.\n\n")

    text_widget.insert(tk.END, "Wells with valid data:\n")
    for well in validation_stats['wells_with_data']:
        text_widget.insert(tk.END, f"• {well}\n")

    if validation_stats['wells_without_data']:
        text_widget.insert(tk.END, "\nWells without valid data:\n")
        for well in validation_stats['wells_without_data']:
            text_widget.insert(tk.END, f"• {well}\n")

    # Make the text widget read-only
    text_widget.configure(state=tk.DISABLED)

    # Add a tag for the heading
    text_widget.tag_configure("heading", font=("Arial", 12, "bold"))

    # Add a "Close" button
    tk.Button(stats_window, text="Close", command=stats_window.destroy).pack(pady=10)

def get_robust_limits(data_array, padding_percent=5):
    """Calculate robust axis limits with padding, handling edge cases."""
    if len(data_array) == 0:
        return None, None

    if len(np.unique(data_array)) == 1:
        # Single unique value - create a small range around it
        single_value = data_array[0]
        # Use 1% of the value or 1.0 if value is zero or very small
        delta = max(abs(single_value) * 0.01, 1.0)
        return single_value - delta, single_value + delta

    data_min, data_max = np.min(data_array), np.max(data_array)
    data_range = data_max - data_min

    # Add padding
    padding = data_range * (padding_percent / 100)
    # Ensure minimum padding for small ranges
    min_padding = max(abs(data_min) * 0.01, abs(data_max) * 0.01, 0.1)
    padding = max(padding, min_padding)

    return data_min - padding, data_max + padding

def show_post_plot_options():
    """
    Show a dialog after the plot is displayed with options to exit or start new analysis.
    Returns: 'restart' if user wants to start new analysis, 'exit' if user wants to exit
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main window

    dialog = tk.Toplevel()
    dialog.title("Analysis Complete")
    dialog.geometry("450x250")
    dialog.resizable(False, False)

    # Make dialog modal
    dialog.transient()
    dialog.grab_set()

    # Center the dialog
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
    y = (dialog.winfo_screenheight() // 2) - (250 // 2)
    dialog.geometry(f"450x250+{x}+{y}")

    # Result variable
    result = {'choice': 'exit'}

    # Create UI elements
    frame = ttk.Frame(dialog, padding="20")
    frame.pack(fill=tk.BOTH, expand=True)

    # Message
    message = ttk.Label(frame,
                       text="Crossplot analysis complete!\n\nWhat would you like to do next?",
                       font=("Arial", 12),
                       justify=tk.CENTER)
    message.pack(pady=(0, 20))

    # Description
    description = ttk.Label(frame,
                           text="• Start New Analysis: Load new LAS files and begin fresh analysis\n• Exit Application: Close the application",
                           font=("Arial", 10),
                           justify=tk.LEFT)
    description.pack(pady=(0, 20))

    # Buttons frame
    button_frame = ttk.Frame(frame)
    button_frame.pack(pady=10)

    def on_restart():
        result['choice'] = 'restart'
        dialog.destroy()
        root.destroy()

    def on_exit():
        result['choice'] = 'exit'
        dialog.destroy()
        root.destroy()

    # Buttons
    restart_btn = ttk.Button(button_frame, text="Start New Analysis", command=on_restart)
    restart_btn.pack(side=tk.LEFT, padx=(0, 15))

    exit_btn = ttk.Button(button_frame, text="Exit Application", command=on_exit)
    exit_btn.pack(side=tk.LEFT)

    # Handle window close
    dialog.protocol("WM_DELETE_WINDOW", on_exit)

    # Set focus to the dialog
    dialog.focus_set()

    # Wait for dialog to close
    dialog.wait_window()

    return result['choice']

def main():
    print("\n=== DEBUG: MAIN FUNCTION EXECUTION ===")
    print("Starting application...")

    while True:  # Main loop for restart functionality
        try:
            # Clear any existing matplotlib figures
            plt.close('all')

            las_files = load_multiple_las_files()
            if not las_files:
                print("No files selected. Exiting.")
                return

            print(f"Successfully loaded {len(las_files)} LAS files")
            for i, las in enumerate(las_files):
                well_name = las.well.WELL.value
                print(f"  LAS file {i+1}: {well_name}")
                print(f"    Depth range: {min(las['DEPTH'].data)} to {max(las['DEPTH'].data)}")
                print(f"    Number of curves: {len(las.curves)}")

            # Get calculations from user
            print("\nPrompting user for calculations...")
            success = get_calculations(las_files)
            if not success:
                print("Calculations aborted. Exiting.")
                return
            print("Calculations completed successfully")

            # Prompt user to load Excel file with depth ranges upfront
            # Pass the LAS files to filter the Excel data to only include matching wells
            # Assuming log_keywords is defined globally or accessible
            # TODO: Ensure log_keywords is properly defined and passed if needed by load_excel_depth_ranges
            preloaded_excel_df = load_excel_depth_ranges(las_files)
            if preloaded_excel_df is not None:
                print(f"Excel file with depth ranges loaded successfully.")
                print(f"Found {len(preloaded_excel_df)} boundary entries for {preloaded_excel_df['Well'].nunique()} wells.")
                print("This data will be automatically used in the depth ranges dialog.")


            print("\nPrompting user for column selection and depth ranges...")
            # Pass preloaded_excel_df to get_column_names_and_depths
            result = get_column_names_and_depths(las_files, preloaded_excel_df=preloaded_excel_df)
            if result is None:
                # Error messages are handled within get_column_names_and_depths or get_depth_ranges
                print("Column and depth selection was cancelled or failed. Exiting.")
                return
            print("Column selection and depth ranges completed")

            x_col = result["x_col"]
            y_col = result["y_col"]
            class_col = result["class_col"]
            z_col = result["z_col"]
            depth_ranges = result["depth_ranges"]

            print(f"\nSelected columns: x_col={x_col}, y_col={y_col}, class_col={class_col}, z_col={z_col}")
            print(f"Selected depth ranges: {depth_ranges}")

            # Check if validation_stats is available from the enhanced get_column_names_and_depths function
            validation_stats = result.get("validation_stats")
            if validation_stats:
                print("Validation statistics available from column selection")
            else:
                print("No validation statistics available from column selection")

            # Check required information (class_col is optional)
            if not all([x_col, y_col, depth_ranges]):
                print("Missing required information. Exiting.")
                return

            # Perform comprehensive validation if not already done
            if validation_stats is None:
                validation_result = validate_data_for_plotting(las_files, x_col, y_col, class_col, depth_ranges, z_col)

                # If data is invalid, show error message and ask if user wants to proceed
                if not validation_result['valid']:
                    error_message = "The following issues were detected with the selected data:\n\n"
                    for issue in validation_result['issues']:
                        error_message += f"• {issue}\n"

                    error_message += "\nThe plot may not display correctly. Do you want to proceed anyway?"
                    proceed = messagebox.askyesno("Data Validation Warning", error_message)

                    if not proceed:
                        print("Plot creation aborted due to validation issues.")
                        return

                # If there are warnings but data is valid, show them to the user
                elif validation_result['warnings']:
                    warning_message = "The following issues were detected with the selected data:\n\n"
                    for warning in validation_result['warnings']:
                        warning_message += f"• {warning}\n"

                    warning_message += "\nThe plot may not display optimally. Do you want to proceed?"
                    proceed = messagebox.askyesno("Data Validation Warning", warning_message)

                    if not proceed:
                        print("Plot creation aborted due to validation warnings.")
                        return

                validation_stats = validation_result['stats']

            settings = get_plot_settings(las_files, x_col, y_col, class_col, depth_ranges, z_col)
            if settings is None:
                print("Plot settings not provided. Exiting.")
                return

            # Print some information about the data
            print("\nSelected columns:")
            print(f"X-axis: {x_col}")
            print(f"Y-axis: {y_col}")
            print(f"Class: {class_col}")
            print("\nDepth ranges:")
            for well, (top, bottom) in depth_ranges.items():
                print(f"{well}: {top} - {bottom}")

            # Print validation statistics
            print("\nData statistics:")
            print(f"Total data points: {validation_stats['total_points']}")
            print(f"Valid data points: {validation_stats['valid_points']} " +
                  f"({validation_stats['valid_points']/max(1, validation_stats['total_points'])*100:.1f}%)")
            print(f"X-axis unique values: {validation_stats['x_unique_values']}")
            print(f"Y-axis unique values: {validation_stats['y_unique_values']}")
            if validation_stats['class_unique_values'] is not None:
                print(f"Class unique values: {validation_stats['class_unique_values']}")
            if validation_stats.get('z_unique_values') is not None:
                print(f"Z-axis unique values: {validation_stats['z_unique_values']}")

            print("\nWells with valid data:")
            for well in validation_stats['wells_with_data']:
                print(f"• {well}")

            if validation_stats['wells_without_data']:
                print("\nWells without valid data:")
                for well in validation_stats['wells_without_data']:
                    print(f"• {well}")

            # Create the plot
            create_plot(las_files, x_col, y_col, class_col, depth_ranges, settings, z_col)

            # After the plot is displayed, show options to exit or restart
            print("\nDisplaying post-plot options...")
            choice = show_post_plot_options()

            if choice == 'exit':
                print("User chose to exit. Closing application.")
                break
            elif choice == 'restart':
                print("User chose to restart. Starting new analysis...")
                continue
            else:
                # Default to exit if unexpected choice
                print("Unexpected choice. Exiting application.")
                break

        except Exception as e:
            print(f"An error occurred during analysis: {str(e)}")
            # Show options even if there's an error
            try:
                choice = show_post_plot_options()
                if choice == 'exit':
                    print("User chose to exit after error. Closing application.")
                    break
                elif choice == 'restart':
                    print("User chose to restart after error. Starting new analysis...")
                    continue
                else:
                    print("Exiting due to error.")
                    break
            except Exception as dialog_error:
                print(f"Error showing post-plot dialog: {str(dialog_error)}")
                break

if __name__ == "__main__":
    main()
