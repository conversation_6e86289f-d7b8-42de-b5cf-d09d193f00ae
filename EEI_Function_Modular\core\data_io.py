# -*- coding: utf-8 -*-
"""
Data I/O Module

This module provides unified input/output operations for loading LAS files,
Excel boundaries, and other data formats. It consolidates functionality from
both EEI_XCorr_Modular and EEI_Xplot_Modular.

Author: Integration of EEI_XCorr_Modular and EEI_Xplot_Modular
Created: 2025
Version: 1.0.0
"""

from __future__ import annotations

from typing import List, Optional, Dict, Tuple
import os
import logging

import lasio
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox

from ..config import BaseConfig

logger = logging.getLogger(__name__)

# ============================================================================
# LAS FILE OPERATIONS
# ============================================================================

def load_las_files(file_paths: Optional[List[str]] = None) -> Optional[List[lasio.LASFile]]:
    """
    Load multiple LAS files from specified paths or via file dialog.
    
    Args:
        file_paths: Optional list of file paths. If None, opens file dialog.
        
    Returns:
        List of LAS file objects or None if no files loaded
    """
    # If no paths provided, open file dialog
    if file_paths is None:
        root = tk.Tk()
        root.withdraw()
        
        file_paths = filedialog.askopenfilenames(
            title="Select LAS files",
            filetypes=[("LAS files", "*.las"), ("All files", "*.*")],
        )
        
        if not file_paths:
            logger.info("No files selected by user.")
            return None
    
    # Load each file
    las_files: List[lasio.LASFile] = []
    errors = []
    
    for path in file_paths:
        try:
            las = lasio.read(path)
            # Store the file path for reference
            las.file_path = path
            las_files.append(las)
            logger.info(f"Loaded file: {os.path.basename(path)}")
        except Exception as exc:
            error_msg = f"Error loading {os.path.basename(path)}: {str(exc)}"
            logger.error(error_msg)
            errors.append(error_msg)
    
    # Show errors if any
    if errors:
        error_text = "\n".join(errors)
        messagebox.showerror(
            "LAS Load Errors",
            f"Failed to load {len(errors)} file(s):\n\n{error_text}"
        )
    
    # Check if any files were loaded
    if not las_files:
        logger.error("No LAS files were successfully loaded.")
        messagebox.showerror(
            "No Files Loaded",
            "No LAS files could be loaded. Please check the files and try again."
        )
        return None
    
    logger.info(f"Successfully loaded {len(las_files)} LAS file(s)")
    return las_files


def find_default_columns(
    las: lasio.LASFile,
    keywords: Optional[Dict[str, List[str]]] = None
) -> Dict[str, Optional[str]]:
    """
    Find default columns for specific logs based on keyword mappings.
    
    Args:
        las: LAS file object
        keywords: Optional keyword dictionary. If None, uses BaseConfig.
        
    Returns:
        Dict mapping standard log names to actual curve names in LAS file
    """
    if keywords is None:
        keywords = BaseConfig.get_log_keywords()
    
    default_columns = {}
    
    for keyword, aliases in keywords.items():
        found = False
        for alias in aliases:
            for curve in las.curves:
                # Case-insensitive comparison
                if alias.upper() == curve.mnemonic.upper():
                    default_columns[keyword] = curve.mnemonic
                    found = True
                    logger.debug(f"Found {keyword} as {curve.mnemonic}")
                    break
            if found:
                break
        
        if not found:
            default_columns[keyword] = None
            logger.debug(f"Log type {keyword} not found in LAS file")
    
    return default_columns


def get_well_name(las: lasio.LASFile) -> str:
    """
    Extract well name from LAS file.
    
    Args:
        las: LAS file object
        
    Returns:
        Well name string
    """
    try:
        return las.well.WELL.value
    except AttributeError:
        # Fallback to filename if well name not in header
        if hasattr(las, 'file_path'):
            return os.path.splitext(os.path.basename(las.file_path))[0]
        return "Unknown"


# ============================================================================
# EXCEL FILE OPERATIONS
# ============================================================================

def load_excel_boundaries(
    file_path: Optional[str] = None,
    title: str = "Select Excel file with boundary information"
) -> Optional[pd.DataFrame]:
    """
    Load boundary information from an Excel file.
    
    Args:
        file_path: Optional path to Excel file. If None, opens file dialog.
        title: Title for file dialog
        
    Returns:
        DataFrame with columns ['Well', 'Surface', 'MD'] or None
    """
    # If no path provided, open file dialog
    if file_path is None:
        root = tk.Tk()
        root.withdraw()
        
        file_path = filedialog.askopenfilename(
            title=title,
            filetypes=[("Excel files", "*.xls;*.xlsx"), ("All files", "*.*")]
        )
        
        if not file_path:
            logger.info("No Excel file selected for boundaries.")
            return None
    
    try:
        # Load the Excel file
        df = pd.read_excel(file_path)
        
        # Check for required columns
        required_columns = ['Well', 'Surface', 'MD']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            error_msg = BaseConfig.get_error_message(
                'missing_columns',
                columns=', '.join(missing_columns)
            )
            messagebox.showerror("Missing Columns", error_msg)
            logger.error(error_msg)
            return None
        
        # Validate data
        if df.empty:
            error_msg = BaseConfig.get_error_message('empty_file')
            messagebox.showerror("Empty File", error_msg)
            logger.error(error_msg)
            return None
        
        # Log success
        success_msg = BaseConfig.get_success_message(
            'boundaries_loaded',
            count=len(df),
            wells=df['Well'].nunique()
        )
        logger.info(success_msg)
        
        return df
        
    except Exception as e:
        error_msg = BaseConfig.get_error_message(
            'file_load_error',
            filename=os.path.basename(file_path),
            error=str(e)
        )
        messagebox.showerror("Error Loading File", error_msg)
        logger.error(f"Error loading Excel file: {str(e)}")
        return None


def filter_excel_for_wells(
    df: pd.DataFrame,
    las_files: List[lasio.LASFile]
) -> Optional[pd.DataFrame]:
    """
    Filter Excel data to only include boundaries for loaded wells.
    
    Args:
        df: DataFrame with boundary data
        las_files: List of LAS file objects
        
    Returns:
        Filtered DataFrame or None if no matches
    """
    if df is None:
        return None
    
    # Get well names from LAS files
    las_well_names = [get_well_name(las) for las in las_files]
    
    # Filter DataFrame
    filtered_df = df[df['Well'].isin(las_well_names)]
    
    # Check for matches
    if filtered_df.empty:
        excel_wells = ', '.join(df['Well'].unique())
        las_wells = ', '.join(las_well_names)
        logger.warning(
            f"No matching wells found. Excel wells: {excel_wells}. "
            f"LAS wells: {las_wells}"
        )
        messagebox.showwarning(
            "No Matching Wells",
            "No wells in the Excel boundary file match the loaded LAS files."
        )
        return None
    
    # Log filtering results
    original_count = df['Well'].nunique()
    filtered_count = filtered_df['Well'].nunique()
    logger.info(
        f"Filtered Excel data from {original_count} wells to "
        f"{filtered_count} wells matching loaded LAS files"
    )
    logger.info(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")
    
    return filtered_df


def load_excel_depth_ranges(
    las_files: List[lasio.LASFile],
    prompt: bool = True
) -> Optional[pd.DataFrame]:
    """
    Load depth ranges from Excel file with optional user prompt.
    
    Args:
        las_files: List of LAS file objects
        prompt: Whether to prompt user before loading
        
    Returns:
        Filtered DataFrame with depth ranges or None
    """
    if prompt:
        root = tk.Tk()
        root.withdraw()
        
        load_excel = messagebox.askyesno(
            "Load Depth Ranges Excel",
            "Would you like to load an Excel file containing depth ranges?\n\n"
            "The file should have columns named 'Well', 'Surface', and 'MD'."
        )
        
        if not load_excel:
            logger.info("User chose not to load Excel file with depth ranges.")
            return None
    
    # Load the Excel file
    df = load_excel_boundaries(title="Select Excel file with depth ranges")
    
    if df is None:
        return None
    
    # Filter for loaded wells
    return filter_excel_for_wells(df, las_files)


# ============================================================================
# DATA EXPORT OPERATIONS
# ============================================================================

def save_results_to_excel(
    data: pd.DataFrame,
    file_path: Optional[str] = None,
    sheet_name: str = 'Results'
) -> bool:
    """
    Save results to Excel file.
    
    Args:
        data: DataFrame to save
        file_path: Optional path. If None, opens save dialog.
        sheet_name: Name of Excel sheet
        
    Returns:
        True if successful, False otherwise
    """
    # If no path provided, open save dialog
    if file_path is None:
        root = tk.Tk()
        root.withdraw()
        
        file_path = filedialog.asksaveasfilename(
            title="Save Results",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        
        if not file_path:
            logger.info("Save cancelled by user.")
            return False
    
    try:
        data.to_excel(file_path, sheet_name=sheet_name, index=False)
        
        success_msg = BaseConfig.get_success_message(
            'export_complete',
            filename=os.path.basename(file_path)
        )
        logger.info(success_msg)
        messagebox.showinfo("Export Successful", success_msg)
        return True
        
    except Exception as e:
        error_msg = BaseConfig.get_error_message(
            'file_load_error',
            filename=os.path.basename(file_path),
            error=str(e)
        )
        messagebox.showerror("Export Error", error_msg)
        logger.error(f"Error saving to Excel: {str(e)}")
        return False


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_depth_range(las: lasio.LASFile) -> Tuple[float, float]:
    """
    Get depth range from LAS file.
    
    Args:
        las: LAS file object
        
    Returns:
        Tuple of (min_depth, max_depth)
    """
    try:
        depth_curve = las.curves[0].data  # Typically first curve is depth
        return float(depth_curve.min()), float(depth_curve.max())
    except Exception as e:
        logger.warning(f"Could not determine depth range: {str(e)}")
        return 0.0, 0.0


def get_available_curves(las: lasio.LASFile) -> List[str]:
    """
    Get list of available curve names in LAS file.
    
    Args:
        las: LAS file object
        
    Returns:
        List of curve names
    """
    return [curve.mnemonic for curve in las.curves]


def analyze_log_availability(las_files: List[lasio.LASFile]) -> Dict[str, Dict[str, bool]]:
    """
    Analyze which logs are available in each well.
    
    Args:
        las_files: List of LAS file objects
        
    Returns:
        Dict mapping well names to dict of log availability
    """
    availability = {}
    keywords = BaseConfig.get_log_keywords()
    
    for las in las_files:
        well_name = get_well_name(las)
        default_cols = find_default_columns(las, keywords)
        
        availability[well_name] = {
            log_type: (col is not None)
            for log_type, col in default_cols.items()
        }
    
    return availability
