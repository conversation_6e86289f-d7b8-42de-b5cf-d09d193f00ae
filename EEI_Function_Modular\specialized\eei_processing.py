# -*- coding: utf-8 -*-
"""
EEI-Specific Processing Module

This module contains specialized processing functions for Extended Elastic Impedance (EEI) analysis.
It extracts EEI-specific functionality from the original XCorr module and enhances it with
improved error handling and integration with the unified framework.

Functions include:
- EEI data preparation
- Multi-well EEI analysis
- EEI correlation optimization
- EEI-specific validation

Author: EEI Function Modular Integration
Created: 2025-10-08
Version: 1.0.0
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass

# Import from core modules
from ..core.statistical_processing import StatisticalProcessor
from ..core.data_processing import DataProcessor

# Configure module-specific logger
logger = logging.getLogger(__name__)


@dataclass
class EEIAnalysisResult:
    """
    Container for EEI analysis results.
    
    Attributes:
        success: Whether the analysis was successful
        eei_data: Calculated EEI values
        correlations: Correlation results with target log
        chi_angles: Chi angles used in analysis
        optimization_results: Results from optimization (if performed)
        metadata: Analysis metadata and statistics
        warnings: List of warning messages
        errors: List of error messages
    """
    success: bool
    eei_data: Optional[np.ndarray]
    correlations: Optional[np.ndarray]
    chi_angles: Optional[np.ndarray]
    optimization_results: Optional[Dict[str, Any]]
    metadata: Dict[str, Any]
    warnings: List[str]
    errors: List[str]


class EEIProcessor:
    """
    Specialized processing operations for EEI analysis.
    
    This class provides methods specifically designed for Extended Elastic Impedance
    calculations and analysis workflows.
    """

    @staticmethod
    def prepare_eei_data(
        wells_data: Dict[str, Dict[str, np.ndarray]],
        required_logs: List[str] = None,
        depth_ranges: Optional[Dict[str, Tuple[float, float]]] = None
    ) -> Dict[str, Any]:
        """
        Prepare well data specifically for EEI analysis.
        
        Args:
            wells_data: Dictionary of well data
            required_logs: List of required log types for EEI analysis
            depth_ranges: Optional depth ranges for each well
            
        Returns:
            Dictionary containing prepared data and validation results
        """
        if required_logs is None:
            required_logs = ['DT', 'DTS', 'RHOB']  # Default EEI requirements
        
        warnings = []
        errors = []
        prepared_data = {}
        
        try:
            # Validate each well for EEI requirements
            for well_name, well_data in wells_data.items():
                well_status = {
                    'valid': True,
                    'missing_logs': [],
                    'available_logs': [],
                    'data_quality': {}
                }
                
                # Check for required logs
                for log_type in required_logs:
                    if log_type in well_data:
                        well_status['available_logs'].append(log_type)
                        
                        # Check data quality
                        log_data = well_data[log_type]
                        finite_mask = np.isfinite(log_data)
                        finite_percentage = np.sum(finite_mask) / len(log_data) * 100
                        
                        well_status['data_quality'][log_type] = {
                            'finite_percentage': finite_percentage,
                            'total_points': len(log_data),
                            'finite_points': np.sum(finite_mask)
                        }
                        
                        if finite_percentage < 50:
                            warnings.append(
                                f"Well {well_name}: {log_type} has only {finite_percentage:.1f}% finite values"
                            )
                    else:
                        well_status['missing_logs'].append(log_type)
                        well_status['valid'] = False
                
                # Apply depth ranges if specified
                if depth_ranges and well_name in depth_ranges:
                    top_depth, bottom_depth = depth_ranges[well_name]
                    if 'depth' in well_data:
                        depth = well_data['depth']
                        depth_mask = (depth >= top_depth) & (depth <= bottom_depth)
                        points_in_range = np.sum(depth_mask)
                        
                        well_status['depth_range'] = {
                            'top': top_depth,
                            'bottom': bottom_depth,
                            'points_in_range': points_in_range,
                            'total_points': len(depth)
                        }
                        
                        if points_in_range == 0:
                            well_status['valid'] = False
                            errors.append(f"Well {well_name}: No data in specified depth range")
                
                prepared_data[well_name] = well_status
                
                if not well_status['valid']:
                    if well_status['missing_logs']:
                        errors.append(
                            f"Well {well_name}: Missing required logs: {', '.join(well_status['missing_logs'])}"
                        )
            
            # Summary statistics
            valid_wells = [name for name, status in prepared_data.items() if status['valid']]
            invalid_wells = [name for name, status in prepared_data.items() if not status['valid']]
            
            summary = {
                'total_wells': len(wells_data),
                'valid_wells': len(valid_wells),
                'invalid_wells': len(invalid_wells),
                'valid_well_names': valid_wells,
                'invalid_well_names': invalid_wells,
                'required_logs': required_logs
            }
            
            return {
                'success': len(valid_wells) > 0,
                'well_status': prepared_data,
                'summary': summary,
                'warnings': warnings,
                'errors': errors
            }
            
        except Exception as e:
            logger.error(f"EEI data preparation failed: {str(e)}")
            return {
                'success': False,
                'well_status': {},
                'summary': {},
                'warnings': warnings,
                'errors': errors + [f"Preparation failed: {str(e)}"]
            }

    @staticmethod
    def merge_eei_well_data(
        wells_data: Dict[str, Dict[str, np.ndarray]],
        target_log: str,
        depth_ranges: Optional[Dict[str, Tuple[float, float]]] = None
    ) -> EEIAnalysisResult:
        """
        Merge well data specifically for EEI analysis.
        
        This is a specialized version of the general merge function that focuses on
        EEI requirements and provides EEI-specific validation.
        
        Args:
            wells_data: Dictionary of well data
            target_log: Target log for EEI correlation analysis
            depth_ranges: Optional depth ranges for each well
            
        Returns:
            EEIAnalysisResult with merged data and analysis metadata
        """
        warnings = []
        errors = []
        metadata = {
            'target_log': target_log,
            'analysis_type': 'EEI_merge',
            'wells_processed': 0,
            'total_points': 0
        }
        
        try:
            # Prepare data for EEI analysis
            preparation_result = EEIProcessor.prepare_eei_data(wells_data, depth_ranges=depth_ranges)
            
            if not preparation_result['success']:
                return EEIAnalysisResult(
                    success=False,
                    eei_data=None,
                    correlations=None,
                    chi_angles=None,
                    optimization_results=None,
                    metadata=metadata,
                    warnings=warnings + preparation_result['warnings'],
                    errors=errors + preparation_result['errors']
                )
            
            # Filter to valid wells only
            valid_wells = preparation_result['summary']['valid_well_names']
            valid_wells_data = {name: wells_data[name] for name in valid_wells}
            
            # Check if target log is available
            target_available_wells = []
            for well_name in valid_wells:
                if target_log in wells_data[well_name]:
                    target_available_wells.append(well_name)
                else:
                    warnings.append(f"Target log '{target_log}' not available in well {well_name}")
            
            if not target_available_wells:
                errors.append(f"Target log '{target_log}' not available in any valid well")
                return EEIAnalysisResult(
                    success=False,
                    eei_data=None,
                    correlations=None,
                    chi_angles=None,
                    optimization_results=None,
                    metadata=metadata,
                    warnings=warnings,
                    errors=errors
                )
            
            # Use core data processor for actual merging
            merged_data = DataProcessor.merge_well_data(valid_wells_data, depth_ranges)
            
            # Validate that required logs are present in merged data
            required_for_eei = ['DT', 'DTS', 'RHOB']
            missing_in_merged = [log for log in required_for_eei if log not in merged_data.data_arrays]
            
            if missing_in_merged:
                errors.append(f"Required EEI logs missing after merge: {', '.join(missing_in_merged)}")
                return EEIAnalysisResult(
                    success=False,
                    eei_data=None,
                    correlations=None,
                    chi_angles=None,
                    optimization_results=None,
                    metadata=metadata,
                    warnings=warnings,
                    errors=errors
                )
            
            # Validate target log in merged data
            if target_log not in merged_data.data_arrays:
                errors.append(f"Target log '{target_log}' missing after merge")
                return EEIAnalysisResult(
                    success=False,
                    eei_data=None,
                    correlations=None,
                    chi_angles=None,
                    optimization_results=None,
                    metadata=metadata,
                    warnings=warnings,
                    errors=errors
                )
            
            # Update metadata with merge results
            metadata.update({
                'wells_processed': merged_data.metadata['wells_processed'],
                'total_points': merged_data.metadata['total_points'],
                'available_logs': list(merged_data.data_arrays.keys()),
                'target_log_available': True,
                'merge_metadata': merged_data.metadata
            })
            
            return EEIAnalysisResult(
                success=True,
                eei_data=merged_data,  # Store the full MergedData object
                correlations=None,  # Will be calculated in subsequent steps
                chi_angles=None,    # Will be calculated in subsequent steps
                optimization_results=None,
                metadata=metadata,
                warnings=warnings + preparation_result['warnings'],
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"EEI data merge failed: {str(e)}")
            return EEIAnalysisResult(
                success=False,
                eei_data=None,
                correlations=None,
                chi_angles=None,
                optimization_results=None,
                metadata=metadata,
                warnings=warnings,
                errors=errors + [f"Merge failed: {str(e)}"]
            )

    @staticmethod
    def validate_eei_inputs(
        dt_data: np.ndarray,
        dts_data: np.ndarray,
        rhob_data: np.ndarray,
        target_data: np.ndarray,
        min_valid_percentage: float = 0.5
    ) -> Dict[str, Any]:
        """
        Validate input data for EEI calculations.

        Args:
            dt_data: P-wave slowness data
            dts_data: S-wave slowness data
            rhob_data: Bulk density data
            target_data: Target log data
            min_valid_percentage: Minimum percentage of valid data required

        Returns:
            Dictionary with validation results
        """
        warnings = []

        try:
            # Use statistical processor for validation
            arrays = [dt_data, dts_data, rhob_data, target_data]
            array_names = ['DT', 'DTS', 'RHOB', 'TARGET']

            validation_result = StatisticalProcessor.validate_array_compatibility(
                arrays, array_names, min_valid_percentage
            )

            # Additional EEI-specific checks
            eei_specific_warnings = []
            eei_specific_errors = []

            # Check for physically reasonable values
            if validation_result.value:  # Only check if basic validation passed
                # DT should be positive and typically 40-200 us/ft
                dt_finite = dt_data[np.isfinite(dt_data)]
                if len(dt_finite) > 0:
                    if np.any(dt_finite <= 0):
                        eei_specific_warnings.append("DT contains non-positive values")
                    if np.any(dt_finite < 40) or np.any(dt_finite > 300):
                        eei_specific_warnings.append("DT values outside typical range (40-300 us/ft)")

                # DTS should be positive and typically 80-400 us/ft
                dts_finite = dts_data[np.isfinite(dts_data)]
                if len(dts_finite) > 0:
                    if np.any(dts_finite <= 0):
                        eei_specific_warnings.append("DTS contains non-positive values")
                    if np.any(dts_finite < 80) or np.any(dts_finite > 500):
                        eei_specific_warnings.append("DTS values outside typical range (80-500 us/ft)")

                # RHOB should be positive and typically 1.5-3.0 g/cc
                rhob_finite = rhob_data[np.isfinite(rhob_data)]
                if len(rhob_finite) > 0:
                    if np.any(rhob_finite <= 0):
                        eei_specific_warnings.append("RHOB contains non-positive values")
                    if np.any(rhob_finite < 1.0) or np.any(rhob_finite > 4.0):
                        eei_specific_warnings.append("RHOB values outside typical range (1.0-4.0 g/cc)")

                # Check DT/DTS ratio (should be reasonable)
                if len(dt_finite) > 0 and len(dts_finite) > 0:
                    # Create common mask for ratio calculation
                    common_mask = np.isfinite(dt_data) & np.isfinite(dts_data)
                    if np.sum(common_mask) > 0:
                        dt_common = dt_data[common_mask]
                        dts_common = dts_data[common_mask]
                        ratio = dts_common / dt_common

                        if np.any(ratio < 1.5) or np.any(ratio > 4.0):
                            eei_specific_warnings.append("DTS/DT ratio outside typical range (1.5-4.0)")

            return {
                'valid': validation_result.value,
                'errors': validation_result.errors + eei_specific_errors,
                'warnings': validation_result.warnings + eei_specific_warnings,
                'statistics': validation_result.statistics,
                'metadata': validation_result.metadata
            }

        except Exception as e:
            logger.error(f"EEI input validation failed: {str(e)}")
            return {
                'valid': False,
                'errors': [f"Validation failed: {str(e)}"],
                'warnings': warnings,
                'statistics': {},
                'metadata': {}
            }

    @staticmethod
    def calculate_eei_correlation_analysis(
        dt_data: np.ndarray,
        dts_data: np.ndarray,
        rhob_data: np.ndarray,
        target_data: np.ndarray,
        chi_angles: Optional[np.ndarray] = None
    ) -> EEIAnalysisResult:
        """
        Perform EEI correlation analysis across multiple chi angles.

        This function calculates EEI for different chi angles and determines
        correlations with the target log.

        Args:
            dt_data: P-wave slowness data
            dts_data: S-wave slowness data
            rhob_data: Bulk density data
            target_data: Target log data
            chi_angles: Array of chi angles to test (default: 0 to 90 degrees)

        Returns:
            EEIAnalysisResult with correlation analysis results
        """
        warnings = []
        errors = []
        metadata = {
            'analysis_type': 'EEI_correlation',
            'chi_angle_count': 0,
            'best_correlation': np.nan,
            'best_chi_angle': np.nan
        }

        try:
            # Default chi angles if not provided
            if chi_angles is None:
                chi_angles = np.arange(0, 91, 1)  # 0 to 90 degrees in 1-degree steps

            metadata['chi_angle_count'] = len(chi_angles)

            # Validate inputs
            validation_result = EEIProcessor.validate_eei_inputs(
                dt_data, dts_data, rhob_data, target_data
            )

            if not validation_result['valid']:
                return EEIAnalysisResult(
                    success=False,
                    eei_data=None,
                    correlations=None,
                    chi_angles=chi_angles,
                    optimization_results=None,
                    metadata=metadata,
                    warnings=warnings + validation_result['warnings'],
                    errors=errors + validation_result['errors']
                )

            # Create common mask for all data
            common_mask = (np.isfinite(dt_data) & np.isfinite(dts_data) &
                          np.isfinite(rhob_data) & np.isfinite(target_data))

            if np.sum(common_mask) < 10:
                errors.append("Insufficient valid data points for EEI analysis")
                return EEIAnalysisResult(
                    success=False,
                    eei_data=None,
                    correlations=None,
                    chi_angles=chi_angles,
                    optimization_results=None,
                    metadata=metadata,
                    warnings=warnings,
                    errors=errors
                )

            # Extract clean data
            dt_clean = dt_data[common_mask]
            dts_clean = dts_data[common_mask]
            rhob_clean = rhob_data[common_mask]
            target_clean = target_data[common_mask]

            # Calculate EEI for each chi angle
            correlations = []
            eei_arrays = []

            for chi in chi_angles:
                try:
                    # Calculate EEI using the formula:
                    # EEI = IP * cos(chi) + IS * sin(chi)
                    # where IP = RHOB/DT and IS = RHOB/DTS

                    chi_rad = np.radians(chi)
                    ip = rhob_clean / dt_clean  # P-impedance
                    is_imp = rhob_clean / dts_clean  # S-impedance

                    eei = ip * np.cos(chi_rad) + is_imp * np.sin(chi_rad)
                    eei_arrays.append(eei)

                    # Calculate correlation with target
                    corr_result = StatisticalProcessor.calculate_correlation_safe(eei, target_clean)
                    correlations.append(corr_result.value)

                except Exception as e:
                    warnings.append(f"EEI calculation failed for chi={chi}: {str(e)}")
                    correlations.append(np.nan)
                    eei_arrays.append(np.full_like(dt_clean, np.nan))

            correlations = np.array(correlations)

            # Find best correlation
            finite_corr_mask = np.isfinite(correlations)
            if np.any(finite_corr_mask):
                finite_correlations = correlations[finite_corr_mask]
                finite_chi_angles = chi_angles[finite_corr_mask]

                # Find maximum absolute correlation
                abs_correlations = np.abs(finite_correlations)
                best_idx = np.argmax(abs_correlations)

                metadata['best_correlation'] = finite_correlations[best_idx]
                metadata['best_chi_angle'] = finite_chi_angles[best_idx]
                metadata['best_abs_correlation'] = abs_correlations[best_idx]
                metadata['valid_correlations'] = np.sum(finite_corr_mask)

                # Store best EEI data
                best_eei_idx = np.where(finite_corr_mask)[0][best_idx]
                best_eei_data = eei_arrays[best_eei_idx]
            else:
                errors.append("No valid correlations calculated")
                best_eei_data = None

            return EEIAnalysisResult(
                success=len(errors) == 0,
                eei_data=best_eei_data,
                correlations=correlations,
                chi_angles=chi_angles,
                optimization_results={
                    'best_correlation': metadata['best_correlation'],
                    'best_chi_angle': metadata['best_chi_angle'],
                    'correlation_curve': correlations
                },
                metadata=metadata,
                warnings=warnings + validation_result['warnings'],
                errors=errors
            )

        except Exception as e:
            logger.error(f"EEI correlation analysis failed: {str(e)}")
            return EEIAnalysisResult(
                success=False,
                eei_data=None,
                correlations=None,
                chi_angles=chi_angles,
                optimization_results=None,
                metadata=metadata,
                warnings=warnings,
                errors=errors + [f"Analysis failed: {str(e)}"]
            )


# Convenience functions for backward compatibility
def prepare_eei_data(wells_data, required_logs=None, depth_ranges=None):
    """Convenience function for EEIProcessor.prepare_eei_data()"""
    return EEIProcessor.prepare_eei_data(wells_data, required_logs, depth_ranges)


def validate_eei_inputs(dt_data, dts_data, rhob_data, target_data):
    """Convenience function for EEIProcessor.validate_eei_inputs()"""
    return EEIProcessor.validate_eei_inputs(dt_data, dts_data, rhob_data, target_data)
