#!/usr/bin/env python3
"""
Test script to verify the enhanced plot configuration workflow.
This tests the missing step that was identified in the workflow.
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_plot_configuration_workflow():
    """Test the complete workflow from column selection to plot configuration."""
    
    print("=== Testing Enhanced Plot Configuration Workflow ===")
    print("This test verifies that the missing plot configuration step has been restored.")
    print()
    
    try:
        # Test 1: Import the enhanced PlotSettingsDialog
        print("1. Testing PlotSettingsDialog import...")
        from dialogs.plot_settings_dialog import PlotSettingsDialog
        print("   ✓ PlotSettingsDialog imported successfully")
        
        # Test 2: Create a test dialog with Z-column
        print("2. Testing PlotSettingsDialog with Z-column...")
        root = tk.Tk()
        root.withdraw()
        
        # Test with Z-column
        dialog_z = PlotSettingsDialog(root, z_col="RHOB", class_col="LITHO")
        print("   ✓ PlotSettingsDialog created with Z-column and class column")
        
        # Test without Z-column
        dialog_basic = PlotSettingsDialog(root, z_col=None, class_col=None)
        print("   ✓ PlotSettingsDialog created without Z-column or class column")
        
        # Clean up test dialogs
        dialog_z.destroy()
        dialog_basic.destroy()
        root.destroy()
        
        # Test 3: Check that main.py workflow includes plot settings
        print("3. Testing main.py workflow integration...")
        with open('main.py', 'r') as f:
            main_content = f.read()
        
        if "PlotSettingsDialog" in main_content:
            print("   ✓ PlotSettingsDialog is integrated in main.py")
        else:
            print("   ✗ PlotSettingsDialog not found in main.py")
            return False
        
        if "Step 5: Plot settings dialog" in main_content:
            print("   ✓ Plot settings step is properly documented in workflow")
        else:
            print("   ✗ Plot settings step not documented in workflow")
            return False
        
        # Test 4: Verify workflow sequence
        print("4. Testing workflow sequence...")
        workflow_steps = [
            "Step 1: Load LAS files",
            "Step 2: Optional Excel depth ranges loading",
            "Step 3: Calculator workflow with retry logic",
            "Step 4: Column selection dialog",
            "Step 5: Plot settings dialog",
            "Step 6: Create the plot"
        ]
        
        missing_steps = []
        for step in workflow_steps:
            if step not in main_content:
                missing_steps.append(step)
        
        if missing_steps:
            print(f"   ✗ Missing workflow steps: {missing_steps}")
            return False
        else:
            print("   ✓ All workflow steps are present and properly sequenced")
        
        # Test 5: Check enhanced plot configuration options
        print("5. Testing enhanced plot configuration options...")
        
        # Check if the dialog has the expected configuration sections
        with open('dialogs/plot_settings_dialog.py', 'r') as f:
            dialog_content = f.read()
        
        expected_features = [
            "Marginal Plot Type",
            "Colormap Settings",
            "Class Visualization Settings", 
            "Legend Settings",
            "marginal_plot_type",
            "colormap_category",
            "reverse_colormap",
            "legend_style"
        ]
        
        missing_features = []
        for feature in expected_features:
            if feature not in dialog_content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"   ✗ Missing plot configuration features: {missing_features}")
            return False
        else:
            print("   ✓ All enhanced plot configuration features are present")
        
        print()
        print("=== Test Results ===")
        print("✅ PLOT CONFIGURATION WORKFLOW TEST PASSED!")
        print()
        print("The missing plot configuration step has been successfully restored:")
        print("• Enhanced PlotSettingsDialog with comprehensive options")
        print("• Proper workflow integration in main.py")
        print("• Support for Z-column (colormap) configuration")
        print("• Support for class column visualization settings")
        print("• Marginal plot type selection (Histogram, KDE, Both, None)")
        print("• Legend customization options")
        print("• Help system for user guidance")
        print()
        print("Users can now properly configure their plots after selecting")
        print("depth ranges, instead of jumping directly to plot generation.")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_workflow_continuity():
    """Test that the workflow provides proper continuity."""
    
    print("\n=== Testing Workflow Continuity ===")
    
    try:
        # Check that the workflow properly handles user cancellation
        with open('main.py', 'r') as f:
            main_content = f.read()
        
        # Check for proper error handling
        continuity_checks = [
            "if not col_dlg.result:",
            "if not settings_dlg.result:",
            "if self.quit_application():",
            "continue"
        ]
        
        missing_checks = []
        for check in continuity_checks:
            if check not in main_content:
                missing_checks.append(check)
        
        if missing_checks:
            print(f"✗ Missing continuity checks: {missing_checks}")
            return False
        
        print("✓ Workflow continuity checks are properly implemented")
        print("✓ Users can cancel at any step and return to previous steps")
        print("✓ Proper error handling for dialog cancellation")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing workflow continuity: {e}")
        return False

if __name__ == "__main__":
    print("Xplot Application - Plot Configuration Workflow Test")
    print("=" * 60)
    
    success1 = test_plot_configuration_workflow()
    success2 = test_workflow_continuity()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED!")
        print("The plot configuration workflow has been successfully restored.")
        print("\nNext steps:")
        print("1. Run the main application: python main.py")
        print("2. Load LAS files and proceed through the workflow")
        print("3. Verify that the plot configuration dialog appears after depth selection")
        print("4. Test the various plot configuration options")
        print("5. Confirm that plots are generated with the selected settings")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("The plot configuration workflow may not be working correctly.")
        print("Please review the implementation and fix any issues.")
