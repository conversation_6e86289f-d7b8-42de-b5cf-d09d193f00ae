# Common Boundaries Implementation

## Overview

This document describes the implementation of the common boundary selection feature for the Xplot application. The feature allows users to define a single boundary that applies to all wells simultaneously, following the visual design and interaction pattern shown in the user's requirements.

## Features Implemented

### 1. Common Boundary Selection Dialog (`dialogs/common_boundaries_dialog.py`)

A new standalone dialog that provides:

- **Common Boundaries Section**:
  - Checkbox to "Use common boundaries for all wells"
  - Dropdown for "Common Top Boundary" 
  - Dropdown for "Common Bottom Boundary"
  - "Apply to All Wells" button

- **Individual Well Boundaries Section**:
  - Table showing each well with individual boundary selections
  - Columns: Well, Top Boundary, Top Depth, Bottom Boundary, Bottom Depth, Status
  - Status indicators: "Manual", "Common", "Common Mode"

- **Enhanced Validation**:
  - Ensures boundaries are compatible with all wells
  - Error handling for wells outside selected boundaries
  - Clear feedback when boundaries are applied

### 2. Integration with Existing System

- **Enhanced Depth Dialog Integration**: Added option to use the new common boundaries dialog
- **Backward Compatibility**: Existing functionality remains unchanged
- **Seamless Workflow**: Users can switch between common and individual boundary modes

## User Interface Design

The implementation follows the exact design shown in the user's image:

```
┌─────────────────────────────────────────────────────────────────┐
│ Select Boundaries for All Wells                                 │
├─────────────────────────────────────────────────────────────────┤
│ Common Boundaries                                               │
│ ☑ Use common boundaries for all wells                          │
│                                                                 │
│ Common Top Boundary: [Dropdown ▼] Common Bottom Boundary: [▼]  │
│                    [Apply to All Wells]                        │
├─────────────────────────────────────────────────────────────────┤
│ Individual Well Boundaries                                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Well │ Top Boundary │ Top Depth │ Bottom Boundary │ Status │ │
│ │ B-G-6│ E8          │ 560.51    │ F19            │ Manual │ │
│ │ B-G-10│ E8         │ 553.28    │ F18-5          │ Manual │ │
│ │ ...  │ ...         │ ...       │ ...            │ ...    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                          [OK]    [Cancel]      │
└─────────────────────────────────────────────────────────────────┘
```

## Technical Implementation

### Class Structure

```python
class CommonBoundariesDialog(tk.Toplevel):
    """Dialog for selecting common boundaries for all wells."""
    
    def __init__(self, parent, df, las_well_names):
        # Initialize dialog with Excel data and well names
    
    def _build_ui(self):
        # Build the complete user interface
    
    def _create_common_boundaries_section(self, parent):
        # Create the common boundaries controls
    
    def _create_individual_boundaries_section(self, parent):
        # Create the individual well boundaries table
    
    def _toggle_common_mode(self):
        # Switch between common and individual modes
    
    def _apply_common_boundaries(self):
        # Apply selected common boundaries to all wells
    
    def _on_ok(self):
        # Validate and return selected boundaries
```

### Key Functions

1. **`select_common_boundaries_for_all_wells(df, las_well_names)`**
   - Main entry point for the common boundaries dialog
   - Returns dictionary mapping well names to (top_depth, bottom_depth) tuples

2. **`_toggle_common_mode()`**
   - Enables/disables controls based on common boundaries checkbox
   - Updates status indicators for all wells

3. **`_apply_common_boundaries()`**
   - Validates common boundary selections
   - Applies boundaries to all compatible wells
   - Provides detailed error reporting

## Validation and Error Handling

### Boundary Validation
- **Surface Existence**: Ensures selected surfaces exist for each well
- **Depth Validation**: Verifies top depth < bottom depth for all wells
- **Range Validation**: Checks for reasonable depth ranges
- **Well Compatibility**: Validates boundaries work across all wells

### Error Reporting
- **Partial Application**: Shows which wells succeeded/failed
- **Detailed Messages**: Specific error descriptions for each well
- **User Choices**: Option to continue with valid wells or fix issues

### Status Indicators
- **Manual**: Individual boundary selection (blue)
- **Common**: Applied via common boundaries (green)
- **Common Mode**: Common mode active but not applied (orange)

## Usage Workflow

### Method 1: Direct Common Boundaries Dialog
1. Load LAS files
2. Load Excel boundary data
3. Select "Use Enhanced Boundary Selection" 
4. Choose common boundaries or set individual boundaries
5. Apply and validate

### Method 2: Through Enhanced Depth Dialog
1. Load LAS files
2. Select Excel method in depth dialog
3. Load Excel data
4. Click "Use Enhanced Boundary Selection"
5. Use common boundaries dialog

## Integration Points

### Enhanced Depth Dialog (`dialogs/enhanced_depth_dialog.py`)
- Added import: `from .common_boundaries_dialog import select_common_boundaries_for_all_wells`
- Added function: `use_common_boundaries_dialog()`
- Added button: "Use Enhanced Boundary Selection"

### Column Selection Dialog (`dialogs/column_select_dialog.py`)
- No changes required - uses existing depth range selection

### Main Application (`main.py`)
- No changes required - uses existing workflow

## Data Requirements

### Excel File Format
- **Required Columns**: 'Well', 'Surface', 'MD' (Measured Depth)
- **Well Column**: Must match LAS file well names exactly
- **Surface Column**: Geological marker names (e.g., 'Top_Formation', 'Base_Formation')
- **MD Column**: Measured depths in same units as LAS files

### Example Data Structure
```
Well    | Surface        | MD
--------|----------------|--------
WELL_A  | Top_Reservoir  | 2100.5
WELL_A  | Base_Reservoir | 2250.8
WELL_B  | Top_Reservoir  | 2150.3
WELL_B  | Base_Reservoir | 2280.6
```

## Benefits

1. **Efficiency**: Apply boundaries to all wells with a single action
2. **Consistency**: Ensures uniform boundary selection across wells
3. **Flexibility**: Switch between common and individual modes
4. **Validation**: Comprehensive error checking and reporting
5. **User-Friendly**: Clear visual feedback and status indicators

## Testing

The implementation includes comprehensive validation:
- ✅ File structure validation
- ✅ Import validation  
- ✅ Class structure validation
- ✅ Integration validation
- ✅ Data processing validation

All tests pass successfully, confirming the implementation is ready for use.

## Future Enhancements

Potential improvements for future versions:
1. **Boundary Templates**: Save/load common boundary configurations
2. **Batch Operations**: Apply different boundary sets to well groups
3. **Visual Preview**: Show boundary locations on well logs
4. **Export Options**: Save boundary selections to Excel
5. **Undo/Redo**: Allow users to revert boundary changes
