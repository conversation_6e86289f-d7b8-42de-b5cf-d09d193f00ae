# -*- coding: utf-8 -*-
"""
EEI-Specific Configuration Module

This module contains configuration parameters specific to Extended Elastic
Impedance (EEI), CPEI, and PEIL analysis workflows.

Author: Migrated from EEI_XCorr_Modular
Created: 2025
Version: 1.0.0
"""

from typing import Dict, Any
import numpy as np

# ============================================================================
# ANALYSIS PARAMETER RANGES AND DEFAULTS
# ============================================================================

ANALYSIS_PARAMS: Dict[str, Dict[str, Any]] = {
    'CPEI': {
        'n_range': {
            'min': 0.1,
            'max': 2.0,
            'step': 0.1,
            'default': np.arange(0.1, 2.1, 0.1)
        },
        'phi_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'description': 'Correlation-based Poisson Elastic Impedance'
    },
    'PEIL': {
        'n_range': {
            'min': 0.1,
            'max': 2.0,
            'step': 0.1,
            'default': np.arange(0.1, 2.1, 0.1)
        },
        'phi_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'description': 'Porosity-Elastic Impedance Log'
    },
    'EEI': {
        'angle_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'k_range': {
            'min': 0.01,
            'max': 1.0,
            'default': 0.25
        },
        'calcmethod_options': [1, 2, 3],
        'calcmethod_default': 3,
        'description': 'Extended Elastic Impedance'
    }
}

# ============================================================================
# VALIDATION PARAMETERS
# ============================================================================

VALIDATION_PARAMS: Dict[str, Any] = {
    'min_data_points': 10,
    'min_finite_percentage': 50.0,
    'correlation_threshold': 0.01,
    'max_iterations': 10000,
    'convergence_tolerance': 1e-6,
    'outlier_std_threshold': 3.0,
    'min_correlation_points': 2
}

# ============================================================================
# DEFAULT CALCULATION PARAMETERS
# ============================================================================

DEFAULT_PARAMS: Dict[str, Any] = {
    'velocity_conversion_factor': 304800,  # microseconds/ft to m/s
    'percentile_clipping': [2, 98],
    'buffer_factor': 0.02,
    'min_range_factor': 0.01,
    'default_k_value': 0.25,
    'correlation_min_points': 2,
    'interpolation_method': 'linear',
    'smoothing_window': 5
}

# ============================================================================
# EEI CONFIGURATION CLASS
# ============================================================================

class EEIConfig:
    """
    Configuration manager for EEI-specific analysis.
    
    Provides centralized access to EEI analysis parameters
    with validation and type checking.
    """
    
    @staticmethod
    def get_analysis_params(analysis_type: str) -> Dict[str, Any]:
        """
        Get analysis parameters for specific analysis type.
        
        Args:
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            
        Returns:
            Dict containing analysis parameters
            
        Raises:
            ValueError: If analysis_type is not supported
        """
        analysis_type_upper = analysis_type.upper()
        if analysis_type_upper not in ANALYSIS_PARAMS:
            raise ValueError(
                f"Unsupported analysis type: {analysis_type}. "
                f"Supported types: {', '.join(ANALYSIS_PARAMS.keys())}"
            )
        
        return ANALYSIS_PARAMS[analysis_type_upper].copy()
    
    @staticmethod
    def get_validation_params() -> Dict[str, Any]:
        """
        Get validation parameters for EEI analysis.
        
        Returns:
            Dict containing validation parameters
        """
        return VALIDATION_PARAMS.copy()
    
    @staticmethod
    def get_default_params() -> Dict[str, Any]:
        """
        Get default calculation parameters.
        
        Returns:
            Dict containing default parameters
        """
        return DEFAULT_PARAMS.copy()
    
    @staticmethod
    def get_supported_analysis_types() -> list:
        """
        Get list of supported analysis types.
        
        Returns:
            List of supported analysis type names
        """
        return list(ANALYSIS_PARAMS.keys())
    
    @staticmethod
    def validate_analysis_type(analysis_type: str) -> bool:
        """
        Check if analysis type is supported.
        
        Args:
            analysis_type: Type of analysis to validate
            
        Returns:
            True if supported, False otherwise
        """
        return analysis_type.upper() in ANALYSIS_PARAMS
    
    @staticmethod
    def get_angle_range(analysis_type: str = 'EEI') -> range:
        """
        Get angle range for specified analysis type.
        
        Args:
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            
        Returns:
            Range object for angles
        """
        params = EEIConfig.get_analysis_params(analysis_type)
        
        if 'angle_range' in params:
            return params['angle_range']['default']
        elif 'phi_range' in params:
            return params['phi_range']['default']
        else:
            return range(-90, 91)
    
    @staticmethod
    def get_n_range(analysis_type: str) -> np.ndarray:
        """
        Get n parameter range for CPEI/PEIL analysis.
        
        Args:
            analysis_type: Type of analysis ('CPEI' or 'PEIL')
            
        Returns:
            NumPy array of n values
            
        Raises:
            ValueError: If analysis type doesn't support n parameter
        """
        params = EEIConfig.get_analysis_params(analysis_type)
        
        if 'n_range' not in params:
            raise ValueError(f"Analysis type {analysis_type} does not have n_range parameter")
        
        return params['n_range']['default']
