# -*- coding: utf-8 -*-
"""
EEI Analysis Configuration Module

This module contains static configuration data for EEI analysis.
All configuration is centralized here for easy maintenance and modification.

Author: Refactored from a7_load_multilas_EEI_XCOR_PLOT_Final.py
Created: 2024
Version: 1.0.0
"""

from typing import Dict, List, Tuple, Any
import numpy as np

# Log keyword mappings for automatic log detection
LOG_KEYWORDS: Dict[str, List[str]] = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC'],
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC'],
    'PHIT': ['PHIT', 'PHID', 'PHI_D'],
    'PHIE': ['PHIE', 'PHIE_D'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO'],
    'SWT': ['SWT', 'SW', 'WATER_SAT'],
    'SWE': ['SWE', 'SWE_D'],
    'DEPTH': ['DEPTH', 'MD', 'MEASURED_DEPTH'],
    'P-WAVE': ['P-WAVE', 'P_VELOCITY', 'VP'],
    'S-WAVE': ['S-WAVE', 'S_VELOCITY', 'VS'],
    'FLUID_CODE': ['FLUID_CODE', 'FLUID'],
    'FLUID_PETREL': ['FLUID_PETREL'],
    'LITHO_CODE': ['LITHO_CODE', 'LITHOLOGY', 'LITHO_PETREL'],
    'GR': ['GR', 'GAMMA_RAY', 'GR_LOG'],
    'NPHI': ['NPHI', 'NEUTRON', 'NEUTRON_POROSITY'],
    'KSOLID': ['KSOLID', 'K_SOLID'],
    'GSOLID': ['GSOLID', 'G_SOLID'],
    'KSAT': ['KSAT', 'K_SATURATION'],
    'GSAT': ['GSAT', 'G_SATURATION'],
    'KDRY': ['KDRY', 'K_DRY'],
    'GDRY': ['GDRY', 'G_DRY'],
    'VCL': ['VCL', 'VOL_WETCLAY', 'V_CLAY'],
    'RT': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD', 'AT90']
}

# Analysis parameter ranges and defaults
ANALYSIS_PARAMS: Dict[str, Dict[str, Any]] = {
    'CPEI': {
        'n_range': {
            'min': 0.1,
            'max': 2.0,
            'step': 0.1,
            'default': np.arange(0.1, 2.1, 0.1)
        },
        'phi_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'description': 'Compressional Poisson Elastic Impedance'
    },
    'PEIL': {
        'n_range': {
            'min': 0.1,
            'max': 2.0,
            'step': 0.1,
            'default': np.arange(0.1, 2.1, 0.1)
        },
        'phi_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'description': 'Poisson Elastic Impedance Log'
    },
    'EEI': {
        'angle_range': {
            'min': -90,
            'max': 90,
            'step': 1,
            'default': range(-90, 91)
        },
        'k_range': {
            'min': 0.01,
            'max': 1.0,
            'default': 0.25
        },
        'calcmethod_options': [1, 2, 3],
        'calcmethod_default': 3,
        'description': 'Extended Elastic Impedance'
    }
}

# Validation parameters
VALIDATION_PARAMS: Dict[str, Any] = {
    'min_data_points': 10,
    'min_finite_percentage': 50.0,
    'correlation_threshold': 0.01,
    'max_iterations': 10000,
    'convergence_tolerance': 1e-6
}

# UI configuration
UI_CONFIG: Dict[str, Any] = {
    'window_sizes': {
        'calculator': (1000, 800),
        'target_selection': (350, 450),
        'depth_ranges': (600, 500),
        'boundary_selection': (800, 600)
    },
    'colors': {
        'safe_logs': '#00CC00',
        'partial_logs': '#FF6600',
        'calculated_logs': '#0066CC',
        'error': '#FF0000',
        'warning': '#FFA500',
        'info': '#0000FF'
    },
    'fonts': {
        'default': ('Arial', 10),
        'bold': ('Arial', 10, 'bold'),
        'small': ('Arial', 8),
        'code': ('Courier', 10)
    }
}

# File format specifications
FILE_FORMATS: Dict[str, Dict[str, Any]] = {
    'las': {
        'extensions': ['.las'],
        'description': 'Log ASCII Standard files',
        'required_curves': ['DEPTH', 'DT', 'DTS', 'RHOB']
    },
    'excel': {
        'extensions': ['.xls', '.xlsx'],
        'description': 'Excel boundary files',
        'required_columns': ['Well', 'Surface', 'MD']
    }
}

# Error messages and user guidance
ERROR_MESSAGES: Dict[str, str] = {
    'missing_essential_logs': "Essential logs (Vp, Vs, Density) are missing from one or more wells.",
    'invalid_depth_range': "Invalid depth range specified. Top depth must be less than bottom depth.",
    'insufficient_data': "Insufficient valid data points for analysis. Minimum {min_points} required.",
    'calculation_failed': "Calculation failed due to invalid input data or parameters.",
    'correlation_failed': "Correlation calculation failed. Check data quality and compatibility.",
    'optimization_failed': "Parameter optimization failed to converge. Try different parameter ranges."
}

# Success messages
SUCCESS_MESSAGES: Dict[str, str] = {
    'calculation_complete': "Calculation completed successfully.",
    'optimization_complete': "Parameter optimization completed successfully.",
    'data_loaded': "Data loaded successfully from {count} wells.",
    'validation_passed': "All validation checks passed.",
    'export_complete': "Results exported successfully to {filename}."
}

# Default calculation parameters
DEFAULT_PARAMS: Dict[str, Any] = {
    'velocity_conversion_factor': 304800,  # microseconds/ft to m/s
    'percentile_clipping': [2, 98],
    'buffer_factor': 0.02,
    'min_range_factor': 0.01,
    'default_k_value': 0.25,
    'correlation_min_points': 2
}

# Logging configuration
LOGGING_CONFIG: Dict[str, Any] = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO'
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'eei_analysis.log',
            'level': 'DEBUG'
        }
    }
}

class EEIConfig:
    """
    Configuration manager for EEI analysis.
    
    Provides centralized access to all configuration parameters
    with validation and type checking.
    """
    
    @staticmethod
    def get_log_keywords() -> Dict[str, List[str]]:
        """Get log keyword mappings."""
        return LOG_KEYWORDS.copy()
    
    @staticmethod
    def get_analysis_params(analysis_type: str) -> Dict[str, Any]:
        """
        Get analysis parameters for specific analysis type.
        
        Args:
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            
        Returns:
            Dict containing analysis parameters
            
        Raises:
            ValueError: If analysis_type is not supported
        """
        if analysis_type.upper() not in ANALYSIS_PARAMS:
            raise ValueError(f"Unsupported analysis type: {analysis_type}")
        
        return ANALYSIS_PARAMS[analysis_type.upper()].copy()
    
    @staticmethod
    def get_validation_params() -> Dict[str, Any]:
        """Get validation parameters."""
        return VALIDATION_PARAMS.copy()
    
    @staticmethod
    def get_ui_config() -> Dict[str, Any]:
        """Get UI configuration."""
        return UI_CONFIG.copy()
    
    @staticmethod
    def get_default_params() -> Dict[str, Any]:
        """Get default calculation parameters."""
        return DEFAULT_PARAMS.copy()
    
    @staticmethod
    def get_error_message(error_key: str, **kwargs) -> str:
        """
        Get formatted error message.
        
        Args:
            error_key: Key for error message
            **kwargs: Format parameters for message
            
        Returns:
            Formatted error message
        """
        if error_key not in ERROR_MESSAGES:
            return f"Unknown error: {error_key}"
        
        try:
            return ERROR_MESSAGES[error_key].format(**kwargs)
        except KeyError:
            return ERROR_MESSAGES[error_key]
    
    @staticmethod
    def get_success_message(success_key: str, **kwargs) -> str:
        """
        Get formatted success message.
        
        Args:
            success_key: Key for success message
            **kwargs: Format parameters for message
            
        Returns:
            Formatted success message
        """
        if success_key not in SUCCESS_MESSAGES:
            return f"Operation completed: {success_key}"
        
        try:
            return SUCCESS_MESSAGES[success_key].format(**kwargs)
        except KeyError:
            return SUCCESS_MESSAGES[success_key]
