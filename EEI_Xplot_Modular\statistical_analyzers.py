"""Statistical analysis and visualization classes."""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, <PERSON>ple

import numpy as np
from scipy import stats


class StatisticalAnalyzer(ABC):
    """Base class for histogram and KDE analyzers."""

    def __init__(
        self,
        data: np.ndarray,
        class_data: Optional[np.ndarray] = None,
        colors: Optional[Dict] = None,
        class_names: Optional[Dict] = None,
    ) -> None:
        """Initialize the analyzer with optional class data and colors."""
        self.data = np.array(data)
        self.class_data = np.array(class_data) if class_data is not None else None
        self.colors = colors or {}
        self.class_names = class_names or {}
        self._validate_data()

    def _validate_data(self) -> None:
        if len(self.data) == 0:
            raise ValueError("Data array cannot be empty")
        if self.class_data is not None and len(self.class_data) != len(self.data):
            raise ValueError("Class data must have the same length as data array")

    def get_clean_data(self) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Return data and class arrays with NaNs removed."""
        valid_mask = ~np.isnan(self.data)
        clean_data = self.data[valid_mask]
        clean_class_data = self.class_data[valid_mask] if self.class_data is not None else None
        return clean_data, clean_class_data

    def get_unique_classes(self) -> List:
        """Return unique class values, excluding NaN."""
        if self.class_data is None:
            return []
        _, clean_class_data = self.get_clean_data()
        if clean_class_data is None or len(clean_class_data) == 0:
            return []
        return list(np.unique(clean_class_data[~np.isnan(clean_class_data)]))

    @abstractmethod
    def plot(self, ax, **kwargs) -> None:
        """Plot the visualization on the provided axes."""


class HistogramAnalyzer(StatisticalAnalyzer):
    """Create histograms for given data."""

    def __init__(
        self,
        data: np.ndarray,
        class_data: Optional[np.ndarray] = None,
        colors: Optional[Dict] = None,
        class_names: Optional[Dict] = None,
        bins: int = 30,
        density: bool = True,
        alpha: float = 0.7,
    ) -> None:
        super().__init__(data, class_data, colors, class_names)
        self.bins = bins
        self.density = density
        self.alpha = alpha

    def _calculate_optimal_bins(self, data: np.ndarray) -> int:
        if len(data) == 0:
            return 1
        unique_values = len(np.unique(data))
        if unique_values <= 1:
            return 1
        return min(self.bins, max(5, unique_values, int(np.ceil(np.log2(len(data)) + 1))))

    def plot(self, ax, orientation: str = "vertical", **kwargs) -> None:
        clean_data, clean_class_data = self.get_clean_data()
        if len(clean_data) == 0:
            print("Warning: No valid data for histogram")
            return

        if clean_class_data is None or len(self.get_unique_classes()) == 0:
            self._plot_single_histogram(ax, clean_data, orientation, **kwargs)
        else:
            self._plot_class_histograms(ax, clean_data, clean_class_data, orientation, **kwargs)

    def _plot_single_histogram(self, ax, data: np.ndarray, orientation: str, **kwargs) -> None:
        bins = self._calculate_optimal_bins(data)
        try:
            if orientation == "horizontal":
                ax.hist(
                    data,
                    bins=bins,
                    orientation="horizontal",
                    color=kwargs.get("color", "gray"),
                    alpha=self.alpha,
                    density=self.density,
                )
            else:
                ax.hist(
                    data,
                    bins=bins,
                    color=kwargs.get("color", "gray"),
                    alpha=self.alpha,
                    density=self.density,
                )
        except Exception as exc:  # noqa: BLE001
            print(f"Warning: Could not create histogram: {exc}")
            if len(np.unique(data)) == 1:
                if orientation == "horizontal":
                    ax.axhline(y=data[0], color=kwargs.get("color", "gray"), alpha=self.alpha)
                else:
                    ax.axvline(x=data[0], color=kwargs.get("color", "gray"), alpha=self.alpha)

    def _plot_class_histograms(
        self, ax, data: np.ndarray, class_data: np.ndarray, orientation: str, **kwargs
    ) -> None:
        unique_classes = self.get_unique_classes()
        bins = self._calculate_optimal_bins(data)
        if len(np.unique(data)) > 1:
            bin_edges = np.linspace(np.min(data), np.max(data), bins + 1)
        else:
            single_val = data[0]
            bin_edges = np.array([single_val - 0.5, single_val + 0.5])

        for class_val in unique_classes:
            class_mask = class_data == class_val
            class_data_subset = data[class_mask]
            if len(class_data_subset) == 0:
                continue

            color = self.colors.get(class_val, f"C{len(unique_classes) % 10}")
            label = self.class_names.get(class_val, f"Class {class_val}")

            try:
                if orientation == "horizontal":
                    ax.hist(
                        class_data_subset,
                        bins=bin_edges,
                        orientation="horizontal",
                        color=color,
                        alpha=self.alpha,
                        density=self.density,
                        label=label,
                        histtype="step",
                        linewidth=2,
                    )
                else:
                    ax.hist(
                        class_data_subset,
                        bins=bin_edges,
                        color=color,
                        alpha=self.alpha,
                        density=self.density,
                        label=label,
                        histtype="step",
                        linewidth=2,
                    )
            except Exception as exc:  # noqa: BLE001
                print(f"Warning: Could not create histogram for class {class_val}: {exc}")


class KDEAnalyzer(StatisticalAnalyzer):
    """Create Kernel Density Estimates for given data."""

    def __init__(
        self,
        data: np.ndarray,
        class_data: Optional[np.ndarray] = None,
        colors: Optional[Dict] = None,
        class_names: Optional[Dict] = None,
        bandwidth: Optional[float] = None,
        n_points: int = 100,
    ) -> None:
        super().__init__(data, class_data, colors, class_names)
        self.bandwidth = bandwidth
        self.n_points = n_points

    def plot(
        self,
        ax,
        orientation: str = "vertical",
        data_range: Optional[Tuple[float, float]] = None,
        **kwargs,
    ) -> None:
        clean_data, clean_class_data = self.get_clean_data()
        if len(clean_data) == 0:
            print("Warning: No valid data for KDE")
            return

        if clean_class_data is None or len(self.get_unique_classes()) == 0:
            self._plot_single_kde(ax, clean_data, orientation, data_range, **kwargs)
        else:
            self._plot_class_kdes(ax, clean_data, clean_class_data, orientation, data_range, **kwargs)

    def _plot_single_kde(
        self,
        ax,
        data: np.ndarray,
        orientation: str,
        data_range: Optional[Tuple[float, float]],
        **kwargs,
    ) -> None:
        if len(data) < 2:
            print("Warning: KDE requires at least 2 data points")
            if len(data) == 1:
                if orientation == "horizontal":
                    ax.axhline(y=data[0], color=kwargs.get("color", "blue"), linestyle="--")
                else:
                    ax.axvline(x=data[0], color=kwargs.get("color", "blue"), linestyle="--")
            return
        if np.var(data) <= 1e-10:
            print("Warning: Data has zero variance, using line instead of KDE")
            if orientation == "horizontal":
                ax.axhline(y=data[0], color=kwargs.get("color", "blue"), linestyle="--")
            else:
                ax.axvline(x=data[0], color=kwargs.get("color", "blue"), linestyle="--")
            return
        try:
            kde = stats.gaussian_kde(data)
            if self.bandwidth is not None:
                kde.set_bandwidth(self.bandwidth)
            if data_range is not None:
                eval_range = np.linspace(data_range[0], data_range[1], self.n_points)
            else:
                data_min, data_max = np.min(data), np.max(data)
                range_padding = (data_max - data_min) * 0.1
                eval_range = np.linspace(
                    data_min - range_padding,
                    data_max + range_padding,
                    self.n_points,
                )
            kde_values = kde(eval_range)
            if orientation == "horizontal":
                ax.plot(kde_values, eval_range, color=kwargs.get("color", "blue"), linestyle="--", linewidth=2)
            else:
                ax.plot(eval_range, kde_values, color=kwargs.get("color", "blue"), linestyle="--", linewidth=2)
        except Exception as exc:  # noqa: BLE001
            print(f"Warning: Could not create KDE: {exc}")

    def _plot_class_kdes(
        self,
        ax,
        data: np.ndarray,
        class_data: np.ndarray,
        orientation: str,
        data_range: Optional[Tuple[float, float]],
        **kwargs,
    ) -> None:
        unique_classes = self.get_unique_classes()
        for class_val in unique_classes:
            class_mask = class_data == class_val
            class_data_subset = data[class_mask]
            if len(class_data_subset) < 2:
                print(f"Warning: Class {class_val} has insufficient data for KDE")
                continue
            if np.var(class_data_subset) <= 1e-10:
                print(f"Warning: Class {class_val} has zero variance")
                continue
            color = self.colors.get(class_val, f"C{len(unique_classes) % 10}")
            label = self.class_names.get(class_val, f"Class {class_val}")
            try:
                kde = stats.gaussian_kde(class_data_subset)
                if self.bandwidth is not None:
                    kde.set_bandwidth(self.bandwidth)
                if data_range is not None:
                    eval_range = np.linspace(data_range[0], data_range[1], self.n_points)
                else:
                    data_min, data_max = np.min(data), np.max(data)
                    range_padding = (data_max - data_min) * 0.1
                    eval_range = np.linspace(
                        data_min - range_padding,
                        data_max + range_padding,
                        self.n_points,
                    )
                kde_values = kde(eval_range)
                if orientation == "horizontal":
                    ax.plot(
                        kde_values,
                        eval_range,
                        color=color,
                        linestyle="--",
                        linewidth=2,
                        label=label,
                    )
                else:
                    ax.plot(
                        eval_range,
                        kde_values,
                        color=color,
                        linestyle="--",
                        linewidth=2,
                        label=label,
                    )
            except Exception as exc:  # noqa: BLE001
                print(f"Warning: Could not create KDE for class {class_val}: {exc}")


class StatisticalVisualizer:
    """Manage histogram and KDE analyzers for X and Y data."""

    def __init__(
        self,
        x_data: np.ndarray,
        y_data: np.ndarray,
        class_data: Optional[np.ndarray] = None,
        colors: Optional[Dict] = None,
        class_names: Optional[Dict] = None,
    ) -> None:
        self.x_data = np.array(x_data)
        self.y_data = np.array(y_data)
        self.class_data = np.array(class_data) if class_data is not None else None
        self.colors = colors or {}
        self.class_names = class_names or {}
        self.x_histogram = HistogramAnalyzer(self.x_data, self.class_data, colors, class_names)
        self.y_histogram = HistogramAnalyzer(self.y_data, self.class_data, colors, class_names)
        self.x_kde = KDEAnalyzer(self.x_data, self.class_data, colors, class_names)
        self.y_kde = KDEAnalyzer(self.y_data, self.class_data, colors, class_names)

    def plot_marginal_distributions(
        self,
        ax_histx,
        ax_histy,
        plot_type: str = "Both",
        x_range: Optional[Tuple[float, float]] = None,
        y_range: Optional[Tuple[float, float]] = None,
        **kwargs,
    ) -> None:
        if plot_type in ["Histogram", "Both"]:
            self.x_histogram.plot(ax_histx, orientation="vertical", **kwargs)
            self.y_histogram.plot(ax_histy, orientation="horizontal", **kwargs)
        if plot_type in ["KDE", "Both"]:
            self.x_kde.plot(ax_histx, orientation="vertical", data_range=x_range, **kwargs)
            self.y_kde.plot(ax_histy, orientation="horizontal", data_range=y_range, **kwargs)

